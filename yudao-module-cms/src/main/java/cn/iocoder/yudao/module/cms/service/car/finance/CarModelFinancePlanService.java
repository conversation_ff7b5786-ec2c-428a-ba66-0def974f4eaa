package cn.iocoder.yudao.module.cms.service.car.finance;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.CarModelFinancePlanCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.CarModelFinancePlanPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.CarModelFinancePlanUpdateReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.finance.CarModelFinancePlanDO;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 车型融资方案 Service 接口
 *
 * <AUTHOR>
 */
public interface CarModelFinancePlanService {

    /**
     * 创建车型融资方案
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCarModelFinancePlan(@Valid CarModelFinancePlanCreateReqVO createReqVO);

    /**
     * 更新车型融资方案
     *
     * @param updateReqVO 更新信息
     */
    void updateCarModelFinancePlan(@Valid CarModelFinancePlanUpdateReqVO updateReqVO);

    /**
     * 删除车型融资方案
     *
     * @param id 编号
     */
    void deleteCarModelFinancePlan(Long id);

    /**
     * 获得车型融资方案
     *
     * @param id 编号
     * @return 车型融资方案
     */
    CarModelFinancePlanDO getCarModelFinancePlan(Long id);

    /**
     * 获得车型融资方案分页
     *
     * @param pageReqVO 分页查询
     * @return 车型融资方案分页
     */
    PageResult<CarModelFinancePlanDO> getCarModelFinancePlanPage(CarModelFinancePlanPageReqVO pageReqVO);

    /**
     * 根据车型ID获得融资方案列表
     *
     * @param modelId 车型ID
     * @return 融资方案列表
     */
    List<CarModelFinancePlanDO> getCarModelFinancePlanListByModelId(Long modelId);

    /**
     * 获得车型的默认融资方案
     *
     * @param modelId 车型ID
     * @return 默认融资方案
     */
    CarModelFinancePlanDO getDefaultFinancePlan(Long modelId);

    /**
     * 获得车型的融资方案（包含关联信息）
     *
     * @param modelId 车型ID
     * @return 融资方案列表
     */
    List<CarModelFinancePlanDO> getCarModelFinancePlansWithDetails(Long modelId);

    /**
     * 设置默认融资方案
     *
     * @param id 方案ID
     */
    void setAsDefaultPlan(Long id);

    /**
     * 批量创建融资方案
     *
     * @param createReqVOList 创建信息列表
     * @return 创建成功的ID列表
     */
    List<Long> batchCreateCarModelFinancePlans(@Valid List<CarModelFinancePlanCreateReqVO> createReqVOList);

    /**
     * 根据车型ID删除所有融资方案
     *
     * @param modelId 车型ID
     */
    void deleteCarModelFinancePlansByModelId(Long modelId);

    /**
     * 计算融资方案详情
     *
     * @param carPrice 车辆价格
     * @param downPaymentRatio 首付比例
     * @param interestRate 利率
     * @param termMonths 期限月数
     * @param gfv 保证未来价值
     * @return 融资方案详情
     */
    FinancePlanDetails calculateFinancePlan(BigDecimal carPrice, BigDecimal downPaymentRatio, 
                                          BigDecimal interestRate, Integer termMonths, BigDecimal gfv);

    /**
     * 融资方案详情类
     */
    class FinancePlanDetails {
        private BigDecimal downPaymentAmount; // 首付金额
        private BigDecimal financeAmount; // 融资金额
        private BigDecimal monthlyPayment; // 月供
        private BigDecimal weeklyPayment; // 周供
        private BigDecimal totalAmount; // 总金额

        public FinancePlanDetails() {}

        public FinancePlanDetails(BigDecimal downPaymentAmount, BigDecimal financeAmount, 
                                BigDecimal monthlyPayment, BigDecimal weeklyPayment, BigDecimal totalAmount) {
            this.downPaymentAmount = downPaymentAmount;
            this.financeAmount = financeAmount;
            this.monthlyPayment = monthlyPayment;
            this.weeklyPayment = weeklyPayment;
            this.totalAmount = totalAmount;
        }

        // Getters and setters
        public BigDecimal getDownPaymentAmount() { return downPaymentAmount; }
        public void setDownPaymentAmount(BigDecimal downPaymentAmount) { this.downPaymentAmount = downPaymentAmount; }

        public BigDecimal getFinanceAmount() { return financeAmount; }
        public void setFinanceAmount(BigDecimal financeAmount) { this.financeAmount = financeAmount; }

        public BigDecimal getMonthlyPayment() { return monthlyPayment; }
        public void setMonthlyPayment(BigDecimal monthlyPayment) { this.monthlyPayment = monthlyPayment; }

        public BigDecimal getWeeklyPayment() { return weeklyPayment; }
        public void setWeeklyPayment(BigDecimal weeklyPayment) { this.weeklyPayment = weeklyPayment; }

        public BigDecimal getTotalAmount() { return totalAmount; }
        public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }
    }

}