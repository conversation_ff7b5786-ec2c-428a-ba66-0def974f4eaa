package cn.iocoder.yudao.module.cms.dal.mysql.diy;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.cms.controller.admin.diy.vo.page.CmsDiyPagePageReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.stream.Collectors;

/**
 * CMS DIY页面 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CmsDiyPageMapper extends BaseMapperX<CmsDiyPageDO> {

    default PageResult<CmsDiyPageDO> selectPage(CmsDiyPagePageReqVO pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<CmsDiyPageDO>()
                .likeIfPresent(CmsDiyPageDO::getName, pageReqVO.getName())
                .eqIfPresent(CmsDiyPageDO::getUuid, pageReqVO.getUuid())
                .eqIfPresent(CmsDiyPageDO::getMenuId, pageReqVO.getMenuId())
                .eqIfPresent(CmsDiyPageDO::getParentId, pageReqVO.getParentId())
                .likeIfPresent(CmsDiyPageDO::getPath, pageReqVO.getPath())
                .eqIfPresent(CmsDiyPageDO::getStatus, pageReqVO.getStatus())
                .betweenIfPresent(CmsDiyPageDO::getCreateTime, pageReqVO.getCreateTime())
                .orderByDesc(CmsDiyPageDO::getId));
    }

    default CmsDiyPageDO selectByUuid(String uuid) {
        return selectOne(CmsDiyPageDO::getUuid, uuid);
    }

    default CmsDiyPageDO selectByPath(String path) {
        return selectOne(CmsDiyPageDO::getPath, path);
    }

    default CmsDiyPageDO selectByUuidAndIdNot(String uuid, Long excludeId) {
        return selectOne(new LambdaQueryWrapperX<CmsDiyPageDO>()
                .eq(CmsDiyPageDO::getUuid, uuid)
                .ne(excludeId != null, CmsDiyPageDO::getId, excludeId));
    }

    default CmsDiyPageDO selectByPathAndIdNot(String path, Long excludeId) {
        return selectOne(new LambdaQueryWrapperX<CmsDiyPageDO>()
                .eq(CmsDiyPageDO::getPath, path)
                .ne(excludeId != null, CmsDiyPageDO::getId, excludeId));
    }

    default List<CmsDiyPageDO> selectListByMenuId(Long menuId) {
        return selectList(CmsDiyPageDO::getMenuId, menuId);
    }

    default List<CmsDiyPageDO> selectListByParentId(Long parentId) {
        return selectList(CmsDiyPageDO::getParentId, parentId);
    }

    default List<Long> selectChildrenIds(Long parentId) {
        List<CmsDiyPageDO> children = selectList(new LambdaQueryWrapperX<CmsDiyPageDO>()
                .select(CmsDiyPageDO::getId)
                .eq(CmsDiyPageDO::getParentId, parentId));
        return children.stream().map(CmsDiyPageDO::getId).toList();
    }

    /**
     * 乐观锁更新页面（带版本号）
     *
     * @param updateObj 更新对象
     * @param version   当前版本号
     * @return 影响行数
     */
    int updateByIdAndVersion(@Param("updateObj") CmsDiyPageDO updateObj, @Param("version") Integer version);

    /**
     * 乐观锁更新页面内容
     *
     * @param id      页面ID
     * @param content 页面内容
     * @param version 当前版本号
     * @return 影响行数
     */
    default int updateContentByIdAndVersion(Long id, String content, Integer version) {
        CmsDiyPageDO updateObj = new CmsDiyPageDO();
        updateObj.setId(id);
        updateObj.setContent(content);
        updateObj.setVersion(version + 1); // 版本号+1
        return updateByIdAndVersion(updateObj, version);
    }

    /**
     * 乐观锁删除页面（带版本号）
     *
     * @param id      页面ID
     * @param version 当前版本号
     * @return 影响行数
     */
    int deleteByIdAndVersion(@Param("id") Long id, @Param("version") Integer version);

    /**
     * 查询热门页面
     *
     * @param limit 限制数量
     * @return 热门页面列表
     */
    List<CmsDiyPageDO> selectHotPages(@Param("limit") Integer limit);

    /**
     * 根据菜单ID查询已发布页面
     *
     * @param menuId 菜单ID
     * @return 已发布页面列表
     */
    default List<CmsDiyPageDO> selectPublishedPagesByMenuId(Long menuId) {
        return selectList(new LambdaQueryWrapperX<CmsDiyPageDO>()
                .eq(CmsDiyPageDO::getMenuId, menuId)
                .eq(CmsDiyPageDO::getStatus, 1) // 已发布状态
                .orderByDesc(CmsDiyPageDO::getId));
    }

    /**
     * 搜索已发布页面
     *
     * @param keyword 关键词
     * @return 已发布页面列表
     */
    List<CmsDiyPageDO> searchPublishedPages(@Param("keyword") String keyword);

    /**
     * 批量更新页面路径（当菜单路径变更时使用）
     * 
     * @param pageUpdates 页面路径更新列表
     */
    void batchUpdatePagePaths(@Param("pageUpdates") List<cn.iocoder.yudao.module.cms.service.menu.dto.PagePathUpdateDTO> pageUpdates);
}