package cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 车型融资方案更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CarModelFinancePlanUpdateReqVO extends CarModelFinancePlanBaseVO {

    @Schema(description = "方案ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "方案ID不能为空")
    private Long id;

    @Schema(description = "融资选项列表", example = "[]")
    private List<FinanceOptionReqVO> financeOptions;

}