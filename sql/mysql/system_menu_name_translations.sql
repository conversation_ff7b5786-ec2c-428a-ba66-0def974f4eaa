-- Update system_menu table: translate Chinese names to English

-- Main menu items
UPDATE `system_menu` SET `name` = 'System Management' WHERE `id` = 1 AND `name` = '系统管理';
UPDATE `system_menu` SET `name` = 'Infrastructure' WHERE `id` = 2 AND `name` = '基础设施';
UPDATE `system_menu` SET `name` = 'OA Example' WHERE `id` = 5 AND `name` = 'OA 示例';

-- System Management submenu
UPDATE `system_menu` SET `name` = 'User Management' WHERE `id` = 100 AND `name` = '用户管理';
UPDATE `system_menu` SET `name` = 'Role Management' WHERE `id` = 101 AND `name` = '角色管理';
UPDATE `system_menu` SET `name` = 'Menu Management' WHERE `id` = 102 AND `name` = '菜单管理';
UPDATE `system_menu` SET `name` = 'Department Management' WHERE `id` = 103 AND `name` = '部门管理';
UPDATE `system_menu` SET `name` = 'Position Management' WHERE `id` = 104 AND `name` = '岗位管理';
UPDATE `system_menu` SET `name` = 'Dictionary Management' WHERE `id` = 105 AND `name` = '字典管理';
UPDATE `system_menu` SET `name` = 'Audit Log' WHERE `id` = 108 AND `name` = '审计日志';

-- Infrastructure submenu
UPDATE `system_menu` SET `name` = 'Configuration Management' WHERE `id` = 106 AND `name` = '配置管理';
UPDATE `system_menu` SET `name` = 'Notice Announcement' WHERE `id` = 107 AND `name` = '通知公告';
UPDATE `system_menu` SET `name` = 'Token Management' WHERE `id` = 109 AND `name` = '令牌管理';
UPDATE `system_menu` SET `name` = 'Scheduled Task' WHERE `id` = 110 AND `name` = '定时任务';
UPDATE `system_menu` SET `name` = 'MySQL Monitor' WHERE `id` = 111 AND `name` = 'MySQL 监控';
UPDATE `system_menu` SET `name` = 'Java Monitor' WHERE `id` = 112 AND `name` = 'Java 监控';
UPDATE `system_menu` SET `name` = 'Redis Monitor' WHERE `id` = 113 AND `name` = 'Redis 监控';
UPDATE `system_menu` SET `name` = 'Form Builder' WHERE `id` = 114 AND `name` = '表单构建';
UPDATE `system_menu` SET `name` = 'Code Generation' WHERE `id` = 115 AND `name` = '代码生成';
UPDATE `system_menu` SET `name` = 'API Interface' WHERE `id` = 116 AND `name` = 'API 接口';

-- Log submenu items
UPDATE `system_menu` SET `name` = 'Operation Log' WHERE `id` = 500 AND `name` = '操作日志';
UPDATE `system_menu` SET `name` = 'Login Log' WHERE `id` = 501 AND `name` = '登录日志';

-- User Management buttons
UPDATE `system_menu` SET `name` = 'User Query' WHERE `id` = 1001 AND `name` = '用户查询';
UPDATE `system_menu` SET `name` = 'User Create' WHERE `id` = 1002 AND `name` = '用户新增';
UPDATE `system_menu` SET `name` = 'User Update' WHERE `id` = 1003 AND `name` = '用户修改';
UPDATE `system_menu` SET `name` = 'User Delete' WHERE `id` = 1004 AND `name` = '用户删除';
UPDATE `system_menu` SET `name` = 'User Export' WHERE `id` = 1005 AND `name` = '用户导出';
UPDATE `system_menu` SET `name` = 'User Import' WHERE `id` = 1006 AND `name` = '用户导入';
UPDATE `system_menu` SET `name` = 'Reset Password' WHERE `id` = 1007 AND `name` = '重置密码';

-- Role Management buttons
UPDATE `system_menu` SET `name` = 'Role Query' WHERE `id` = 1008 AND `name` = '角色查询';
UPDATE `system_menu` SET `name` = 'Role Create' WHERE `id` = 1009 AND `name` = '角色新增';
UPDATE `system_menu` SET `name` = 'Role Update' WHERE `id` = 1010 AND `name` = '角色修改';
UPDATE `system_menu` SET `name` = 'Role Delete' WHERE `id` = 1011 AND `name` = '角色删除';
UPDATE `system_menu` SET `name` = 'Role Export' WHERE `id` = 1012 AND `name` = '角色导出';

-- Menu Management buttons
UPDATE `system_menu` SET `name` = 'Menu Query' WHERE `id` = 1013 AND `name` = '菜单查询';
UPDATE `system_menu` SET `name` = 'Menu Create' WHERE `id` = 1014 AND `name` = '菜单新增';
UPDATE `system_menu` SET `name` = 'Menu Update' WHERE `id` = 1015 AND `name` = '菜单修改';
UPDATE `system_menu` SET `name` = 'Menu Delete' WHERE `id` = 1016 AND `name` = '菜单删除';

-- Department Management buttons
UPDATE `system_menu` SET `name` = 'Department Query' WHERE `id` = 1017 AND `name` = '部门查询';
UPDATE `system_menu` SET `name` = 'Department Create' WHERE `id` = 1018 AND `name` = '部门新增';
UPDATE `system_menu` SET `name` = 'Department Update' WHERE `id` = 1019 AND `name` = '部门修改';
UPDATE `system_menu` SET `name` = 'Department Delete' WHERE `id` = 1020 AND `name` = '部门删除';

-- Position Management buttons
UPDATE `system_menu` SET `name` = 'Position Query' WHERE `id` = 1021 AND `name` = '岗位查询';
UPDATE `system_menu` SET `name` = 'Position Create' WHERE `id` = 1022 AND `name` = '岗位新增';
UPDATE `system_menu` SET `name` = 'Position Update' WHERE `id` = 1023 AND `name` = '岗位修改';
UPDATE `system_menu` SET `name` = 'Position Delete' WHERE `id` = 1024 AND `name` = '岗位删除';
UPDATE `system_menu` SET `name` = 'Position Export' WHERE `id` = 1025 AND `name` = '岗位导出';

-- Dictionary Management buttons
UPDATE `system_menu` SET `name` = 'Dictionary Query' WHERE `id` = 1026 AND `name` = '字典查询';
UPDATE `system_menu` SET `name` = 'Dictionary Create' WHERE `id` = 1027 AND `name` = '字典新增';
UPDATE `system_menu` SET `name` = 'Dictionary Update' WHERE `id` = 1028 AND `name` = '字典修改';
UPDATE `system_menu` SET `name` = 'Dictionary Delete' WHERE `id` = 1029 AND `name` = '字典删除';
UPDATE `system_menu` SET `name` = 'Dictionary Export' WHERE `id` = 1030 AND `name` = '字典导出';

-- Configuration Management buttons
UPDATE `system_menu` SET `name` = 'Configuration Query' WHERE `id` = 1031 AND `name` = '配置查询';
UPDATE `system_menu` SET `name` = 'Configuration Create' WHERE `id` = 1032 AND `name` = '配置新增';
UPDATE `system_menu` SET `name` = 'Configuration Update' WHERE `id` = 1033 AND `name` = '配置修改';
UPDATE `system_menu` SET `name` = 'Configuration Delete' WHERE `id` = 1034 AND `name` = '配置删除';
UPDATE `system_menu` SET `name` = 'Configuration Export' WHERE `id` = 1035 AND `name` = '配置导出';

-- Notice buttons
UPDATE `system_menu` SET `name` = 'Notice Query' WHERE `id` = 1036 AND `name` = '公告查询';
UPDATE `system_menu` SET `name` = 'Notice Create' WHERE `id` = 1037 AND `name` = '公告新增';
UPDATE `system_menu` SET `name` = 'Notice Update' WHERE `id` = 1038 AND `name` = '公告修改';
UPDATE `system_menu` SET `name` = 'Notice Delete' WHERE `id` = 1039 AND `name` = '公告删除';

-- Operation Log buttons
UPDATE `system_menu` SET `name` = 'Operation Query' WHERE `id` = 1040 AND `name` = '操作查询';
UPDATE `system_menu` SET `name` = 'Log Export' WHERE `id` = 1042 AND `name` = '日志导出';

-- Login Log buttons
UPDATE `system_menu` SET `name` = 'Login Query' WHERE `id` = 1043 AND `name` = '登录查询';
UPDATE `system_menu` SET `name` = 'Log Export' WHERE `id` = 1045 AND `name` = '日志导出';

-- Token Management buttons
UPDATE `system_menu` SET `name` = 'Token List' WHERE `id` = 1046 AND `name` = '令牌列表';
UPDATE `system_menu` SET `name` = 'Token Delete' WHERE `id` = 1048 AND `name` = '令牌删除';

-- Scheduled Task buttons
UPDATE `system_menu` SET `name` = 'Task Create' WHERE `id` = 1050 AND `name` = '任务新增';
UPDATE `system_menu` SET `name` = 'Task Update' WHERE `id` = 1051 AND `name` = '任务修改';
UPDATE `system_menu` SET `name` = 'Task Delete' WHERE `id` = 1052 AND `name` = '任务删除';
UPDATE `system_menu` SET `name` = 'Status Update' WHERE `id` = 1053 AND `name` = '状态修改';
UPDATE `system_menu` SET `name` = 'Task Export' WHERE `id` = 1054 AND `name` = '任务导出';

-- Code Generation buttons
UPDATE `system_menu` SET `name` = 'Generation Update' WHERE `id` = 1056 AND `name` = '生成修改';
UPDATE `system_menu` SET `name` = 'Generation Delete' WHERE `id` = 1057 AND `name` = '生成删除';
UPDATE `system_menu` SET `name` = 'Import Code' WHERE `id` = 1058 AND `name` = '导入代码';
UPDATE `system_menu` SET `name` = 'Preview Code' WHERE `id` = 1059 AND `name` = '预览代码';
UPDATE `system_menu` SET `name` = 'Generate Code' WHERE `id` = 1060 AND `name` = '生成代码';

-- Permission Management buttons
UPDATE `system_menu` SET `name` = 'Set Role Menu Permission' WHERE `id` = 1063 AND `name` = '设置角色菜单权限';
UPDATE `system_menu` SET `name` = 'Set Role Data Permission' WHERE `id` = 1064 AND `name` = '设置角色数据权限';
UPDATE `system_menu` SET `name` = 'Set User Role' WHERE `id` = 1065 AND `name` = '设置用户角色';

-- Redis Management buttons
UPDATE `system_menu` SET `name` = 'Get Redis Monitor Info' WHERE `id` = 1066 AND `name` = '获得 Redis 监控信息';
UPDATE `system_menu` SET `name` = 'Get Redis Key List' WHERE `id` = 1067 AND `name` = '获得 Redis Key 列表';

-- Code Generation Example
UPDATE `system_menu` SET `name` = 'Code Generation Example' WHERE `id` = 1070 AND `name` = '代码生成案例';

-- Task Management buttons
UPDATE `system_menu` SET `name` = 'Task Trigger' WHERE `id` = 1075 AND `name` = '任务触发';
UPDATE `system_menu` SET `name` = 'Task Query' WHERE `id` = 1087 AND `name` = '任务查询';

-- Monitoring items
UPDATE `system_menu` SET `name` = 'Link Tracking' WHERE `id` = 1077 AND `name` = '链路追踪';
UPDATE `system_menu` SET `name` = 'Access Log' WHERE `id` = 1078 AND `name` = '访问日志';
UPDATE `system_menu` SET `name` = 'API Log' WHERE `id` = 1083 AND `name` = 'API 日志';
UPDATE `system_menu` SET `name` = 'Error Log' WHERE `id` = 1084 AND `name` = '错误日志';
UPDATE `system_menu` SET `name` = 'Monitor Center' WHERE `id` = 2740 AND `name` = '监控中心';

-- Log Management buttons
UPDATE `system_menu` SET `name` = 'Log Export' WHERE `id` = 1082 AND `name` = '日志导出';
UPDATE `system_menu` SET `name` = 'Log Process' WHERE `id` = 1085 AND `name` = '日志处理';
UPDATE `system_menu` SET `name` = 'Log Export' WHERE `id` = 1086 AND `name` = '日志导出';
UPDATE `system_menu` SET `name` = 'Log Query' WHERE `id` = 1088 AND `name` = '日志查询';
UPDATE `system_menu` SET `name` = 'Log Query' WHERE `id` = 1089 AND `name` = '日志查询';

-- File Management
UPDATE `system_menu` SET `name` = 'File List' WHERE `id` = 1090 AND `name` = '文件列表';
UPDATE `system_menu` SET `name` = 'File Query' WHERE `id` = 1091 AND `name` = '文件查询';
UPDATE `system_menu` SET `name` = 'File Delete' WHERE `id` = 1092 AND `name` = '文件删除';

-- SMS Management
UPDATE `system_menu` SET `name` = 'SMS Management' WHERE `id` = 1093 AND `name` = '短信管理';
UPDATE `system_menu` SET `name` = 'SMS Channel' WHERE `id` = 1094 AND `name` = '短信渠道';
UPDATE `system_menu` SET `name` = 'SMS Channel Query' WHERE `id` = 1095 AND `name` = '短信渠道查询';
UPDATE `system_menu` SET `name` = 'SMS Channel Create' WHERE `id` = 1096 AND `name` = '短信渠道创建';
UPDATE `system_menu` SET `name` = 'SMS Channel Update' WHERE `id` = 1097 AND `name` = '短信渠道更新';
UPDATE `system_menu` SET `name` = 'SMS Channel Delete' WHERE `id` = 1098 AND `name` = '短信渠道删除';
UPDATE `system_menu` SET `name` = 'SMS Template' WHERE `id` = 1100 AND `name` = '短信模板';
UPDATE `system_menu` SET `name` = 'SMS Template Query' WHERE `id` = 1101 AND `name` = '短信模板查询';
UPDATE `system_menu` SET `name` = 'SMS Template Create' WHERE `id` = 1102 AND `name` = '短信模板创建';
UPDATE `system_menu` SET `name` = 'SMS Template Update' WHERE `id` = 1103 AND `name` = '短信模板更新';
UPDATE `system_menu` SET `name` = 'SMS Template Delete' WHERE `id` = 1104 AND `name` = '短信模板删除';
UPDATE `system_menu` SET `name` = 'SMS Template Export' WHERE `id` = 1105 AND `name` = '短信模板导出';
UPDATE `system_menu` SET `name` = 'Send Test SMS' WHERE `id` = 1106 AND `name` = '发送测试短信';
UPDATE `system_menu` SET `name` = 'SMS Log' WHERE `id` = 1107 AND `name` = '短信日志';
UPDATE `system_menu` SET `name` = 'SMS Log Query' WHERE `id` = 1108 AND `name` = '短信日志查询';
UPDATE `system_menu` SET `name` = 'SMS Log Export' WHERE `id` = 1109 AND `name` = '短信日志导出';

-- Payment Management
UPDATE `system_menu` SET `name` = 'Payment Management' WHERE `id` = 1117 AND `name` = '支付管理';
UPDATE `system_menu` SET `name` = 'Application Info' WHERE `id` = 1126 AND `name` = '应用信息';
UPDATE `system_menu` SET `name` = 'Payment App Query' WHERE `id` = 1127 AND `name` = '支付应用信息查询';
UPDATE `system_menu` SET `name` = 'Payment App Create' WHERE `id` = 1128 AND `name` = '支付应用信息创建';
UPDATE `system_menu` SET `name` = 'Payment App Update' WHERE `id` = 1129 AND `name` = '支付应用信息更新';
UPDATE `system_menu` SET `name` = 'Payment App Delete' WHERE `id` = 1130 AND `name` = '支付应用信息删除';
UPDATE `system_menu` SET `name` = 'Key Parsing' WHERE `id` = 1132 AND `name` = '秘钥解析';
UPDATE `system_menu` SET `name` = 'Payment Merchant Query' WHERE `id` = 1133 AND `name` = '支付商户信息查询';
UPDATE `system_menu` SET `name` = 'Payment Merchant Create' WHERE `id` = 1134 AND `name` = '支付商户信息创建';
UPDATE `system_menu` SET `name` = 'Payment Merchant Update' WHERE `id` = 1135 AND `name` = '支付商户信息更新';
UPDATE `system_menu` SET `name` = 'Payment Merchant Delete' WHERE `id` = 1136 AND `name` = '支付商户信息删除';
UPDATE `system_menu` SET `name` = 'Payment Merchant Export' WHERE `id` = 1137 AND `name` = '支付商户信息导出';
UPDATE `system_menu` SET `name` = 'Key Parsing' WHERE `id` = 1150 AND `name` = '秘钥解析';
UPDATE `system_menu` SET `name` = 'Refund Order' WHERE `id` = 1161 AND `name` = '退款订单';
UPDATE `system_menu` SET `name` = 'Refund Order Query' WHERE `id` = 1162 AND `name` = '退款订单查询';
UPDATE `system_menu` SET `name` = 'Refund Order Export' WHERE `id` = 1166 AND `name` = '退款订单导出';
UPDATE `system_menu` SET `name` = 'Payment Order' WHERE `id` = 1173 AND `name` = '支付订单';
UPDATE `system_menu` SET `name` = 'Payment Order Query' WHERE `id` = 1174 AND `name` = '支付订单查询';
UPDATE `system_menu` SET `name` = 'Payment Order Export' WHERE `id` = 1178 AND `name` = '支付订单导出';

-- Tenant Management
UPDATE `system_menu` SET `name` = 'Tenant List' WHERE `id` = 1138 AND `name` = '租户列表';
UPDATE `system_menu` SET `name` = 'Tenant Query' WHERE `id` = 1139 AND `name` = '租户查询';
UPDATE `system_menu` SET `name` = 'Tenant Create' WHERE `id` = 1140 AND `name` = '租户创建';
UPDATE `system_menu` SET `name` = 'Tenant Update' WHERE `id` = 1141 AND `name` = '租户更新';
UPDATE `system_menu` SET `name` = 'Tenant Delete' WHERE `id` = 1142 AND `name` = '租户删除';
UPDATE `system_menu` SET `name` = 'Tenant Export' WHERE `id` = 1143 AND `name` = '租户导出';

-- Leave Management
UPDATE `system_menu` SET `name` = 'Leave Query' WHERE `id` = 1118 AND `name` = '请假查询';
UPDATE `system_menu` SET `name` = 'Leave Application Query' WHERE `id` = 1119 AND `name` = '请假申请查询';
UPDATE `system_menu` SET `name` = 'Leave Application Create' WHERE `id` = 1120 AND `name` = '请假申请创建';

-- Workflow Management
UPDATE `system_menu` SET `name` = 'Workflow' WHERE `id` = 1185 AND `name` = '工作流程';
UPDATE `system_menu` SET `name` = 'Process Management' WHERE `id` = 1186 AND `name` = '流程管理';
UPDATE `system_menu` SET `name` = 'Process Form' WHERE `id` = 1187 AND `name` = '流程表单';
UPDATE `system_menu` SET `name` = 'Form Query' WHERE `id` = 1188 AND `name` = '表单查询';
UPDATE `system_menu` SET `name` = 'Form Create' WHERE `id` = 1189 AND `name` = '表单创建';
UPDATE `system_menu` SET `name` = 'Form Update' WHERE `id` = 1190 AND `name` = '表单更新';
UPDATE `system_menu` SET `name` = 'Form Delete' WHERE `id` = 1191 AND `name` = '表单删除';
UPDATE `system_menu` SET `name` = 'Form Export' WHERE `id` = 1192 AND `name` = '表单导出';
UPDATE `system_menu` SET `name` = 'Process Model' WHERE `id` = 1193 AND `name` = '流程模型';
UPDATE `system_menu` SET `name` = 'Model Query' WHERE `id` = 1194 AND `name` = '模型查询';
UPDATE `system_menu` SET `name` = 'Model Create' WHERE `id` = 1195 AND `name` = '模型创建';
UPDATE `system_menu` SET `name` = 'Model Update' WHERE `id` = 1197 AND `name` = '模型更新';
UPDATE `system_menu` SET `name` = 'Model Delete' WHERE `id` = 1198 AND `name` = '模型删除';
UPDATE `system_menu` SET `name` = 'Model Deploy' WHERE `id` = 1199 AND `name` = '模型发布';
UPDATE `system_menu` SET `name` = 'Approval Center' WHERE `id` = 1200 AND `name` = '审批中心';
UPDATE `system_menu` SET `name` = 'My Process' WHERE `id` = 1201 AND `name` = '我的流程';
UPDATE `system_menu` SET `name` = 'Process Instance Query' WHERE `id` = 1202 AND `name` = '流程实例的查询';
UPDATE `system_menu` SET `name` = 'Todo Task' WHERE `id` = 1207 AND `name` = '待办任务';
UPDATE `system_menu` SET `name` = 'Done Task' WHERE `id` = 1208 AND `name` = '已办任务';
UPDATE `system_menu` SET `name` = 'User Group' WHERE `id` = 1209 AND `name` = '用户分组';
UPDATE `system_menu` SET `name` = 'User Group Query' WHERE `id` = 1210 AND `name` = '用户组查询';
UPDATE `system_menu` SET `name` = 'User Group Create' WHERE `id` = 1211 AND `name` = '用户组创建';
UPDATE `system_menu` SET `name` = 'User Group Update' WHERE `id` = 1212 AND `name` = '用户组更新';
UPDATE `system_menu` SET `name` = 'User Group Delete' WHERE `id` = 1213 AND `name` = '用户组删除';
UPDATE `system_menu` SET `name` = 'Process Definition Query' WHERE `id` = 1215 AND `name` = '流程定义查询';
UPDATE `system_menu` SET `name` = 'Task Assignment Rule Query' WHERE `id` = 1216 AND `name` = '流程任务分配规则查询';
UPDATE `system_menu` SET `name` = 'Task Assignment Rule Create' WHERE `id` = 1217 AND `name` = '流程任务分配规则创建';
UPDATE `system_menu` SET `name` = 'Task Assignment Rule Update' WHERE `id` = 1218 AND `name` = '流程任务分配规则更新';
UPDATE `system_menu` SET `name` = 'Process Instance Create' WHERE `id` = 1219 AND `name` = '流程实例的创建';
UPDATE `system_menu` SET `name` = 'Process Instance Cancel' WHERE `id` = 1220 AND `name` = '流程实例的取消';
UPDATE `system_menu` SET `name` = 'Process Task Query' WHERE `id` = 1221 AND `name` = '流程任务的查询';
UPDATE `system_menu` SET `name` = 'Process Task Update' WHERE `id` = 1222 AND `name` = '流程任务的更新';

-- Tenant Management (continued)
UPDATE `system_menu` SET `name` = 'Tenant Management' WHERE `id` = 1224 AND `name` = '租户管理';
UPDATE `system_menu` SET `name` = 'Tenant Package' WHERE `id` = 1225 AND `name` = '租户套餐';
UPDATE `system_menu` SET `name` = 'Tenant Package Query' WHERE `id` = 1226 AND `name` = '租户套餐查询';
UPDATE `system_menu` SET `name` = 'Tenant Package Create' WHERE `id` = 1227 AND `name` = '租户套餐创建';
UPDATE `system_menu` SET `name` = 'Tenant Package Update' WHERE `id` = 1228 AND `name` = '租户套餐更新';
UPDATE `system_menu` SET `name` = 'Tenant Package Delete' WHERE `id` = 1229 AND `name` = '租户套餐删除';

-- File Management
UPDATE `system_menu` SET `name` = 'File Management' WHERE `id` = 1243 AND `name` = '文件管理';
UPDATE `system_menu` SET `name` = 'File Configuration' WHERE `id` = 1237 AND `name` = '文件配置';
UPDATE `system_menu` SET `name` = 'File Config Query' WHERE `id` = 1238 AND `name` = '文件配置查询';
UPDATE `system_menu` SET `name` = 'File Config Create' WHERE `id` = 1239 AND `name` = '文件配置创建';
UPDATE `system_menu` SET `name` = 'File Config Update' WHERE `id` = 1240 AND `name` = '文件配置更新';
UPDATE `system_menu` SET `name` = 'File Config Delete' WHERE `id` = 1241 AND `name` = '文件配置删除';
UPDATE `system_menu` SET `name` = 'File Config Export' WHERE `id` = 1242 AND `name` = '文件配置导出';

-- Data Source Configuration
UPDATE `system_menu` SET `name` = 'Data Source Config' WHERE `id` = 1255 AND `name` = '数据源配置';
UPDATE `system_menu` SET `name` = 'Data Source Config Query' WHERE `id` = 1256 AND `name` = '数据源配置查询';
UPDATE `system_menu` SET `name` = 'Data Source Config Create' WHERE `id` = 1257 AND `name` = '数据源配置创建';
UPDATE `system_menu` SET `name` = 'Data Source Config Update' WHERE `id` = 1258 AND `name` = '数据源配置更新';
UPDATE `system_menu` SET `name` = 'Data Source Config Delete' WHERE `id` = 1259 AND `name` = '数据源配置删除';
UPDATE `system_menu` SET `name` = 'Data Source Config Export' WHERE `id` = 1260 AND `name` = '数据源配置导出';

-- OAuth 2.0 Management
UPDATE `system_menu` SET `name` = 'Application Management' WHERE `id` = 1263 AND `name` = '应用管理';
UPDATE `system_menu` SET `name` = 'Client Query' WHERE `id` = 1264 AND `name` = '客户端查询';
UPDATE `system_menu` SET `name` = 'Client Create' WHERE `id` = 1265 AND `name` = '客户端创建';
UPDATE `system_menu` SET `name` = 'Client Update' WHERE `id` = 1266 AND `name` = '客户端更新';
UPDATE `system_menu` SET `name` = 'Client Delete' WHERE `id` = 1267 AND `name` = '客户端删除';

-- Report Management
UPDATE `system_menu` SET `name` = 'Report Management' WHERE `id` = 1281 AND `name` = '报表管理';
UPDATE `system_menu` SET `name` = 'Report Designer' WHERE `id` = 1282 AND `name` = '报表设计器';

-- Mall Management
UPDATE `system_menu` SET `name` = 'Product Center' WHERE `id` = 2000 AND `name` = '商品中心';
UPDATE `system_menu` SET `name` = 'Product Category' WHERE `id` = 2002 AND `name` = '商品分类';
UPDATE `system_menu` SET `name` = 'Category Query' WHERE `id` = 2003 AND `name` = '分类查询';
UPDATE `system_menu` SET `name` = 'Category Create' WHERE `id` = 2004 AND `name` = '分类创建';
UPDATE `system_menu` SET `name` = 'Category Update' WHERE `id` = 2005 AND `name` = '分类更新';
UPDATE `system_menu` SET `name` = 'Category Delete' WHERE `id` = 2006 AND `name` = '分类删除';
UPDATE `system_menu` SET `name` = 'Product Brand' WHERE `id` = 2008 AND `name` = '商品品牌';
UPDATE `system_menu` SET `name` = 'Brand Query' WHERE `id` = 2009 AND `name` = '品牌查询';
UPDATE `system_menu` SET `name` = 'Brand Create' WHERE `id` = 2010 AND `name` = '品牌创建';
UPDATE `system_menu` SET `name` = 'Brand Update' WHERE `id` = 2011 AND `name` = '品牌更新';
UPDATE `system_menu` SET `name` = 'Brand Delete' WHERE `id` = 2012 AND `name` = '品牌删除';
UPDATE `system_menu` SET `name` = 'Product List' WHERE `id` = 2014 AND `name` = '商品列表';
UPDATE `system_menu` SET `name` = 'Product Query' WHERE `id` = 2015 AND `name` = '商品查询';
UPDATE `system_menu` SET `name` = 'Product Create' WHERE `id` = 2016 AND `name` = '商品创建';
UPDATE `system_menu` SET `name` = 'Product Update' WHERE `id` = 2017 AND `name` = '商品更新';
UPDATE `system_menu` SET `name` = 'Product Delete' WHERE `id` = 2018 AND `name` = '商品删除';
UPDATE `system_menu` SET `name` = 'Product Property' WHERE `id` = 2019 AND `name` = '商品属性';
UPDATE `system_menu` SET `name` = 'Property Query' WHERE `id` = 2020 AND `name` = '规格查询';
UPDATE `system_menu` SET `name` = 'Property Create' WHERE `id` = 2021 AND `name` = '规格创建';
UPDATE `system_menu` SET `name` = 'Property Update' WHERE `id` = 2022 AND `name` = '规格更新';
UPDATE `system_menu` SET `name` = 'Property Delete' WHERE `id` = 2023 AND `name` = '规格删除';

-- Author Dynamic
UPDATE `system_menu` SET `name` = 'Author Dynamic' WHERE `id` = 1254 AND `name` = '作者动态';

-- Promotion Management
UPDATE `system_menu` SET `name` = 'Banner Query' WHERE `id` = 2026 AND `name` = 'Banner查询';
UPDATE `system_menu` SET `name` = 'Banner Create' WHERE `id` = 2027 AND `name` = 'Banner创建';
UPDATE `system_menu` SET `name` = 'Banner Update' WHERE `id` = 2028 AND `name` = 'Banner更新';
UPDATE `system_menu` SET `name` = 'Banner Delete' WHERE `id` = 2029 AND `name` = 'Banner删除';
UPDATE `system_menu` SET `name` = 'Marketing Center' WHERE `id` = 2030 AND `name` = '营销中心';
UPDATE `system_menu` SET `name` = 'Coupon List' WHERE `id` = 2032 AND `name` = '优惠劵列表';
UPDATE `system_menu` SET `name` = 'Coupon Template Query' WHERE `id` = 2033 AND `name` = '优惠劵模板查询';
UPDATE `system_menu` SET `name` = 'Coupon Template Create' WHERE `id` = 2034 AND `name` = '优惠劵模板创建';
UPDATE `system_menu` SET `name` = 'Coupon Template Update' WHERE `id` = 2035 AND `name` = '优惠劵模板更新';
UPDATE `system_menu` SET `name` = 'Coupon Template Delete' WHERE `id` = 2036 AND `name` = '优惠劵模板删除';
UPDATE `system_menu` SET `name` = 'Collection Record' WHERE `id` = 2038 AND `name` = '领取记录';
UPDATE `system_menu` SET `name` = 'Coupon Query' WHERE `id` = 2039 AND `name` = '优惠劵查询';
UPDATE `system_menu` SET `name` = 'Coupon Delete' WHERE `id` = 2040 AND `name` = '优惠劵删除';
UPDATE `system_menu` SET `name` = 'Full Reduction' WHERE `id` = 2041 AND `name` = '满减送';
UPDATE `system_menu` SET `name` = 'Reward Activity Query' WHERE `id` = 2042 AND `name` = '满减送活动查询';
UPDATE `system_menu` SET `name` = 'Reward Activity Create' WHERE `id` = 2043 AND `name` = '满减送活动创建';
UPDATE `system_menu` SET `name` = 'Reward Activity Update' WHERE `id` = 2044 AND `name` = '满减送活动更新';
UPDATE `system_menu` SET `name` = 'Reward Activity Delete' WHERE `id` = 2045 AND `name` = '满减送活动删除';
UPDATE `system_menu` SET `name` = 'Reward Activity Close' WHERE `id` = 2046 AND `name` = '满减送活动关闭';
UPDATE `system_menu` SET `name` = 'Limited Discount' WHERE `id` = 2047 AND `name` = '限时折扣';
UPDATE `system_menu` SET `name` = 'Discount Activity Query' WHERE `id` = 2048 AND `name` = '限时折扣活动查询';
UPDATE `system_menu` SET `name` = 'Discount Activity Create' WHERE `id` = 2049 AND `name` = '限时折扣活动创建';
UPDATE `system_menu` SET `name` = 'Discount Activity Update' WHERE `id` = 2050 AND `name` = '限时折扣活动更新';
UPDATE `system_menu` SET `name` = 'Discount Activity Delete' WHERE `id` = 2051 AND `name` = '限时折扣活动删除';
UPDATE `system_menu` SET `name` = 'Discount Activity Close' WHERE `id` = 2052 AND `name` = '限时折扣活动关闭';

-- Seckill Management
UPDATE `system_menu` SET `name` = 'Seckill Product' WHERE `id` = 2059 AND `name` = '秒杀商品';
UPDATE `system_menu` SET `name` = 'Seckill Activity Query' WHERE `id` = 2060 AND `name` = '秒杀活动查询';
UPDATE `system_menu` SET `name` = 'Seckill Activity Create' WHERE `id` = 2061 AND `name` = '秒杀活动创建';
UPDATE `system_menu` SET `name` = 'Seckill Activity Update' WHERE `id` = 2062 AND `name` = '秒杀活动更新';
UPDATE `system_menu` SET `name` = 'Seckill Activity Delete' WHERE `id` = 2063 AND `name` = '秒杀活动删除';
UPDATE `system_menu` SET `name` = 'Seckill Activity Close' WHERE `id` = 2075 AND `name` = '秒杀活动关闭';
UPDATE `system_menu` SET `name` = 'Seckill Time' WHERE `id` = 2066 AND `name` = '秒杀时段';
UPDATE `system_menu` SET `name` = 'Seckill Config Query' WHERE `id` = 2067 AND `name` = '秒杀时段查询';
UPDATE `system_menu` SET `name` = 'Seckill Config Create' WHERE `id` = 2068 AND `name` = '秒杀时段创建';
UPDATE `system_menu` SET `name` = 'Seckill Config Update' WHERE `id` = 2069 AND `name` = '秒杀时段更新';
UPDATE `system_menu` SET `name` = 'Seckill Config Delete' WHERE `id` = 2070 AND `name` = '秒杀时段删除';

-- Order Management
UPDATE `system_menu` SET `name` = 'Order Center' WHERE `id` = 2072 AND `name` = '订单中心';
UPDATE `system_menu` SET `name` = 'After Sale Refund' WHERE `id` = 2073 AND `name` = '售后退款';
UPDATE `system_menu` SET `name` = 'After Sale Query' WHERE `id` = 2074 AND `name` = '售后查询';
UPDATE `system_menu` SET `name` = 'Order List' WHERE `id` = 2076 AND `name` = '订单列表';

-- Area Management
UPDATE `system_menu` SET `name` = 'Area Management' WHERE `id` = 2083 AND `name` = '地区管理';

-- WeChat Official Account Management
UPDATE `system_menu` SET `name` = 'Official Account Management' WHERE `id` = 2084 AND `name` = '公众号管理';
UPDATE `system_menu` SET `name` = 'Account Management' WHERE `id` = 2085 AND `name` = '账号管理';
UPDATE `system_menu` SET `name` = 'Create Account' WHERE `id` = 2086 AND `name` = '新增账号';
UPDATE `system_menu` SET `name` = 'Update Account' WHERE `id` = 2087 AND `name` = '修改账号';
UPDATE `system_menu` SET `name` = 'Query Account' WHERE `id` = 2088 AND `name` = '查询账号';
UPDATE `system_menu` SET `name` = 'Delete Account' WHERE `id` = 2089 AND `name` = '删除账号';
UPDATE `system_menu` SET `name` = 'Generate QR Code' WHERE `id` = 2090 AND `name` = '生成二维码';
UPDATE `system_menu` SET `name` = 'Clear API Quota' WHERE `id` = 2091 AND `name` = '清空 API 配额';
UPDATE `system_menu` SET `name` = 'Data Statistics' WHERE `id` = 2092 AND `name` = '数据统计';
UPDATE `system_menu` SET `name` = 'Tag Management' WHERE `id` = 2093 AND `name` = '标签管理';
UPDATE `system_menu` SET `name` = 'Query Tag' WHERE `id` = 2094 AND `name` = '查询标签';
UPDATE `system_menu` SET `name` = 'Create Tag' WHERE `id` = 2095 AND `name` = '新增标签';
UPDATE `system_menu` SET `name` = 'Update Tag' WHERE `id` = 2096 AND `name` = '修改标签';
UPDATE `system_menu` SET `name` = 'Delete Tag' WHERE `id` = 2097 AND `name` = '删除标签';
UPDATE `system_menu` SET `name` = 'Sync Tag' WHERE `id` = 2098 AND `name` = '同步标签';
UPDATE `system_menu` SET `name` = 'Fans Management' WHERE `id` = 2099 AND `name` = '粉丝管理';
UPDATE `system_menu` SET `name` = 'Query Fans' WHERE `id` = 2100 AND `name` = '查询粉丝';
UPDATE `system_menu` SET `name` = 'Update Fans' WHERE `id` = 2101 AND `name` = '修改粉丝';
UPDATE `system_menu` SET `name` = 'Sync Fans' WHERE `id` = 2102 AND `name` = '同步粉丝';
UPDATE `system_menu` SET `name` = 'Message Management' WHERE `id` = 2103 AND `name` = '消息管理';
UPDATE `system_menu` SET `name` = 'Article Publish Record' WHERE `id` = 2104 AND `name` = '图文发表记录';

-- WeChat Additional Features
UPDATE `system_menu` SET `name` = 'Query Publish List' WHERE `id` = 2105 AND `name` = '查询发布列表';
UPDATE `system_menu` SET `name` = 'Publish Draft' WHERE `id` = 2106 AND `name` = '发布草稿';
UPDATE `system_menu` SET `name` = 'Delete Publish Record' WHERE `id` = 2107 AND `name` = '删除发布记录';
UPDATE `system_menu` SET `name` = 'Article Draft Box' WHERE `id` = 2108 AND `name` = '图文草稿箱';
UPDATE `system_menu` SET `name` = 'Create Draft' WHERE `id` = 2109 AND `name` = '新建草稿';
UPDATE `system_menu` SET `name` = 'Update Draft' WHERE `id` = 2110 AND `name` = '修改草稿';
UPDATE `system_menu` SET `name` = 'Query Draft' WHERE `id` = 2111 AND `name` = '查询草稿';
UPDATE `system_menu` SET `name` = 'Delete Draft' WHERE `id` = 2112 AND `name` = '删除草稿';
UPDATE `system_menu` SET `name` = 'Material Management' WHERE `id` = 2113 AND `name` = '素材管理';
UPDATE `system_menu` SET `name` = 'Upload Temporary Material' WHERE `id` = 2114 AND `name` = '上传临时素材';
UPDATE `system_menu` SET `name` = 'Upload Permanent Material' WHERE `id` = 2115 AND `name` = '上传永久素材';
UPDATE `system_menu` SET `name` = 'Delete Material' WHERE `id` = 2116 AND `name` = '删除素材';
UPDATE `system_menu` SET `name` = 'Upload News Image' WHERE `id` = 2117 AND `name` = '上传图文图片';
UPDATE `system_menu` SET `name` = 'Query Material' WHERE `id` = 2118 AND `name` = '查询素材';
UPDATE `system_menu` SET `name` = 'Menu Management' WHERE `id` = 2119 AND `name` = '菜单管理';
UPDATE `system_menu` SET `name` = 'Auto Reply' WHERE `id` = 2120 AND `name` = '自动回复';
UPDATE `system_menu` SET `name` = 'Query Reply' WHERE `id` = 2121 AND `name` = '查询回复';
UPDATE `system_menu` SET `name` = 'Create Reply' WHERE `id` = 2122 AND `name` = '新增回复';
UPDATE `system_menu` SET `name` = 'Update Reply' WHERE `id` = 2123 AND `name` = '修改回复';
UPDATE `system_menu` SET `name` = 'Delete Reply' WHERE `id` = 2124 AND `name` = '删除回复';
UPDATE `system_menu` SET `name` = 'Query Menu' WHERE `id` = 2125 AND `name` = '查询菜单';
UPDATE `system_menu` SET `name` = 'Save Menu' WHERE `id` = 2126 AND `name` = '保存菜单';
UPDATE `system_menu` SET `name` = 'Delete Menu' WHERE `id` = 2127 AND `name` = '删除菜单';
UPDATE `system_menu` SET `name` = 'Query Message' WHERE `id` = 2128 AND `name` = '查询消息';
UPDATE `system_menu` SET `name` = 'Send Message' WHERE `id` = 2129 AND `name` = '发送消息';

-- Email Management
UPDATE `system_menu` SET `name` = 'Email Management' WHERE `id` = 2130 AND `name` = '邮箱管理';
UPDATE `system_menu` SET `name` = 'Email Account' WHERE `id` = 2131 AND `name` = '邮箱账号';
UPDATE `system_menu` SET `name` = 'Account Query' WHERE `id` = 2132 AND `name` = '账号查询';
UPDATE `system_menu` SET `name` = 'Account Create' WHERE `id` = 2133 AND `name` = '账号创建';
UPDATE `system_menu` SET `name` = 'Account Update' WHERE `id` = 2134 AND `name` = '账号更新';
UPDATE `system_menu` SET `name` = 'Account Delete' WHERE `id` = 2135 AND `name` = '账号删除';
UPDATE `system_menu` SET `name` = 'Email Template' WHERE `id` = 2136 AND `name` = '邮件模版';
UPDATE `system_menu` SET `name` = 'Template Query' WHERE `id` = 2137 AND `name` = '模版查询';
UPDATE `system_menu` SET `name` = 'Template Create' WHERE `id` = 2138 AND `name` = '模版创建';
UPDATE `system_menu` SET `name` = 'Template Update' WHERE `id` = 2139 AND `name` = '模版更新';
UPDATE `system_menu` SET `name` = 'Template Delete' WHERE `id` = 2140 AND `name` = '模版删除';
UPDATE `system_menu` SET `name` = 'Email Log' WHERE `id` = 2141 AND `name` = '邮件记录';
UPDATE `system_menu` SET `name` = 'Log Query' WHERE `id` = 2142 AND `name` = '日志查询';
UPDATE `system_menu` SET `name` = 'Send Test Email' WHERE `id` = 2143 AND `name` = '发送测试邮件';

-- Notification Management
UPDATE `system_menu` SET `name` = 'Notification Management' WHERE `id` = 2144 AND `name` = '站内信管理';
UPDATE `system_menu` SET `name` = 'Template Management' WHERE `id` = 2145 AND `name` = '模板管理';
UPDATE `system_menu` SET `name` = 'Notify Template Query' WHERE `id` = 2146 AND `name` = '站内信模板查询';
UPDATE `system_menu` SET `name` = 'Notify Template Create' WHERE `id` = 2147 AND `name` = '站内信模板创建';
UPDATE `system_menu` SET `name` = 'Notify Template Update' WHERE `id` = 2148 AND `name` = '站内信模板更新';
UPDATE `system_menu` SET `name` = 'Notify Template Delete' WHERE `id` = 2149 AND `name` = '站内信模板删除';
UPDATE `system_menu` SET `name` = 'Send Test Notification' WHERE `id` = 2150 AND `name` = '发送测试站内信';
UPDATE `system_menu` SET `name` = 'Message Record' WHERE `id` = 2151 AND `name` = '消息记录';
UPDATE `system_menu` SET `name` = 'Notify Message Query' WHERE `id` = 2152 AND `name` = '站内信消息查询';

-- Sensitive Word Management
UPDATE `system_menu` SET `name` = 'Sensitive Word' WHERE `id` = 2153 AND `name` = '敏感词';
UPDATE `system_menu` SET `name` = 'Sensitive Word Query' WHERE `id` = 2154 AND `name` = '敏感词查询';
UPDATE `system_menu` SET `name` = 'Sensitive Word Create' WHERE `id` = 2155 AND `name` = '敏感词创建';
UPDATE `system_menu` SET `name` = 'Sensitive Word Update' WHERE `id` = 2156 AND `name` = '敏感词更新';
UPDATE `system_menu` SET `name` = 'Sensitive Word Delete' WHERE `id` = 2157 AND `name` = '敏感词删除';
UPDATE `system_menu` SET `name` = 'Sensitive Word Export' WHERE `id` = 2158 AND `name` = '敏感词导出';
UPDATE `system_menu` SET `name` = 'Tag Query' WHERE `id` = 2159 AND `name` = '标签查询';
UPDATE `system_menu` SET `name` = 'Tag Create' WHERE `id` = 2160 AND `name` = '标签新增';
UPDATE `system_menu` SET `name` = 'Tag Update' WHERE `id` = 2161 AND `name` = '标签修改';
UPDATE `system_menu` SET `name` = 'Tag Delete' WHERE `id` = 2162 AND `name` = '标签删除';

-- Member Management
UPDATE `system_menu` SET `name` = 'Member Management' WHERE `id` = 2164 AND `name` = '会员中心';
UPDATE `system_menu` SET `name` = 'Member User' WHERE `id` = 2166 AND `name` = '会员用户';
UPDATE `system_menu` SET `name` = 'Member User Query' WHERE `id` = 2167 AND `name` = '会员用户查询';
UPDATE `system_menu` SET `name` = 'Member User Update' WHERE `id` = 2168 AND `name` = '会员用户更新';
UPDATE `system_menu` SET `name` = 'Member Tag' WHERE `id` = 2170 AND `name` = '会员标签';
UPDATE `system_menu` SET `name` = 'Member Tag Query' WHERE `id` = 2171 AND `name` = '会员标签查询';
UPDATE `system_menu` SET `name` = 'Member Tag Create' WHERE `id` = 2172 AND `name` = '会员标签新增';
UPDATE `system_menu` SET `name` = 'Member Tag Update' WHERE `id` = 2173 AND `name` = '会员标签更新';
UPDATE `system_menu` SET `name` = 'Member Tag Delete' WHERE `id` = 2174 AND `name` = '会员标签删除';
UPDATE `system_menu` SET `name` = 'Member Level' WHERE `id` = 2175 AND `name` = '会员等级';
UPDATE `system_menu` SET `name` = 'Member Level Query' WHERE `id` = 2176 AND `name` = '会员等级查询';
UPDATE `system_menu` SET `name` = 'Member Level Create' WHERE `id` = 2177 AND `name` = '会员等级新增';
UPDATE `system_menu` SET `name` = 'Member Level Update' WHERE `id` = 2178 AND `name` = '会员等级更新';
UPDATE `system_menu` SET `name` = 'Member Level Delete' WHERE `id` = 2179 AND `name` = '会员等级删除';
UPDATE `system_menu` SET `name` = 'Member Group' WHERE `id` = 2180 AND `name` = '会员分组';
UPDATE `system_menu` SET `name` = 'Member Group Query' WHERE `id` = 2181 AND `name` = '会员分组查询';
UPDATE `system_menu` SET `name` = 'Member Group Create' WHERE `id` = 2182 AND `name` = '会员分组新增';
UPDATE `system_menu` SET `name` = 'Member Group Update' WHERE `id` = 2183 AND `name` = '会员分组更新';
UPDATE `system_menu` SET `name` = 'Member Group Delete' WHERE `id` = 2184 AND `name` = '会员分组删除';

-- Additional Menu Items (continued)
UPDATE `system_menu` SET `name` = 'Seckill Activity' WHERE `id` = 2209 AND `name` = '秒杀活动';
UPDATE `system_menu` SET `name` = 'Member Center' WHERE `id` = 2262 AND `name` = '会员中心';
UPDATE `system_menu` SET `name` = 'Member Config' WHERE `id` = 2275 AND `name` = '会员配置';
UPDATE `system_menu` SET `name` = 'Member Config Query' WHERE `id` = 2276 AND `name` = '会员配置查询';
UPDATE `system_menu` SET `name` = 'Member Config Save' WHERE `id` = 2277 AND `name` = '会员配置保存';
UPDATE `system_menu` SET `name` = 'Sign In Config' WHERE `id` = 2281 AND `name` = '签到配置';
UPDATE `system_menu` SET `name` = 'Point Sign In Config Query' WHERE `id` = 2282 AND `name` = '积分签到规则查询';
UPDATE `system_menu` SET `name` = 'Point Sign In Config Create' WHERE `id` = 2283 AND `name` = '积分签到规则创建';
UPDATE `system_menu` SET `name` = 'Point Sign In Config Update' WHERE `id` = 2284 AND `name` = '积分签到规则更新';
UPDATE `system_menu` SET `name` = 'Point Sign In Config Delete' WHERE `id` = 2285 AND `name` = '积分签到规则删除';
UPDATE `system_menu` SET `name` = 'Member Points' WHERE `id` = 2287 AND `name` = '会员积分';
UPDATE `system_menu` SET `name` = 'User Point Record Query' WHERE `id` = 2288 AND `name` = '用户积分记录查询';
UPDATE `system_menu` SET `name` = 'Sign In Record' WHERE `id` = 2293 AND `name` = '签到记录';
UPDATE `system_menu` SET `name` = 'User Sign In Point Query' WHERE `id` = 2294 AND `name` = '用户签到积分查询';
UPDATE `system_menu` SET `name` = 'User Sign In Point Delete' WHERE `id` = 2297 AND `name` = '用户签到积分删除';
UPDATE `system_menu` SET `name` = 'Member Sign In' WHERE `id` = 2300 AND `name` = '会员签到';
UPDATE `system_menu` SET `name` = 'Callback Notify' WHERE `id` = 2301 AND `name` = '回调通知';
UPDATE `system_menu` SET `name` = 'Payment Notify Query' WHERE `id` = 2302 AND `name` = '支付通知查询';

-- Group Buy Activities
UPDATE `system_menu` SET `name` = 'Group Buy Activity' WHERE `id` = 2303 AND `name` = '拼团活动';
UPDATE `system_menu` SET `name` = 'Group Buy Product' WHERE `id` = 2304 AND `name` = '拼团商品';
UPDATE `system_menu` SET `name` = 'Group Buy Activity Query' WHERE `id` = 2305 AND `name` = '拼团活动查询';
UPDATE `system_menu` SET `name` = 'Group Buy Activity Create' WHERE `id` = 2306 AND `name` = '拼团活动创建';
UPDATE `system_menu` SET `name` = 'Group Buy Activity Update' WHERE `id` = 2307 AND `name` = '拼团活动更新';
UPDATE `system_menu` SET `name` = 'Group Buy Activity Delete' WHERE `id` = 2308 AND `name` = '拼团活动删除';
UPDATE `system_menu` SET `name` = 'Group Buy Activity Close' WHERE `id` = 2309 AND `name` = '拼团活动关闭';

-- Bargain Activities
UPDATE `system_menu` SET `name` = 'Bargain Activity' WHERE `id` = 2310 AND `name` = '砍价活动';
UPDATE `system_menu` SET `name` = 'Bargain Product' WHERE `id` = 2311 AND `name` = '砍价商品';
UPDATE `system_menu` SET `name` = 'Bargain Activity Query' WHERE `id` = 2312 AND `name` = '砍价活动查询';
UPDATE `system_menu` SET `name` = 'Bargain Activity Create' WHERE `id` = 2313 AND `name` = '砍价活动创建';
UPDATE `system_menu` SET `name` = 'Bargain Activity Update' WHERE `id` = 2314 AND `name` = '砍价活动更新';
UPDATE `system_menu` SET `name` = 'Bargain Activity Delete' WHERE `id` = 2315 AND `name` = '砍价活动删除';
UPDATE `system_menu` SET `name` = 'Bargain Activity Close' WHERE `id` = 2316 AND `name` = '砍价活动关闭';

-- Member Management (Additional)
UPDATE `system_menu` SET `name` = 'Member Management' WHERE `id` = 2317 AND `name` = '会员管理';
UPDATE `system_menu` SET `name` = 'Member User Query' WHERE `id` = 2318 AND `name` = '会员用户查询';
UPDATE `system_menu` SET `name` = 'Member User Update' WHERE `id` = 2319 AND `name` = '会员用户更新';
UPDATE `system_menu` SET `name` = 'Member Tag' WHERE `id` = 2320 AND `name` = '会员标签';
UPDATE `system_menu` SET `name` = 'Member Tag Query' WHERE `id` = 2321 AND `name` = '会员标签查询';
UPDATE `system_menu` SET `name` = 'Member Tag Create' WHERE `id` = 2322 AND `name` = '会员标签创建';
UPDATE `system_menu` SET `name` = 'Member Tag Update' WHERE `id` = 2323 AND `name` = '会员标签更新';
UPDATE `system_menu` SET `name` = 'Member Tag Delete' WHERE `id` = 2324 AND `name` = '会员标签删除';
UPDATE `system_menu` SET `name` = 'Member Level' WHERE `id` = 2325 AND `name` = '会员等级';
UPDATE `system_menu` SET `name` = 'Member Level Query' WHERE `id` = 2326 AND `name` = '会员等级查询';
UPDATE `system_menu` SET `name` = 'Member Level Create' WHERE `id` = 2327 AND `name` = '会员等级创建';
UPDATE `system_menu` SET `name` = 'Member Level Update' WHERE `id` = 2328 AND `name` = '会员等级更新';
UPDATE `system_menu` SET `name` = 'Member Level Delete' WHERE `id` = 2329 AND `name` = '会员等级删除';
UPDATE `system_menu` SET `name` = 'Member Group' WHERE `id` = 2330 AND `name` = '会员分组';
UPDATE `system_menu` SET `name` = 'User Group Query' WHERE `id` = 2331 AND `name` = '用户分组查询';
UPDATE `system_menu` SET `name` = 'User Group Create' WHERE `id` = 2332 AND `name` = '用户分组创建';
UPDATE `system_menu` SET `name` = 'User Group Update' WHERE `id` = 2333 AND `name` = '用户分组更新';
UPDATE `system_menu` SET `name` = 'User Group Delete' WHERE `id` = 2334 AND `name` = '用户分组删除';

-- Mall Decoration
UPDATE `system_menu` SET `name` = 'Mall Decoration' WHERE `id` = 2435 AND `name` = '商城装修';

-- Additional Menu Items
UPDATE `system_menu` SET `name` = 'Process Monitor' WHERE `id` = 2740 AND `name` = '监控中心';
UPDATE `system_menu` SET `name` = 'Message Center' WHERE `id` = 2739 AND `name` = '消息中心';

-- Note: Some menu items may use parent_id references that need to be verified
-- This script should be run after backing up the original data