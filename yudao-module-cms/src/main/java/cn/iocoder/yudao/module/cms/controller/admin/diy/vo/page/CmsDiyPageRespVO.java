package cn.iocoder.yudao.module.cms.controller.admin.diy.vo.page;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * CMS DIY页面 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - CMS DIY页面 Response VO")
@Data
public class CmsDiyPageRespVO {

    @Schema(description = "页面ID", example = "1024")
    private Long id;

    @Schema(description = "页面UUID", example = "550e8400-e29b-41d4-a716-************")
    private String uuid;

    @Schema(description = "页面名称", example = "首页")
    private String name;

    @Schema(description = "关联菜单ID", example = "1024")
    private Long menuId;

    @Schema(description = "关联菜单名称", example = "首页菜单")
    private String menuName;

    @Schema(description = "上级页面ID", example = "1025")
    private Long parentId;

    @Schema(description = "上级页面名称", example = "主页")
    private String parentName;

    @Schema(description = "页面路径", example = "/home/<USER>")
    private String path;

    @Schema(description = "关键词", example = "首页,主页")
    private String keywords;

    @Schema(description = "页面描述", example = "这是网站首页")
    private String description;

    @Schema(description = "状态", example = "1")
    private Integer status;

    @Schema(description = "状态名称", example = "已发布")
    private String statusName;

    @Schema(description = "当前版本号", example = "3")
    private Integer version;

    @Schema(description = "已发布版本号", example = "2")
    private Integer publishedVersion;

    @Schema(description = "页面内容JSON")
    private String content;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}