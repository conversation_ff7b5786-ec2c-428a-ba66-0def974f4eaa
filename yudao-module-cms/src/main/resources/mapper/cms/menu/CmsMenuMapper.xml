<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.cms.dal.mysql.menu.CmsMenuMapper">

    <!-- 递归查询菜单路径 - 使用变量方式避免CTE序列化问题 -->
    <select id="selectFullPathById" resultType="java.lang.String">
        SELECT GROUP_CONCAT(path ORDER BY level SEPARATOR '/') as full_path
        FROM (
            SELECT @r AS _id,
                   (SELECT @r := parent_id FROM cms_menu WHERE id = _id AND deleted = 0) AS parent_id,
                   @l := @l + 1 AS level,
                   path
            FROM (SELECT @r := #{menuId}, @l := 0) vars, cms_menu h
            WHERE @r &lt;&gt; 0 AND deleted = 0
        ) T1
        WHERE _id IS NOT NULL
    </select>

    <!-- 查询菜单的所有子菜单ID（递归） -->
    <select id="selectChildrenIds" resultType="java.lang.Long">
        WITH RECURSIVE menu_children AS (
            SELECT id FROM cms_menu WHERE parent_id = #{parentId} AND deleted = 0
            UNION ALL
            SELECT m.id FROM cms_menu m
            INNER JOIN menu_children mc ON m.parent_id = mc.id
            WHERE m.deleted = 0
        )
        SELECT id FROM menu_children
    </select>

    <!-- 批量查询菜单的父菜单路径 -->
    <select id="selectHierarchyByIds" resultType="cn.iocoder.yudao.module.cms.dal.dataobject.menu.CmsMenuDO">
        WITH RECURSIVE menu_hierarchy AS (
            SELECT id, parent_id, path, name, icon, sort, status, 
                   creator, create_time, updater, update_time, deleted, tenant_id, 0 as level
            FROM cms_menu
            WHERE id IN
            <foreach collection="menuIds" item="menuId" open="(" separator="," close=")">
                #{menuId}
            </foreach>
            AND deleted = 0
            UNION ALL
            SELECT m.id, m.parent_id, m.path, m.name, m.icon, m.sort, m.status,
                   m.creator, m.create_time, m.updater, m.update_time, m.deleted, m.tenant_id, mh.level + 1
            FROM cms_menu m
            INNER JOIN menu_hierarchy mh ON m.id = mh.parent_id
            WHERE m.deleted = 0 AND mh.level &lt; 10
        )
        SELECT * FROM menu_hierarchy ORDER BY id, level DESC
    </select>

    <!-- 批量更新子菜单的物化路径 -->
    <update id="updateChildrenMaterializedPaths">
        UPDATE cms_menu 
        SET materialized_path = REPLACE(materialized_path, #{oldParentPath}, #{newParentPath})
        WHERE parent_id = #{parentId} AND deleted = 0
          AND materialized_path LIKE CONCAT(#{oldParentPath}, '%')
    </update>

</mapper>