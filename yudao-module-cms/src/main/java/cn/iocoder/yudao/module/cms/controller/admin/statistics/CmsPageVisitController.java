package cn.iocoder.yudao.module.cms.controller.admin.statistics;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.statistics.vo.CmsPageVisitLogPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.statistics.vo.CmsPageVisitStatsPageReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.statistics.CmsPageVisitLogDO;
import cn.iocoder.yudao.module.cms.dal.dataobject.statistics.CmsPageVisitStatsDO;
import cn.iocoder.yudao.module.cms.service.statistics.CmsPageVisitService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * CMS页面访问统计 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - CMS页面访问统计")
@RestController
@RequestMapping("/cms/page-visit")
@Validated
@Slf4j
public class CmsPageVisitController {

    @Resource
    private CmsPageVisitService pageVisitService;

    @GetMapping("/log/page")
    @Operation(summary = "获得访问日志分页")
    @PreAuthorize("@ss.hasPermission('cms:page-visit:query')")
    public CommonResult<PageResult<CmsPageVisitLogDO>> getVisitLogPage(@Valid CmsPageVisitLogPageReqVO pageVO) {
        PageResult<CmsPageVisitLogDO> pageResult = pageVisitService.getVisitLogPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/stats/page")
    @Operation(summary = "获得访问统计分页")
    @PreAuthorize("@ss.hasPermission('cms:page-visit:query')")
    public CommonResult<PageResult<CmsPageVisitStatsDO>> getVisitStatsPage(@Valid CmsPageVisitStatsPageReqVO pageVO) {
        PageResult<CmsPageVisitStatsDO> pageResult = pageVisitService.getVisitStatsPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/stats/page-range")
    @Operation(summary = "获得页面指定时间范围的访问统计")
    @Parameter(name = "pageId", description = "页面ID", required = true)
    @Parameter(name = "startDate", description = "开始日期", required = true)
    @Parameter(name = "endDate", description = "结束日期", required = true)
    @PreAuthorize("@ss.hasPermission('cms:page-visit:query')")
    public CommonResult<List<CmsPageVisitStatsDO>> getPageVisitStats(
            @RequestParam("pageId") Long pageId,
            @RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        List<CmsPageVisitStatsDO> stats = pageVisitService.getPageVisitStats(pageId, startDate, endDate);
        return success(stats);
    }

    @GetMapping("/stats/hot-pages")
    @Operation(summary = "获得热门页面")
    @Parameter(name = "days", description = "统计天数", required = false)
    @Parameter(name = "limit", description = "返回数量限制", required = false)
    @PreAuthorize("@ss.hasPermission('cms:page-visit:query')")
    public CommonResult<List<CmsPageVisitStatsDO>> getHotPages(
            @RequestParam(value = "days", required = false) Integer days,
            @RequestParam(value = "limit", required = false) Integer limit) {
        List<CmsPageVisitStatsDO> hotPages = pageVisitService.getHotPages(days, limit);
        return success(hotPages);
    }

    @GetMapping("/stats/today-count")
    @Operation(summary = "获得页面今日访问量")
    @Parameter(name = "pageId", description = "页面ID", required = true)
    @PreAuthorize("@ss.hasPermission('cms:page-visit:query')")
    public CommonResult<Integer> getTodayVisitCount(@RequestParam("pageId") Long pageId) {
        Integer count = pageVisitService.getTodayVisitCount(pageId);
        return success(count);
    }

    @GetMapping("/stats/total-count")
    @Operation(summary = "获得页面总访问量")
    @Parameter(name = "pageId", description = "页面ID", required = true)
    @PreAuthorize("@ss.hasPermission('cms:page-visit:query')")
    public CommonResult<Long> getTotalVisitCount(@RequestParam("pageId") Long pageId) {
        Long count = pageVisitService.getTotalVisitCount(pageId);
        return success(count);
    }

    @GetMapping("/stats/trend")
    @Operation(summary = "获得页面访问趋势")
    @Parameter(name = "pageId", description = "页面ID", required = true)
    @Parameter(name = "days", description = "天数", required = false)
    @PreAuthorize("@ss.hasPermission('cms:page-visit:query')")
    public CommonResult<List<Object[]>> getPageVisitTrend(@RequestParam("pageId") Long pageId,
                                                          @RequestParam(value = "days", required = false) Integer days) {
        List<Object[]> trend = pageVisitService.getPageVisitTrend(pageId, days);
        return success(trend);
    }

    @GetMapping("/stats/realtime")
    @Operation(summary = "获得实时访问统计")
    @PreAuthorize("@ss.hasPermission('cms:page-visit:query')")
    public CommonResult<Object> getRealTimeStats() {
        Object stats = pageVisitService.getRealTimeStats();
        return success(stats);
    }

    @PostMapping("/aggregate")
    @Operation(summary = "聚合每日访问统计")
    @Parameter(name = "statDate", description = "统计日期（不传则为昨天）", required = false)
    @PreAuthorize("@ss.hasPermission('cms:page-visit:update')")
    public CommonResult<Integer> aggregateDailyStats(
            @RequestParam(value = "statDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate statDate) {
        Integer count = pageVisitService.aggregateDailyStats(statDate);
        return success(count);
    }

    @PostMapping("/aggregate-batch")
    @Operation(summary = "批量聚合访问统计")
    @Parameter(name = "startDate", description = "开始日期", required = true)
    @Parameter(name = "endDate", description = "结束日期", required = true)
    @PreAuthorize("@ss.hasPermission('cms:page-visit:update')")
    public CommonResult<Integer> batchAggregateStats(
            @RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        Integer count = pageVisitService.batchAggregateStats(startDate, endDate);
        return success(count);
    }

    @PostMapping("/cleanup")
    @Operation(summary = "清理旧的访问日志")
    @Parameter(name = "keepDays", description = "保留天数", required = false)
    @PreAuthorize("@ss.hasPermission('cms:page-visit:delete')")
    public CommonResult<Integer> cleanupOldVisitLogs(@RequestParam(value = "keepDays", required = false) Integer keepDays) {
        Integer count = pageVisitService.cleanupOldVisitLogs(keepDays);
        return success(count);
    }
}