package cn.iocoder.yudao.module.cms.controller.admin.car.vo.api;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotNull;

/**
 * API SQL配置 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class ApiSqlConfigBaseVO {

    @Schema(description = "API标识码", requiredMode = Schema.RequiredMode.REQUIRED, example = "car_models_list")
    @NotNull(message = "API标识码不能为空")
    @Length(max = 100, message = "API标识码长度不能超过100个字符")
    private String apiCode;

    @Schema(description = "API版本", example = "v1.0")
    @Length(max = 20, message = "API版本长度不能超过20个字符")
    private String version;

    @Schema(description = "SQL查询语句", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "SQL查询语句不能为空")
    private String sqlContent;

    @Schema(description = "响应处理器类名", example = "DefaultResponseProcessor")
    @Length(max = 100, message = "响应处理器类名长度不能超过100个字符")
    private String responseProcessor;

    @Schema(description = "配置描述", example = "获取车型列表的API配置")
    private String description;

    @Schema(description = "是否默认版本", example = "true")
    private Boolean isDefault;

    @Schema(description = "状态：0-启用，1-禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "状态不能为空")
    private Integer status;

}