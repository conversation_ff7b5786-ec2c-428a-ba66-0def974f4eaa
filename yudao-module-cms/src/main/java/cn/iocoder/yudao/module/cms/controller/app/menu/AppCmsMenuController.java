package cn.iocoder.yudao.module.cms.controller.app.menu;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.controller.admin.menu.vo.CmsMenuRespVO;
import cn.iocoder.yudao.module.cms.service.menu.CmsMenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * App端 - CMS菜单 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "App端 - CMS菜单")
@RestController
@RequestMapping("/app/cms/menu")
@Validated
@Slf4j
public class AppCmsMenuController {

    @Resource
    private CmsMenuService menuService;

    @GetMapping("/tree")
    @Operation(summary = "获得启用菜单树")
    @PermitAll
    public CommonResult<List<CmsMenuRespVO>> getEnabledMenuTree() {
        List<CmsMenuRespVO> tree = menuService.getEnabledMenuTree();
        return success(tree);
    }

    @GetMapping("/by-path")
    @Operation(summary = "根据路径获得菜单")
    @Parameter(name = "path", description = "菜单路径", required = true, example = "/home")
    @PermitAll
    public CommonResult<CmsMenuRespVO> getMenuByPath(@RequestParam("path") String path) {
        return success(BeanUtils.toBean(menuService.getMenuByPath(path), CmsMenuRespVO.class));
    }
}