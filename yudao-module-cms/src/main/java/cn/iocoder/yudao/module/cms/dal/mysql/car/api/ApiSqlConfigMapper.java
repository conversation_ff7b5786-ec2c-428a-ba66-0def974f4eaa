package cn.iocoder.yudao.module.cms.dal.mysql.car.api;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.api.ApiSqlConfigDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * API SQL配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ApiSqlConfigMapper extends BaseMapperX<ApiSqlConfigDO> {

    default PageResult<ApiSqlConfigDO> selectPage(String apiCode, String version, Integer status,
                                                   Integer pageNo, Integer pageSize) {
        return selectPage(new PageParam().setPageNo(pageNo).setPageSize(pageSize),new LambdaQueryWrapperX<ApiSqlConfigDO>()
                .likeIfPresent(ApiSqlConfigDO::getApiCode, apiCode)
                .eqIfPresent(ApiSqlConfigDO::getVersion, version)
                .eqIfPresent(ApiSqlConfigDO::getStatus, status)
                .orderByDesc(ApiSqlConfigDO::getIsDefault)
                .orderByDesc(ApiSqlConfigDO::getCreateTime));
    }

    default ApiSqlConfigDO selectByApiCodeAndVersion(String apiCode, String version) {
        return selectOne(new LambdaQueryWrapperX<ApiSqlConfigDO>()
                .eq(ApiSqlConfigDO::getApiCode, apiCode)
                .eq(ApiSqlConfigDO::getVersion, version)
                .eq(ApiSqlConfigDO::getStatus, 0));
    }

    default ApiSqlConfigDO selectDefaultByApiCode(String apiCode) {
        return selectOne(new LambdaQueryWrapperX<ApiSqlConfigDO>()
                .eq(ApiSqlConfigDO::getApiCode, apiCode)
                .eq(ApiSqlConfigDO::getIsDefault, true)
                .eq(ApiSqlConfigDO::getStatus, 0));
    }

    default List<ApiSqlConfigDO> selectListByApiCode(String apiCode) {
        return selectList(new LambdaQueryWrapperX<ApiSqlConfigDO>()
                .eq(ApiSqlConfigDO::getApiCode, apiCode)
                .eq(ApiSqlConfigDO::getStatus, 0)
                .orderByDesc(ApiSqlConfigDO::getIsDefault)
                .orderByAsc(ApiSqlConfigDO::getVersion));
    }

    default List<ApiSqlConfigDO> selectListByStatus(Integer status) {
        return selectList(new LambdaQueryWrapperX<ApiSqlConfigDO>()
                .eqIfPresent(ApiSqlConfigDO::getStatus, status)
                .orderByAsc(ApiSqlConfigDO::getApiCode)
                .orderByDesc(ApiSqlConfigDO::getIsDefault)
                .orderByAsc(ApiSqlConfigDO::getVersion));
    }

    default List<String> selectDistinctApiCodes() {
        return selectObjs(new LambdaQueryWrapperX<ApiSqlConfigDO>()
                .select(ApiSqlConfigDO::getApiCode)
                .eq(ApiSqlConfigDO::getStatus, 0)
                .groupBy(ApiSqlConfigDO::getApiCode)
                .orderByAsc(ApiSqlConfigDO::getApiCode));
    }

    default ApiSqlConfigDO selectByApiCodeAndIsDefault(String apiCode, Boolean isDefault) {
        return selectOne(new LambdaQueryWrapperX<ApiSqlConfigDO>()
                .eq(ApiSqlConfigDO::getApiCode, apiCode)
                .eq(ApiSqlConfigDO::getIsDefault, isDefault)
                .eq(ApiSqlConfigDO::getStatus, 0));
    }

    default Long selectCountByStatus(Integer status) {
        return selectCount(new LambdaQueryWrapperX<ApiSqlConfigDO>()
                .eq(ApiSqlConfigDO::getStatus, status));
    }

}