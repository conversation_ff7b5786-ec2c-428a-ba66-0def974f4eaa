package cn.iocoder.yudao.module.cms.controller.admin.dealer;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.dealer.vo.DealerCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.dealer.vo.DealerPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.dealer.vo.DealerRespVO;
import cn.iocoder.yudao.module.cms.controller.admin.dealer.vo.DealerUpdateReqVO;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.dal.dataobject.dealer.DealerDO;
import cn.iocoder.yudao.module.cms.dal.dataobject.dealer.DealerWithDistanceDO;
import cn.iocoder.yudao.module.cms.service.dealer.DealerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - CMS经销商")
@RestController
@RequestMapping("/cms/dealer")
@Validated
public class DealerController {

    @Resource
    private DealerService dealerService;

    @PostMapping("/create")
    @Operation(summary = "创建经销商")
    @PreAuthorize("@ss.hasPermission('cms:dealer:create')")
    public CommonResult<Long> createDealer(@Valid @RequestBody DealerCreateReqVO createReqVO) {
        return success(dealerService.createDealer(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新经销商")
    @PreAuthorize("@ss.hasPermission('cms:dealer:update')")
    public CommonResult<Boolean> updateDealer(@Valid @RequestBody DealerUpdateReqVO updateReqVO) {
        dealerService.updateDealer(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除经销商")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('cms:dealer:delete')")
    public CommonResult<Boolean> deleteDealer(@RequestParam("id") Long id) {
        dealerService.deleteDealer(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得经销商详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('cms:dealer:query')")
    public CommonResult<DealerRespVO> getDealer(@RequestParam("id") Long id) {
        DealerDO dealer = dealerService.getDealer(id);
        return success(BeanUtils.toBean(dealer, DealerRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得经销商分页")
    @PreAuthorize("@ss.hasPermission('cms:dealer:query')")
    public CommonResult<PageResult<DealerRespVO>> getDealerPage(@Valid DealerPageReqVO pageVO) {
        PageResult<DealerDO> pageResult = dealerService.getDealerPage(pageVO);
        return success(BeanUtils.toBean(pageResult, DealerRespVO.class));
    }

    @GetMapping("/nearby")
    @Operation(summary = "查询附近经销商")
    @PreAuthorize("@ss.hasPermission('cms:dealer:query')")
    public CommonResult<List<DealerWithDistanceDO>> getNearbyDealers(@RequestParam Double lat,
                                                             @RequestParam Double lng,
                                                             @RequestParam(defaultValue = "50") Integer radius) {
        List<DealerWithDistanceDO> dealers = dealerService.getNearbyDealers(lat, lng, radius);
        return success(dealers);
    }

}