package cn.iocoder.yudao.module.cms.controller.admin.car.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;

@Schema(description = "管理后台 - 配置包更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CarPackageUpdateReqVO extends CarPackageBaseVO {

    @Schema(description = "配置包ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "配置包ID不能为空")
    private Long id;

}