package cn.iocoder.yudao.module.cms.controller.admin.car;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelRespVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelSimpleRespVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelUpdateReqVO;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarModelDO;
import cn.iocoder.yudao.module.cms.service.car.CarModelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 车型管理")
@RestController
@RequestMapping("/cms/car/model")
@Validated
public class CarModelController {

    @Resource
    private CarModelService carModelService;

    @PostMapping("/create")
    @Operation(summary = "创建车型")
    @PreAuthorize("@ss.hasPermission('cms:car-model:create')")
    public CommonResult<Long> createCarModel(@Valid @RequestBody CarModelCreateReqVO createReqVO) {
        return success(carModelService.createCarModel(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新车型")
    @PreAuthorize("@ss.hasPermission('cms:car-model:update')")
    public CommonResult<Boolean> updateCarModel(@Valid @RequestBody CarModelUpdateReqVO updateReqVO) {
        carModelService.updateCarModel(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除车型")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('cms:car-model:delete')")
    public CommonResult<Boolean> deleteCarModel(@RequestParam("id") Long id) {
        carModelService.deleteCarModel(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得车型详情")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('cms:car-model:query')")
    public CommonResult<CarModelRespVO> getCarModel(@RequestParam("id") Long id) {
        CarModelDO carModel = carModelService.getCarModel(id);
        return success(BeanUtils.toBean(carModel, CarModelRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得车型分页")
    @PreAuthorize("@ss.hasPermission('cms:car-model:query')")
    public CommonResult<PageResult<CarModelRespVO>> getCarModelPage(@Valid CarModelPageReqVO pageReqVO) {
        PageResult<CarModelDO> pageResult = carModelService.getCarModelPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CarModelRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得车型列表")
    @Parameter(name = "seriesId", description = "车系ID", example = "1024")
    @Parameter(name = "status", description = "状态", example = "1")
    @PreAuthorize("@ss.hasPermission('cms:car-model:query')")
    public CommonResult<List<CarModelRespVO>> getCarModelList(@RequestParam(value = "seriesId", required = false) Long seriesId,
                                                             @RequestParam(value = "status", required = false) Integer status) {
        List<CarModelDO> list = carModelService.getCarModelList(seriesId, status);
        return success(BeanUtils.toBean(list, CarModelRespVO.class));
    }

    @GetMapping("/get-by-code")
    @Operation(summary = "根据编码获取车型")
    @Parameter(name = "code", description = "车型编码", required = true, example = "TIGGO8_PRO_1_5T")
    @PreAuthorize("@ss.hasPermission('cms:car-model:query')")
    public CommonResult<CarModelRespVO> getCarModelByCode(@RequestParam("code") String code) {
        CarModelDO carModel = carModelService.getCarModelByCode(code);
        return success(BeanUtils.toBean(carModel, CarModelRespVO.class));
    }

    @GetMapping("/list-by-price-range")
    @Operation(summary = "获得价格区间内的车型列表")
    @Parameter(name = "minPrice", description = "最小价格", example = "100000.00")
    @Parameter(name = "maxPrice", description = "最大价格", example = "200000.00")
    @Parameter(name = "status", description = "状态", example = "1")
    @PreAuthorize("@ss.hasPermission('cms:car-model:query')")
    public CommonResult<List<CarModelRespVO>> getCarModelListByPriceRange(@RequestParam(value = "minPrice", required = false) BigDecimal minPrice,
                                                                         @RequestParam(value = "maxPrice", required = false) BigDecimal maxPrice,
                                                                         @RequestParam(value = "status", required = false) Integer status) {
        List<CarModelDO> list = carModelService.getCarModelListByPriceRange(minPrice, maxPrice, status);
        return success(BeanUtils.toBean(list, CarModelRespVO.class));
    }

    @GetMapping("/hot-list")
    @Operation(summary = "获得热销车型列表")
    @Parameter(name = "limit", description = "限制数量", example = "10")
    @PreAuthorize("@ss.hasPermission('cms:car-model:query')")
    public CommonResult<List<CarModelRespVO>> getHotCarModelList(@RequestParam(value = "limit", required = false) Integer limit) {
        List<CarModelDO> list = carModelService.getHotCarModelList(limit);
        return success(BeanUtils.toBean(list, CarModelRespVO.class));
    }

    @GetMapping("/simple-list")
    @Operation(summary = "获得车型精简列表")
    @PreAuthorize("@ss.hasPermission('cms:car-model:query')")
    public CommonResult<List<CarModelSimpleRespVO>> getCarModelSimpleList() {
        List<CarModelDO> list = carModelService.getCarModelSimpleList();
        return success(BeanUtils.toBean(list, CarModelSimpleRespVO.class));
    }

}