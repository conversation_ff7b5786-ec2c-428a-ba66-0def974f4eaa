package cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 融资期限分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FinanceTermPageReqVO extends PageParam {

    @Schema(description = "融资选项ID", example = "1024")
    private Long optionId;

    @Schema(description = "期限名称", example = "36个月")
    private String name;

    @Schema(description = "状态：0-禁用，1-启用", example = "1")
    private Integer status;

}