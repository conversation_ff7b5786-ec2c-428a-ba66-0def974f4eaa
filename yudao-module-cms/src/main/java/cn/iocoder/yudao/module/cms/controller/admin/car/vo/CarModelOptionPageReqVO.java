package cn.iocoder.yudao.module.cms.controller.admin.car.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 车型配置选项分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CarModelOptionPageReqVO extends PageParam {

    @Schema(description = "车型ID", example = "1024")
    private Long modelId;

    @Schema(description = "选项类型ID", example = "1024")
    private Long optionTypeId;

    @Schema(description = "选项名称", example = "红色")
    private String name;

    @Schema(description = "状态：0-禁用，1-启用", example = "1")
    private Integer status;

}