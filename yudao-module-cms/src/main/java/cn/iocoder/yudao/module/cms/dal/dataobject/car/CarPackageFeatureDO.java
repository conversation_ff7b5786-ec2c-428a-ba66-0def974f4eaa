package cn.iocoder.yudao.module.cms.dal.dataobject.car;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

/**
 * 配置包特性 DO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CarPackageFeatureDO {

    /**
     * 特性名称
     */
    @JsonProperty("name")
    private String name;

    /**
     * 是否包含
     */
    @JsonProperty("included")
    private Boolean included;

}