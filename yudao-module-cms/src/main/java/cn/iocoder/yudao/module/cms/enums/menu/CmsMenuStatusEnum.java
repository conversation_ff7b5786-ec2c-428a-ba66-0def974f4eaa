package cn.iocoder.yudao.module.cms.enums.menu;

import cn.iocoder.yudao.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * CMS菜单状态枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum CmsMenuStatusEnum implements ArrayValuable<Integer> {

    ENABLE(0, "启用"),
    DISABLE(1, "禁用");

    public static final Integer[] ARRAYS = Arrays.stream(values()).map(CmsMenuStatusEnum::getStatus).toArray(Integer[]::new);

    /**
     * 状态值
     */
    private final Integer status;

    /**
     * 状态名
     */
    private final String name;

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

}