package cn.iocoder.yudao.module.cms.dal.dataobject.car;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.type.JsonMapTypeHandler;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Map;

/**
 * 车型规格 DO
 *
 * <AUTHOR>
 */
@TableName(value = "cms_car_model_specs", autoResultMap = true)
@KeySequence("cms_car_model_specs_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CarModelSpecDO extends BaseDO {

    /**
     * 规格ID
     */
    @TableId
    private Long id;

    /**
     * 车型ID
     */
    private Long modelId;

    /**
     * 发动机信息
     */
    private String engine;

    /**
     * 发动机类型
     */
    private String engineType;

    /**
     * 燃油消耗
     */
    private String fuelConsumption;

    /**
     * 功率
     */
    private String power;

    /**
     * 扭矩
     */
    private String torque;

    /**
     * 座位数
     */
    private String seatsNum;

    /**
     * 座位数单位
     */
    private String seatsUnit;

    /**
     * 车身尺寸信息
     *
     * 包含：长宽高、轴距等
     */
    @TableField(typeHandler = JsonMapTypeHandler.class)
    private Map<String, Object> dimensions;

    /**
     * 性能参数
     *
     * 包含：加速、最高速度等
     */
    @TableField(typeHandler = JsonMapTypeHandler.class)
    private Map<String, Object> performance;

    /**
     * 安全评级
     */
    private String safetyRating;

    /**
     * 质保信息
     */
    private String warrantyInfo;

}