# CMS 菜单和 DIY 页面管理功能详细设计文档 v1.0

## 1. 概述

### 1.1 项目背景

基于现有的 yudao-vue-pro 项目架构，在 yudao-module-cms 模块中新增菜单管理和 DIY 页面管理功能，支持页面的草稿、发布、版本管理等核心功能。

### 1.2 设计目标

- 提供完整的菜单管理功能，支持层级结构
- 实现 DIY 页面的全生命周期管理（草稿→发布→版本历史）
- 支持页面路径的自动生成和唯一性约束
- 提供版本控制和历史记录查询功能

### 1.3 技术架构

- 基于 Spring Boot + MyBatis Plus
- 遵循 yudao-vue-pro 项目的分层架构
- 使用 MySQL 作为数据存储
- 支持多租户架构

## 2. 数据库设计

### 2.1 CMS 菜单表 (cms_menu)

```sql
CREATE TABLE `cms_menu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `name` varchar(50) NOT NULL COMMENT '菜单名称',
  `icon` varchar(100) DEFAULT NULL COMMENT '菜单图标',
  `path` varchar(200) NOT NULL COMMENT '菜单路径',
  `parent_id` bigint DEFAULT 0 COMMENT '上级菜单ID，0表示根菜单',
  `sort` int DEFAULT 0 COMMENT '显示顺序',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态：0-启用，1-禁用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_path` (`path`, `deleted`, `tenant_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CMS菜单表';
```

### 2.2 CMS DIY 页面表 (cms_diy_page)

```sql
CREATE TABLE `cms_diy_page` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '页面ID',
  `uuid` varchar(36) NOT NULL COMMENT '页面UUID，全局唯一',
  `name` varchar(100) NOT NULL COMMENT '页面名称',
  `parent_id` bigint DEFAULT NULL COMMENT '上级页面ID',
  `menu_id` bigint NOT NULL COMMENT '关联菜单ID',
  `path` varchar(500) NOT NULL COMMENT '页面路径，唯一',
  `keywords` varchar(200) DEFAULT NULL COMMENT '关键词',
  `description` text COMMENT '页面描述',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态：0-草稿，1-已发布，2-已下线',
  `version` int NOT NULL DEFAULT 1 COMMENT '当前版本号，用于乐观锁',
  `published_version` int DEFAULT NULL COMMENT '已发布的版本号',
  `content` longtext COMMENT '页面内容JSON',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_uuid` (`uuid`, `deleted`, `tenant_id`),
  UNIQUE KEY `uk_path` (`path`, `deleted`, `tenant_id`),
  KEY `idx_menu_id` (`menu_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CMS DIY页面表';
```

### 2.3 CMS DIY 页面版本表 (cms_diy_page_version)

```sql
CREATE TABLE `cms_diy_page_version` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '版本ID',
  `page_id` bigint NOT NULL COMMENT '页面ID',
  `version` int NOT NULL COMMENT '版本号',
  `name` varchar(100) NOT NULL COMMENT '版本名称',
  `content` longtext COMMENT '页面内容JSON',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `is_published` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已发布',
  `remark` varchar(500) DEFAULT NULL COMMENT '版本备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_page_version` (`page_id`, `version`, `deleted`, `tenant_id`),
  KEY `idx_page_id` (`page_id`),
  KEY `idx_publish_time` (`publish_time`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CMS DIY页面版本表';
```

### 2.4 CMS 页面访问统计表 (cms_page_visit_log)

```sql
CREATE TABLE `cms_page_visit_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '访问记录ID',
  `page_id` bigint NOT NULL COMMENT '页面ID',
  `page_uuid` varchar(36) NOT NULL COMMENT '页面UUID',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID（登录用户）',
  `session_id` varchar(64) DEFAULT NULL COMMENT '会话ID',
  `ip` varchar(45) DEFAULT NULL COMMENT '访问IP',
  `user_agent` text COMMENT '用户代理',
  `referer` varchar(500) DEFAULT NULL COMMENT '来源页面',
  `visit_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
  `stay_time` int DEFAULT 0 COMMENT '停留时间（秒）',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_page_id` (`page_id`),
  KEY `idx_page_uuid` (`page_uuid`),
  KEY `idx_visit_time` (`visit_time`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CMS页面访问统计表';
```

### 2.5 CMS 页面访问统计汇总表 (cms_page_visit_stats)

```sql
CREATE TABLE `cms_page_visit_stats` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `page_id` bigint NOT NULL COMMENT '页面ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `total_visits` int NOT NULL DEFAULT 0 COMMENT '总访问次数',
  `unique_visitors` int NOT NULL DEFAULT 0 COMMENT '独立访客数',
  `avg_stay_time` int NOT NULL DEFAULT 0 COMMENT '平均停留时间（秒）',
  `bounce_count` int NOT NULL DEFAULT 0 COMMENT '跳出次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_page_date` (`page_id`, `stat_date`, `tenant_id`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CMS页面访问统计汇总表';
```

## 3. 系统架构设计

### 3.1 模块结构

```text
yudao-module-cms/
├── src/main/java/cn/iocoder/yudao/module/cms/
│   ├── controller/admin/
│   │   ├── menu/
│   │   │   └── CmsMenuController.java
│   │   └── diypage/
│   │       ├── CmsDiyPageController.java
│   │       └── CmsDiyPageVersionController.java
│   ├── controller/app/
│   │   ├── menu/
│   │   │   └── AppCmsMenuController.java
│   │   └── diypage/
│   │       ├── AppCmsDiyPageController.java
│   │       └── AppCmsDiyPageStatisticsController.java
│   ├── service/
│   │   ├── menu/
│   │   │   ├── CmsMenuService.java
│   │   │   └── CmsMenuServiceImpl.java
│   │   └── diypage/
│   │       ├── CmsDiyPageService.java
│   │       ├── CmsDiyPageServiceImpl.java
│   │       ├── CmsDiyPageVersionService.java
│   │       └── CmsDiyPageVersionServiceImpl.java
│   ├── dal/
│   │   ├── dataobject/
│   │   │   ├── menu/
│   │   │   │   └── CmsMenuDO.java
│   │   │   └── diypage/
│   │   │       ├── CmsDiyPageDO.java
│   │   │       └── CmsDiyPageVersionDO.java
│   │   └── mysql/
│   │       ├── menu/
│   │       │   └── CmsMenuMapper.java
│   │       └── diypage/
│   │           ├── CmsDiyPageMapper.java
│   │           └── CmsDiyPageVersionMapper.java
│   ├── convert/
│   │   ├── menu/
│   │   │   └── CmsMenuConvert.java
│   │   └── diypage/
│   │       ├── CmsDiyPageConvert.java
│   │       └── CmsDiyPageVersionConvert.java
│   ├── enums/
│   │   ├── menu/
│   │   │   └── CmsMenuStatusEnum.java
│   │   └── diypage/
│   │       └── CmsDiyPageStatusEnum.java
│   ├── cache/
│   │   ├── CmsCacheService.java
│   │   ├── CmsCacheConfig.java
│   │   ├── CmsCacheAspect.java
│   │   ├── CmsCacheWarmupService.java
│   │   └── CmsCacheMonitor.java
│   └── async/
│       └── CmsAsyncConfig.java
```

### 3.2 核心枚举定义

#### 3.2.1 菜单状态枚举

```java
public enum CmsMenuStatusEnum implements IntArrayValuable {
    ENABLE(0, "启用"),
    DISABLE(1, "禁用");
}
```

#### 3.2.2 页面状态枚举

```java
public enum CmsDiyPageStatusEnum implements IntArrayValuable {
    DRAFT(0, "草稿"),
    PUBLISHED(1, "已发布"),
    OFFLINE(2, "已下线");
}
```

## 4. 核心功能设计

### 4.1 菜单管理功能

#### 4.1.1 功能特性

- 支持无限层级菜单结构
- 菜单路径唯一性约束
- 菜单状态管理（启用/禁用）
- 菜单排序功能

#### 4.1.2 核心接口

```java
public interface CmsMenuService {
    // 创建菜单
    Long createMenu(CmsMenuSaveReqVO createReqVO);
    
    // 更新菜单
    void updateMenu(CmsMenuSaveReqVO updateReqVO);
    
    // 删除菜单
    void deleteMenu(Long id);
    
    // 获取菜单详情
    CmsMenuDO getMenu(Long id);
    
    // 获取菜单列表
    List<CmsMenuDO> getMenuList(CmsMenuListReqVO listReqVO);
    
    // 获取菜单树
    List<CmsMenuRespVO> getMenuTree(CmsMenuListReqVO listReqVO);
    
    // 构建菜单路径
    String buildMenuPath(Long menuId);
    
    // App端接口
    // 获取启用状态的菜单树
    List<CmsMenuRespVO> getEnabledMenuTree();
    
    // 根据路径获取菜单
    CmsMenuDO getMenuByPath(String path);
    
    // 缓存相关接口
    // 清除菜单缓存
    void clearMenuCache(Long tenantId);
    
    // 预热菜单缓存
    void warmupMenuCache(Long tenantId);
}
```

### 4.2 DIY 页面管理功能

#### 4.2.1 功能特性

- 页面草稿保存和编辑
- 页面发布和版本自动创建
- 页面路径自动生成（菜单路径/上级页面路径/页面路径）
- UUID 全局唯一标识
- 版本历史查询和回滚

#### 4.2.2 核心接口

```java
public interface CmsDiyPageService {
    // 创建页面
    Long createPage(CmsDiyPageSaveReqVO createReqVO);
    
    // 更新页面（草稿）- 使用乐观锁
    void updatePage(CmsDiyPageSaveReqVO updateReqVO);
    
    // 发布页面 - 自动创建版本
    void publishPage(Long id, Integer version, String remark);
    
    // 下线页面
    void offlinePage(Long id, Integer version);
    
    // 删除页面
    void deletePage(Long id);
    
    // 获取页面详情
    CmsDiyPageDO getPage(Long id);
    
    // 根据UUID获取页面
    CmsDiyPageDO getPageByUuid(String uuid);
    
    // 获取页面列表
    PageResult<CmsDiyPageDO> getPagePage(CmsDiyPagePageReqVO pageReqVO);
    
    // 构建页面路径
    String buildPagePath(Long menuId, Long parentId, String pagePath);
    
    // 检查版本冲突
    void checkVersionConflict(Long id, Integer version);
    
    // App端接口
    // 根据路径获取已发布页面
    CmsDiyPageDO getPublishedPageByPath(String path);
    
    // 根据UUID获取已发布页面
    CmsDiyPageDO getPublishedPageByUuid(String uuid);
    
    // 获取菜单下的已发布页面列表
    PageResult<CmsDiyPageDO> getPublishedPagesByMenu(Long menuId, Integer pageNo, Integer pageSize);
    
    // 搜索已发布页面
    PageResult<CmsDiyPageDO> searchPublishedPages(String keyword, Integer pageNo, Integer pageSize);
    
    // 获取页面导航路径
    List<CmsBreadcrumbRespVO> getPageBreadcrumb(Long pageId);
    
    // 记录页面访问
    void recordPageVisit(CmsPageVisitReqVO visitReqVO);
    
    // 缓存相关接口
    // 清除页面缓存
    void clearPageCache(Long pageId);
    
    // 预热页面缓存
    void warmupPageCache(List<Long> pageIds);
    
    // 获取热门页面
    List<CmsDiyPageDO> getHotPages(Integer limit);
}
```

#### 4.2.3 版本管理接口

```java
public interface CmsDiyPageVersionService {
    // 自动创建版本（发布时调用）
    Long createVersionOnPublish(Long pageId, String content, String remark);
    
    // 获取版本列表
    PageResult<CmsDiyPageVersionDO> getVersionPage(CmsDiyPageVersionPageReqVO pageReqVO);
    
    // 获取指定版本内容
    CmsDiyPageVersionDO getVersion(Long pageId, Integer version);
    
    // 获取已发布版本内容
    CmsDiyPageVersionDO getPublishedVersion(Long pageId);
    
    // 回滚到指定版本
    void rollbackToVersion(Long pageId, Integer targetVersion, Integer currentVersion);
    
    // 获取下一个版本号
    Integer getNextVersionNumber(Long pageId);
    
    // 清理历史版本（保留最近N个版本）
    void cleanupOldVersions(Long pageId, Integer keepCount);
}
```

### 4.3 乐观锁版本控制机制

#### 4.3.1 乐观锁实现原理

- 使用 `version` 字段作为乐观锁版本号，每次更新时自动递增
- 更新操作时必须传入当前版本号，系统会检查版本是否匹配
- 如果版本不匹配，说明数据已被其他用户修改，抛出版本冲突异常
- 发布时自动创建版本快照，记录到版本历史表

#### 4.3.2 版本控制流程

```java
// 更新页面时的乐观锁检查
@Override
public void updatePage(CmsDiyPageSaveReqVO updateReqVO) {
    // 1. 检查页面是否存在
    CmsDiyPageDO existPage = validatePageExists(updateReqVO.getId());
    
    // 2. 乐观锁版本检查
    if (!Objects.equals(existPage.getVersion(), updateReqVO.getVersion())) {
        throw exception(CMS_DIY_PAGE_VERSION_CONFLICT);
    }
    
    // 3. 更新数据，版本号自动递增
    CmsDiyPageDO updateObj = CmsDiyPageConvert.INSTANCE.convert(updateReqVO);
    updateObj.setVersion(existPage.getVersion() + 1);
    
    // 4. 执行更新（使用 WHERE 条件包含版本号）
    int updateCount = diyPageMapper.updateByIdAndVersion(updateObj, updateReqVO.getVersion());
    if (updateCount == 0) {
        throw exception(CMS_DIY_PAGE_VERSION_CONFLICT);
    }
}

// 发布页面时自动创建版本
@Override
public void publishPage(Long id, Integer version, String remark) {
    // 1. 乐观锁检查
    CmsDiyPageDO page = validatePageExists(id);
    checkVersionConflict(id, version);
    
    // 2. 创建版本快照
    diyPageVersionService.createVersionOnPublish(id, page.getContent(), remark);
    
    // 3. 更新页面状态和发布版本号
    CmsDiyPageDO updateObj = new CmsDiyPageDO();
    updateObj.setId(id);
    updateObj.setStatus(CmsDiyPageStatusEnum.PUBLISHED.getStatus());
    updateObj.setPublishedVersion(page.getVersion());
    updateObj.setVersion(page.getVersion() + 1);
    
    diyPageMapper.updateByIdAndVersion(updateObj, version);
}
```

#### 4.3.3 版本冲突处理

```java
// 前端处理版本冲突的建议流程
1. 用户编辑页面时，前端记录当前版本号
2. 提交更新时，携带版本号到后端
3. 如果返回版本冲突错误，提示用户：
   - "页面已被其他用户修改，请刷新后重新编辑"
   - 提供"强制覆盖"选项（需要特殊权限）
4. 用户选择刷新后，重新加载最新数据继续编辑
```

### 4.4 页面路径生成规则

页面路径生成规则：`菜单路径/上级页面路径【递归】/页面路径`

#### 4.4.1 路径生成算法

```java
public String buildPagePath(Long menuId, Long parentId, String pagePath) {
    StringBuilder fullPath = new StringBuilder();
    
    // 1. 获取菜单路径
    CmsMenuDO menu = getMenu(menuId);
    if (menu != null) {
        fullPath.append(buildMenuPath(menu.getId()));
    }
    
    // 2. 递归获取上级页面路径
    if (parentId != null && parentId > 0) {
        String parentPath = buildParentPagePath(parentId);
        if (StrUtil.isNotBlank(parentPath)) {
            fullPath.append("/").append(parentPath);
        }
    }
    
    // 3. 添加当前页面路径
    if (StrUtil.isNotBlank(pagePath)) {
        fullPath.append("/").append(pagePath);
    }
    
    return fullPath.toString();
}

private String buildParentPagePath(Long parentId) {
    CmsDiyPageDO parentPage = getPage(parentId);
    if (parentPage == null) {
        return "";
    }
    
    StringBuilder parentPath = new StringBuilder();
    
    // 递归获取上级页面路径
    if (parentPage.getParentId() != null && parentPage.getParentId() > 0) {
        String grandParentPath = buildParentPagePath(parentPage.getParentId());
        if (StrUtil.isNotBlank(grandParentPath)) {
            parentPath.append(grandParentPath).append("/");
        }
    }
    
    // 从完整路径中提取页面路径部分
    String fullPath = parentPage.getPath();
    String[] pathParts = fullPath.split("/");
    if (pathParts.length > 0) {
        parentPath.append(pathParts[pathParts.length - 1]);
    }
    
    return parentPath.toString();
}
```

## 5. 数据访问层设计

### 5.1 乐观锁 Mapper 实现

#### 5.1.1 CmsDiyPageMapper 接口

```java
@Mapper
public interface CmsDiyPageMapper extends BaseMapperX<CmsDiyPageDO> {
    
    /**
     * 根据ID和版本号更新页面（乐观锁）
     * @param updateObj 更新对象
     * @param version 当前版本号
     * @return 影响行数
     */
    int updateByIdAndVersion(@Param("updateObj") CmsDiyPageDO updateObj, @Param("version") Integer version);
    
    /**
     * 根据ID和版本号删除页面（乐观锁）
     * @param id 页面ID
     * @param version 当前版本号
     * @return 影响行数
     */
    int deleteByIdAndVersion(@Param("id") Long id, @Param("version") Integer version);
}
```

#### 5.1.2 MyBatis XML 实现

```xml
<!-- CmsDiyPageMapper.xml -->
<update id="updateByIdAndVersion">
    UPDATE cms_diy_page
    <set>
        <if test="updateObj.name != null">name = #{updateObj.name},</if>
        <if test="updateObj.parentId != null">parent_id = #{updateObj.parentId},</if>
        <if test="updateObj.menuId != null">menu_id = #{updateObj.menuId},</if>
        <if test="updateObj.path != null">path = #{updateObj.path},</if>
        <if test="updateObj.keywords != null">keywords = #{updateObj.keywords},</if>
        <if test="updateObj.description != null">description = #{updateObj.description},</if>
        <if test="updateObj.status != null">status = #{updateObj.status},</if>
        <if test="updateObj.publishedVersion != null">published_version = #{updateObj.publishedVersion},</if>
        <if test="updateObj.content != null">content = #{updateObj.content},</if>
        <if test="updateObj.version != null">version = #{updateObj.version},</if>
        updater = #{updateObj.updater},
        update_time = NOW()
    </set>
    WHERE id = #{updateObj.id} 
      AND version = #{version} 
      AND deleted = 0
</update>

<delete id="deleteByIdAndVersion">
    UPDATE cms_diy_page 
    SET deleted = 1, 
        updater = #{updateObj.updater},
        update_time = NOW()
    WHERE id = #{id} 
      AND version = #{version} 
      AND deleted = 0
</delete>
```

### 5.2 版本管理数据访问

#### 5.2.1 CmsDiyPageVersionMapper 接口

```java
@Mapper
public interface CmsDiyPageVersionMapper extends BaseMapperX<CmsDiyPageVersionDO> {
    
    /**
     * 获取页面的最大版本号
     * @param pageId 页面ID
     * @return 最大版本号
     */
    Integer getMaxVersionByPageId(@Param("pageId") Long pageId);
    
    /**
     * 获取页面的已发布版本
     * @param pageId 页面ID
     * @return 已发布版本
     */
    CmsDiyPageVersionDO getPublishedVersionByPageId(@Param("pageId") Long pageId);
    
    /**
     * 清理旧版本（保留最近N个版本）
     * @param pageId 页面ID
     * @param keepCount 保留数量
     * @return 删除数量
     */
    int cleanupOldVersions(@Param("pageId") Long pageId, @Param("keepCount") Integer keepCount);
}
```

### 5.3 App 端数据访问

#### 5.3.1 CmsPageVisitLogMapper 接口

```java
@Mapper
public interface CmsPageVisitLogMapper extends BaseMapperX<CmsPageVisitLogDO> {
    
    /**
     * 批量插入访问记录
     * @param visitLogs 访问记录列表
     * @return 插入数量
     */
    int batchInsert(@Param("visitLogs") List<CmsPageVisitLogDO> visitLogs);
    
    /**
     * 获取页面访问统计
     * @param pageId 页面ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    CmsPageVisitStatsVO getPageVisitStats(@Param("pageId") Long pageId, 
                                         @Param("startDate") LocalDate startDate, 
                                         @Param("endDate") LocalDate endDate);
}
```

#### 5.3.2 CmsPageVisitStatsMapper 接口

```java
@Mapper
public interface CmsPageVisitStatsMapper extends BaseMapperX<CmsPageVisitStatsDO> {
    
    /**
     * 获取页面每日统计
     * @param pageId 页面ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 每日统计列表
     */
    List<CmsPageVisitStatsDO> getDailyStats(@Param("pageId") Long pageId, 
                                           @Param("startDate") LocalDate startDate, 
                                           @Param("endDate") LocalDate endDate);
    
    /**
     * 更新或插入统计数据
     * @param stats 统计数据
     * @return 影响行数
     */
    int upsertStats(@Param("stats") CmsPageVisitStatsDO stats);
}
```

## 6. API 接口设计

### 6.1 管理端接口

#### 6.1.1 菜单管理接口

##### 6.1.1.1 创建菜单

```http
POST /admin-api/cms/menu/create
Content-Type: application/json

{
  "name": "商品管理",
  "icon": "product",
  "path": "/product",
  "parentId": 0,
  "sort": 1,
  "status": 0
}
```

##### 6.1.1.2 更新菜单

```http
PUT /admin-api/cms/menu/update
Content-Type: application/json

{
  "id": 1,
  "name": "商品管理",
  "icon": "product",
  "path": "/product",
  "parentId": 0,
  "sort": 1,
  "status": 0
}
```

##### ******* 删除菜单

```http
DELETE /admin-api/cms/menu/delete?id=1
```

##### ******* 获取菜单树

```http
GET /admin-api/cms/menu/tree?name=商品
```

#### 6.1.2 DIY 页面管理接口

##### ******* 创建页面

```http
POST /admin-api/cms/diy-page/create
Content-Type: application/json

{
  "name": "商品详情页",
  "parentId": null,
  "menuId": 1,
  "path": "detail",
  "keywords": "商品,详情",
  "description": "商品详情展示页面",
  "content": "{\"components\": []}"
}
```

##### ******* 更新页面（草稿）

```http
PUT /admin-api/cms/diy-page/update
Content-Type: application/json

{
  "id": 1,
  "version": 3,
  "name": "商品详情页",
  "parentId": null,
  "menuId": 1,
  "path": "detail",
  "keywords": "商品,详情",
  "description": "商品详情展示页面",
  "content": "{\"components\": []}"
}
```

##### ******* 发布页面

```http
POST /admin-api/cms/diy-page/publish
Content-Type: application/json

{
  "id": 1,
  "version": 3,
  "remark": "修复样式问题后发布"
}
```

##### ******* 获取版本历史

```http
GET /admin-api/cms/diy-page/version/page?pageId=1&pageNo=1&pageSize=10
```

##### ******* 获取指定版本内容

```http
GET /admin-api/cms/diy-page/version/get?pageId=1&version=2
```

##### ******* 回滚到指定版本

```http
POST /admin-api/cms/diy-page/rollback
Content-Type: application/json

{
  "pageId": 1,
  "targetVersion": 2,
  "currentVersion": 5
}
```

### 6.2 App 端接口

#### 6.2.1 菜单相关接口

##### ******* 获取菜单树（App端）

```http
GET /app-api/cms/menu/tree
```

**响应示例：**

```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "name": "商品管理",
      "icon": "product",
      "path": "/product",
      "parentId": 0,
      "sort": 1,
      "children": [
        {
          "id": 2,
          "name": "商品分类",
          "icon": "category",
          "path": "/product/category",
          "parentId": 1,
          "sort": 1,
          "children": []
        }
      ]
    }
  ],
  "msg": "操作成功"
}
```

##### 6.2.1.2 根据路径获取菜单信息

```http
GET /app-api/cms/menu/get-by-path?path=/product/category
```

**响应示例：**

```json
{
  "code": 0,
  "data": {
    "id": 2,
    "name": "商品分类",
    "icon": "category",
    "path": "/product/category",
    "parentId": 1,
    "sort": 1
  },
  "msg": "操作成功"
}
```

#### 6.2.2 页面相关接口

##### ******* 根据路径获取页面内容

```http
GET /app-api/cms/diy-page/get-by-path?path=/product/category/detail
```

**响应示例：**

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "uuid": "550e8400-e29b-41d4-a716-************",
    "name": "商品详情页",
    "path": "/product/category/detail",
    "keywords": "商品,详情",
    "description": "商品详情展示页面",
    "content": {
      "components": [
        {
          "type": "banner",
          "props": {
            "images": ["https://example.com/banner1.jpg"],
            "autoPlay": true,
            "interval": 3000
          }
        },
        {
          "type": "product-list",
          "props": {
            "title": "热门商品",
            "layout": "grid",
            "columns": 2
          }
        }
      ],
      "style": {
        "backgroundColor": "#ffffff",
        "padding": "10px"
      }
    },
    "publishTime": "2025-01-08 10:30:00"
  },
  "msg": "操作成功"
}
```

##### ******* 根据UUID获取页面内容

```http
GET /app-api/cms/diy-page/get-by-uuid?uuid=550e8400-e29b-41d4-a716-************
```

**响应示例：**

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "uuid": "550e8400-e29b-41d4-a716-************",
    "name": "商品详情页",
    "path": "/product/category/detail",
    "keywords": "商品,详情",
    "description": "商品详情展示页面",
    "content": {
      "components": [
        {
          "type": "banner",
          "props": {
            "images": ["https://example.com/banner1.jpg"],
            "autoPlay": true,
            "interval": 3000
          }
        }
      ]
    },
    "publishTime": "2025-01-08 10:30:00"
  },
  "msg": "操作成功"
}
```

##### ******* 获取菜单下的页面列表

```http
GET /app-api/cms/diy-page/list-by-menu?menuId=1&pageNo=1&pageSize=10
```

**响应示例：**

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "商品详情页",
        "path": "/product/category/detail",
        "keywords": "商品,详情",
        "description": "商品详情展示页面",
        "publishTime": "2025-01-08 10:30:00"
      }
    ],
    "total": 1
  },
  "msg": "操作成功"
}
```

##### ******* 搜索页面

```http
GET /app-api/cms/diy-page/search?keyword=商品&pageNo=1&pageSize=10
```

**响应示例：**

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "商品详情页",
        "path": "/product/category/detail",
        "keywords": "商品,详情",
        "description": "商品详情展示页面",
        "publishTime": "2025-01-08 10:30:00"
      }
    ],
    "total": 1
  },
  "msg": "操作成功"
}
```

##### ******* 获取页面导航路径

```http
GET /app-api/cms/diy-page/breadcrumb?pageId=1
```

**响应示例：**

```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "name": "商品管理",
      "path": "/product",
      "type": "menu"
    },
    {
      "id": 2,
      "name": "商品分类",
      "path": "/product/category",
      "type": "menu"
    },
    {
      "id": 1,
      "name": "商品详情页",
      "path": "/product/category/detail",
      "type": "page"
    }
  ],
  "msg": "操作成功"
}
```

#### 6.2.3 页面统计接口

##### ******* 记录页面访问

```http
POST /app-api/cms/diy-page/visit
Content-Type: application/json

{
  "pageId": 1,
  "uuid": "550e8400-e29b-41d4-a716-************",
  "userAgent": "Mozilla/5.0...",
  "ip": "*************",
  "referer": "https://example.com/home"
}
```

**响应示例：**

```json
{
  "code": 0,
  "data": true,
  "msg": "操作成功"
}
```

##### ******* 获取页面访问统计

```http
GET /app-api/cms/diy-page/statistics?pageId=1&startDate=2025-01-01&endDate=2025-01-08
```

**响应示例：**

```json
{
  "code": 0,
  "data": {
    "totalVisits": 1250,
    "uniqueVisitors": 890,
    "avgStayTime": 120,
    "bounceRate": 0.35,
    "dailyStats": [
      {
        "date": "2025-01-01",
        "visits": 180,
        "uniqueVisitors": 120
      },
      {
        "date": "2025-01-02",
        "visits": 200,
        "uniqueVisitors": 150
      }
    ]
  },
  "msg": "操作成功"
}
```

## 7. 错误码定义

在 `ErrorCodeConstants.java` 中新增错误码：

```java
// ========== CMS 菜单 1-013-021-000 ==========
ErrorCode CMS_MENU_NOT_EXISTS = new ErrorCode(1_013_021_000, "菜单不存在");
ErrorCode CMS_MENU_PATH_DUPLICATE = new ErrorCode(1_013_021_001, "菜单路径已存在");
ErrorCode CMS_MENU_DELETE_FAIL_HAVE_CHILDREN = new ErrorCode(1_013_021_002, "菜单删除失败，存在子菜单");
ErrorCode CMS_MENU_DELETE_FAIL_HAVE_PAGES = new ErrorCode(1_013_021_003, "菜单删除失败，存在关联页面");

// ========== CMS DIY 页面 1-013-022-000 ==========
ErrorCode CMS_DIY_PAGE_NOT_EXISTS = new ErrorCode(1_013_022_000, "DIY页面不存在");
ErrorCode CMS_DIY_PAGE_PATH_DUPLICATE = new ErrorCode(1_013_022_001, "页面路径已存在");
ErrorCode CMS_DIY_PAGE_UUID_DUPLICATE = new ErrorCode(1_013_022_002, "页面UUID已存在");
ErrorCode CMS_DIY_PAGE_PUBLISH_FAIL_NO_CONTENT = new ErrorCode(1_013_022_003, "发布失败，页面内容不能为空");
ErrorCode CMS_DIY_PAGE_DELETE_FAIL_HAVE_CHILDREN = new ErrorCode(1_013_022_004, "页面删除失败，存在子页面");

// ========== CMS DIY 页面版本 1-013-023-000 ==========
ErrorCode CMS_DIY_PAGE_VERSION_NOT_EXISTS = new ErrorCode(1_013_023_000, "页面版本不存在");
ErrorCode CMS_DIY_PAGE_VERSION_CONFLICT = new ErrorCode(1_013_023_001, "页面版本冲突，请刷新后重试");
ErrorCode CMS_DIY_PAGE_ROLLBACK_FAIL_SAME_VERSION = new ErrorCode(1_013_023_002, "回滚失败，目标版本与当前版本相同");

// ========== CMS App 端 1-013-024-000 ==========
ErrorCode CMS_PAGE_NOT_PUBLISHED = new ErrorCode(1_013_024_000, "页面未发布或已下线");
ErrorCode CMS_PAGE_PATH_NOT_FOUND = new ErrorCode(1_013_024_001, "页面路径不存在");
ErrorCode CMS_PAGE_UUID_NOT_FOUND = new ErrorCode(1_013_024_002, "页面UUID不存在");
ErrorCode CMS_MENU_PATH_NOT_FOUND = new ErrorCode(1_013_024_003, "菜单路径不存在");
```

## 8. 数据字典配置

### 8.1 菜单状态

```sql
INSERT INTO system_dict_type (name, type, status, remark, creator, create_time, updater, update_time, deleted) 
VALUES ('CMS菜单状态', 'cms_menu_status', 0, 'CMS菜单状态', '1', NOW(), '1', NOW(), 0);

INSERT INTO system_dict_data (sort, label, value, dict_type, status, color_type, css_class, remark, creator, create_time, updater, update_time, deleted) VALUES
(1, '启用', '0', 'cms_menu_status', 0, 'success', '', '启用状态', '1', NOW(), '1', NOW(), 0),
(2, '禁用', '1', 'cms_menu_status', 0, 'danger', '', '禁用状态', '1', NOW(), '1', NOW(), 0);
```

### 8.2 页面状态

```sql
INSERT INTO system_dict_type (name, type, status, remark, creator, create_time, updater, update_time, deleted) 
VALUES ('CMS页面状态', 'cms_diy_page_status', 0, 'CMS DIY页面状态', '1', NOW(), '1', NOW(), 0);

INSERT INTO system_dict_data (sort, label, value, dict_type, status, color_type, css_class, remark, creator, create_time, updater, update_time, deleted) VALUES
(1, '草稿', '0', 'cms_diy_page_status', 0, 'info', '', '草稿状态', '1', NOW(), '1', NOW(), 0),
(2, '已发布', '1', 'cms_diy_page_status', 0, 'success', '', '已发布状态', '1', NOW(), '1', NOW(), 0),
(3, '已下线', '2', 'cms_diy_page_status', 0, 'warning', '', '已下线状态', '1', NOW(), '1', NOW(), 0);
```

## 9. 权限配置

### 9.1 菜单权限

```sql
-- CMS 菜单管理权限
INSERT INTO system_menu (name, permission, type, sort, parent_id, path, icon, component, status) VALUES
('CMS菜单管理', '', 1, 1, 0, '/cms/menu', 'menu', '', 0),
('菜单查询', 'cms:menu:query', 3, 1, (SELECT id FROM system_menu WHERE permission = '' AND name = 'CMS菜单管理'), '', '', '', 0),
('菜单创建', 'cms:menu:create', 3, 2, (SELECT id FROM system_menu WHERE permission = '' AND name = 'CMS菜单管理'), '', '', '', 0),
('菜单更新', 'cms:menu:update', 3, 3, (SELECT id FROM system_menu WHERE permission = '' AND name = 'CMS菜单管理'), '', '', '', 0),
('菜单删除', 'cms:menu:delete', 3, 4, (SELECT id FROM system_menu WHERE permission = '' AND name = 'CMS菜单管理'), '', '', '', 0);
```

### 9.2 DIY 页面权限

```sql
-- CMS DIY页面管理权限
INSERT INTO system_menu (name, permission, type, sort, parent_id, path, icon, component, status) VALUES
('DIY页面管理', '', 1, 2, 0, '/cms/diy-page', 'page', '', 0),
('页面查询', 'cms:diy-page:query', 3, 1, (SELECT id FROM system_menu WHERE permission = '' AND name = 'DIY页面管理'), '', '', '', 0),
('页面创建', 'cms:diy-page:create', 3, 2, (SELECT id FROM system_menu WHERE permission = '' AND name = 'DIY页面管理'), '', '', '', 0),
('页面更新', 'cms:diy-page:update', 3, 3, (SELECT id FROM system_menu WHERE permission = '' AND name = 'DIY页面管理'), '', '', '', 0),
('页面发布', 'cms:diy-page:publish', 3, 4, (SELECT id FROM system_menu WHERE permission = '' AND name = 'DIY页面管理'), '', '', '', 0),
('页面删除', 'cms:diy-page:delete', 3, 5, (SELECT id FROM system_menu WHERE permission = '' AND name = 'DIY页面管理'), '', '', '', 0),
('版本管理', 'cms:diy-page:version', 3, 6, (SELECT id FROM system_menu WHERE permission = '' AND name = 'DIY页面管理'), '', '', '', 0);
```

### 9.3 App 端权限说明

App 端接口通常面向终端用户，权限控制相对简单：

- **公开接口**：菜单树、页面内容获取等接口无需权限验证
- **用户接口**：页面访问记录等需要用户登录
- **租户隔离**：所有接口都需要进行租户隔离
- **访问限制**：可以通过 IP 限制、频率限制等方式防止恶意访问

```

## 10. 性能优化建议

### 10.1 数据库优化

- 为经常查询的字段添加索引（path、parent_id、menu_id、tenant_id）
- 对于大文本字段（content）考虑分离存储
- 定期清理历史版本数据

### 10.2 缓存策略

#### 10.2.1 Caffeine + Redis 双层缓存架构

```text
用户请求 → L1缓存(Caffeine本地) → L2缓存(Redis分布式) → 数据库
          ↑                    ↑
          JVM内存缓存            网络缓存
          毫秒级响应              集群共享
```

**缓存层级说明：**

- **L1缓存(Caffeine)**：JVM本地缓存，访问速度最快，但不能跨实例共享
- **L2缓存(Redis)**：分布式缓存，支持集群共享，持久化存储
- **缓存同步**：L1和L2缓存之间的数据同步机制
- **缓存穿透保护**：多层防护避免缓存穿透

#### 10.2.2 缓存分类设计

##### A. 菜单缓存

- **缓存键**：`cms:menu:tree:{tenantId}`
- **缓存时间**：30分钟
- **更新策略**：菜单变更时主动清除
- **缓存内容**：完整菜单树结构

```java
@Cacheable(value = "cms:menu", key = "'tree:' + #tenantId")
public List<CmsMenuRespVO> getEnabledMenuTree(Long tenantId) {
    // 查询数据库构建菜单树
}

@CacheEvict(value = "cms:menu", key = "'tree:' + #tenantId")
public void clearMenuCache(Long tenantId) {
    // 菜单变更时清除缓存
}
```

##### B. 页面内容缓存

- **缓存键**：`cms:page:content:{pageId}:{version}`
- **缓存时间**：1小时
- **更新策略**：页面发布时更新缓存
- **缓存内容**：页面完整内容JSON

```java
@Cacheable(value = "cms:page", key = "'content:' + #pageId + ':' + #version")
public CmsDiyPageDO getPageContent(Long pageId, Integer version) {
    // 查询页面内容
}

@CachePut(value = "cms:page", key = "'content:' + #pageId + ':' + #version")
public CmsDiyPageDO updatePageCache(Long pageId, Integer version, CmsDiyPageDO page) {
    return page;
}
```

##### C. 页面路径映射缓存

- **缓存键**：`cms:page:path:{path}`
- **缓存时间**：2小时
- **更新策略**：页面路径变更时清除
- **缓存内容**：路径到页面ID的映射

```java
@Cacheable(value = "cms:page:path", key = "#path")
public Long getPageIdByPath(String path) {
    // 根据路径查询页面ID
}
```

##### D. 访问统计缓存

- **缓存键**：`cms:stats:visit:{pageId}:{date}`
- **缓存时间**：5分钟
- **更新策略**：定时批量写入数据库
- **缓存内容**：页面访问计数

```java
// 使用Redis计数器缓存访问统计
public void incrementVisitCount(Long pageId, String date) {
    String key = "cms:stats:visit:" + pageId + ":" + date;
    redisTemplate.opsForValue().increment(key, 1);
    redisTemplate.expire(key, Duration.ofHours(25)); // 超过一天过期
}
```

#### 10.2.3 基于注解的缓存实现

##### A. Caffeine + Redis 双层缓存配置

```java
@Configuration
@EnableCaching
@EnableAsync
@Slf4j
public class CmsCacheConfig {

    @Value("${yudao.cms.cache.caffeine.maximum-size:1000}")
    private int caffeineMaximumSize;
    
    @Value("${yudao.cms.cache.caffeine.expire-after-write:10}")
    private int caffeineExpireAfterWrite;
    
    @Value("${yudao.cms.cache.redis.menu-ttl:30}")
    private int redisMenuTtl;

    /**
     * Redis缓存管理器（L2缓存）
     */
    @Bean("redisCacheManager")
    @Primary
    public CacheManager redisCacheManager(RedisConnectionFactory redisConnectionFactory) {
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(30))
                .disableCachingNullValues()
                .serializeKeysWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new GenericJackson2JsonRedisSerializer()));

        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // 菜单缓存：30分钟
        cacheConfigurations.put("cms:menu", defaultConfig.entryTtl(Duration.ofMinutes(redisMenuTtl)));
        
        // 页面内容缓存：1小时
        cacheConfigurations.put("cms:page", defaultConfig.entryTtl(Duration.ofHours(1)));
        
        // 路径映射缓存：2小时
        cacheConfigurations.put("cms:page:path", defaultConfig.entryTtl(Duration.ofHours(2)));
        
        // 热门页面缓存：30分钟
        cacheConfigurations.put("cms:page:hot", defaultConfig.entryTtl(Duration.ofMinutes(30)));
        
        // 访问统计缓存：5分钟
        cacheConfigurations.put("cms:stats", defaultConfig.entryTtl(Duration.ofMinutes(5)));

        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .transactionAware()
                .build();
    }

    /**
     * Caffeine本地缓存管理器（L1缓存）
     */
    @Bean("caffeineCacheManager")
    public CacheManager caffeineCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(caffeineConfig());
        cacheManager.setAllowNullValues(false);
        return cacheManager;
    }

    /**
     * Caffeine缓存配置
     */
    @Bean
    public Caffeine<Object, Object> caffeineConfig() {
        return Caffeine.newBuilder()
                .maximumSize(caffeineMaximumSize)
                .expireAfterWrite(Duration.ofMinutes(caffeineExpireAfterWrite))
                .expireAfterAccess(Duration.ofMinutes(5))
                .recordStats()
                .removalListener((key, value, cause) -> {
                    log.debug("Caffeine缓存移除: key={}, cause={}", key, cause);
                });
    }

    /**
     * 双层缓存管理器
     */
    @Bean("multiLevelCacheManager")
    public MultiLevelCacheManager multiLevelCacheManager(
            @Qualifier("caffeineCacheManager") CacheManager l1CacheManager,
            @Qualifier("redisCacheManager") CacheManager l2CacheManager) {
        return new MultiLevelCacheManager(l1CacheManager, l2CacheManager);
    }

    /**
     * 异步执行器配置
     */
    @Bean("cmsAsyncExecutor")
    public Executor asyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("cms-cache-async-");
        executor.setKeepAliveSeconds(60);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    /**
     * 缓存同步服务
     */
    @Bean
    public CacheSyncService cacheSyncService(RedisTemplate<String, Object> redisTemplate) {
        return new CacheSyncService(redisTemplate);
    }
}

/**
 * 双层缓存管理器实现
 */
@Slf4j
public class MultiLevelCacheManager implements CacheManager {
    
    private final CacheManager l1CacheManager; // Caffeine本地缓存
    private final CacheManager l2CacheManager; // Redis分布式缓存
    private final Map<String, MultiLevelCache> cacheMap = new ConcurrentHashMap<>();

    public MultiLevelCacheManager(CacheManager l1CacheManager, CacheManager l2CacheManager) {
        this.l1CacheManager = l1CacheManager;
        this.l2CacheManager = l2CacheManager;
    }

    @Override
    public Cache getCache(String name) {
        return cacheMap.computeIfAbsent(name, cacheName -> {
            Cache l1Cache = l1CacheManager.getCache(cacheName);
            Cache l2Cache = l2CacheManager.getCache(cacheName);
            return new MultiLevelCache(cacheName, l1Cache, l2Cache);
        });
    }

    @Override
    public Collection<String> getCacheNames() {
        Set<String> names = new HashSet<>();
        names.addAll(l1CacheManager.getCacheNames());
        names.addAll(l2CacheManager.getCacheNames());
        return names;
    }
}

/**
 * 双层缓存实现
 */
@Slf4j
public class MultiLevelCache implements Cache {
    
    private final String name;
    private final Cache l1Cache; // Caffeine本地缓存
    private final Cache l2Cache; // Redis分布式缓存

    public MultiLevelCache(String name, Cache l1Cache, Cache l2Cache) {
        this.name = name;
        this.l1Cache = l1Cache;
        this.l2Cache = l2Cache;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public Object getNativeCache() {
        return this;
    }

    @Override
    public ValueWrapper get(Object key) {
        // 1. 先从L1缓存获取
        ValueWrapper l1Value = l1Cache != null ? l1Cache.get(key) : null;
        if (l1Value != null) {
            log.debug("L1缓存命中: cache={}, key={}", name, key);
            return l1Value;
        }

        // 2. 从L2缓存获取
        ValueWrapper l2Value = l2Cache != null ? l2Cache.get(key) : null;
        if (l2Value != null) {
            log.debug("L2缓存命中: cache={}, key={}", name, key);
            // 异步回写到L1缓存
            if (l1Cache != null) {
                CompletableFuture.runAsync(() -> {
                    try {
                        l1Cache.put(key, l2Value.get());
                        log.debug("L2->L1缓存回写: cache={}, key={}", name, key);
                    } catch (Exception e) {
                        log.warn("L1缓存回写失败: cache={}, key={}", name, key, e);
                    }
                });
            }
            return l2Value;
        }

        log.debug("缓存未命中: cache={}, key={}", name, key);
        return null;
    }

    @Override
    public <T> T get(Object key, Class<T> type) {
        ValueWrapper wrapper = get(key);
        return wrapper != null ? (T) wrapper.get() : null;
    }

    @Override
    public <T> T get(Object key, Callable<T> valueLoader) {
        ValueWrapper wrapper = get(key);
        if (wrapper != null) {
            return (T) wrapper.get();
        }

        // 缓存未命中，加载数据
        try {
            T value = valueLoader.call();
            put(key, value);
            return value;
        } catch (Exception e) {
            throw new ValueRetrievalException(key, valueLoader, e);
        }
    }

    @Override
    public void put(Object key, Object value) {
        if (value == null) {
            return;
        }

        // 同时写入L1和L2缓存
        if (l1Cache != null) {
            l1Cache.put(key, value);
        }
        if (l2Cache != null) {
            l2Cache.put(key, value);
        }
        
        log.debug("双层缓存写入: cache={}, key={}", name, key);
    }

    @Override
    public ValueWrapper putIfAbsent(Object key, Object value) {
        ValueWrapper existingValue = get(key);
        if (existingValue == null) {
            put(key, value);
            return null;
        }
        return existingValue;
    }

    @Override
    public void evict(Object key) {
        // 同时清除L1和L2缓存
        if (l1Cache != null) {
            l1Cache.evict(key);
        }
        if (l2Cache != null) {
            l2Cache.evict(key);
        }
        
        log.debug("双层缓存清除: cache={}, key={}", name, key);
    }

    @Override
    public void clear() {
        // 同时清空L1和L2缓存
        if (l1Cache != null) {
            l1Cache.clear();
        }
        if (l2Cache != null) {
            l2Cache.clear();
        }
        
        log.debug("双层缓存清空: cache={}", name);
    }
}

    /**
     * 缓存键生成器
     */
    @Bean
    public KeyGenerator cmsKeyGenerator() {
        return (target, method, params) -> {
            StringBuilder key = new StringBuilder();
            key.append(target.getClass().getSimpleName()).append(":");
            key.append(method.getName()).append(":");
            
            for (Object param : params) {
                if (param != null) {
                    key.append(param.toString()).append(":");
                }
            }
            
            return key.toString();
        };
    }

/**
 * 缓存同步服务 - 处理分布式环境下的缓存一致性
 */
@Service
@Slf4j
public class CacheSyncService {
    
    private final RedisTemplate<String, Object> redisTemplate;
    private final String instanceId;
    
    private static final String CACHE_SYNC_CHANNEL = "cms:cache:sync";
    private static final String CACHE_EVICT_CHANNEL = "cms:cache:evict";

    public CacheSyncService(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
        this.instanceId = generateInstanceId();
        setupMessageListener();
    }

    /**
     * 发布缓存更新消息
     */
    public void publishCacheUpdate(String cacheName, Object key, Object value) {
        CacheSyncMessage message = new CacheSyncMessage();
        message.setInstanceId(instanceId);
        message.setCacheName(cacheName);
        message.setKey(key);
        message.setValue(value);
        message.setOperation(CacheOperation.PUT);
        message.setTimestamp(System.currentTimeMillis());
        
        redisTemplate.convertAndSend(CACHE_SYNC_CHANNEL, message);
        log.debug("发布缓存更新消息: cache={}, key={}", cacheName, key);
    }

    /**
     * 发布缓存清除消息
     */
    public void publishCacheEvict(String cacheName, Object key) {
        CacheSyncMessage message = new CacheSyncMessage();
        message.setInstanceId(instanceId);
        message.setCacheName(cacheName);
        message.setKey(key);
        message.setOperation(CacheOperation.EVICT);
        message.setTimestamp(System.currentTimeMillis());
        
        redisTemplate.convertAndSend(CACHE_EVICT_CHANNEL, message);
        log.debug("发布缓存清除消息: cache={}, key={}", cacheName, key);
    }

    /**
     * 发布缓存清空消息
     */
    public void publishCacheClear(String cacheName) {
        CacheSyncMessage message = new CacheSyncMessage();
        message.setInstanceId(instanceId);
        message.setCacheName(cacheName);
        message.setOperation(CacheOperation.CLEAR);
        message.setTimestamp(System.currentTimeMillis());
        
        redisTemplate.convertAndSend(CACHE_EVICT_CHANNEL, message);
        log.debug("发布缓存清空消息: cache={}", cacheName);
    }

    /**
     * 设置消息监听器
     */
    private void setupMessageListener() {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(redisTemplate.getConnectionFactory());
        
        // 缓存同步消息监听
        container.addMessageListener(new MessageListener() {
            @Override
            public void onMessage(Message message, byte[] pattern) {
                try {
                    CacheSyncMessage syncMessage = (CacheSyncMessage) redisTemplate
                            .getValueSerializer().deserialize(message.getBody());
                    
                    // 忽略自己发送的消息
                    if (instanceId.equals(syncMessage.getInstanceId())) {
                        return;
                    }
                    
                    handleCacheSyncMessage(syncMessage);
                } catch (Exception e) {
                    log.error("处理缓存同步消息失败", e);
                }
            }
        }, new ChannelTopic(CACHE_SYNC_CHANNEL));
        
        // 缓存清除消息监听
        container.addMessageListener(new MessageListener() {
            @Override
            public void onMessage(Message message, byte[] pattern) {
                try {
                    CacheSyncMessage syncMessage = (CacheSyncMessage) redisTemplate
                            .getValueSerializer().deserialize(message.getBody());
                    
                    // 忽略自己发送的消息
                    if (instanceId.equals(syncMessage.getInstanceId())) {
                        return;
                    }
                    
                    handleCacheEvictMessage(syncMessage);
                } catch (Exception e) {
                    log.error("处理缓存清除消息失败", e);
                }
            }
        }, new ChannelTopic(CACHE_EVICT_CHANNEL));
        
        container.start();
    }

    /**
     * 处理缓存同步消息
     */
    private void handleCacheSyncMessage(CacheSyncMessage message) {
        // 这里需要获取本地缓存管理器并更新L1缓存
        log.info("收到缓存同步消息: cache={}, key={}, operation={}", 
                message.getCacheName(), message.getKey(), message.getOperation());
        
        // 实际实现中需要注入CacheManager并更新本地缓存
    }

    /**
     * 处理缓存清除消息
     */
    private void handleCacheEvictMessage(CacheSyncMessage message) {
        log.info("收到缓存清除消息: cache={}, key={}, operation={}", 
                message.getCacheName(), message.getKey(), message.getOperation());
        
        // 实际实现中需要注入CacheManager并清除本地缓存
    }

    private String generateInstanceId() {
        return InetAddress.getLocalHost().getHostName() + "-" + 
               ManagementFactory.getRuntimeMXBean().getName();
    }
}

/**
 * 缓存同步消息
 */
@Data
public class CacheSyncMessage implements Serializable {
    private String instanceId;
    private String cacheName;
    private Object key;
    private Object value;
    private CacheOperation operation;
    private long timestamp;
}

/**
 * 缓存操作类型
 */
public enum CacheOperation {
    PUT,    // 更新缓存
    EVICT,  // 清除缓存
    CLEAR   // 清空缓存
}

    /**
     * 缓存错误处理器
     */
    @Bean
    public CacheErrorHandler cacheErrorHandler() {
        return new CacheErrorHandler() {
            @Override
            public void handleCacheGetError(RuntimeException exception, Cache cache, Object key) {
                log.error("缓存获取异常，缓存: {}, 键: {}", cache.getName(), key, exception);
            }

            @Override
            public void handleCachePutError(RuntimeException exception, Cache cache, Object key, Object value) {
                log.error("缓存写入异常，缓存: {}, 键: {}", cache.getName(), key, exception);
            }

            @Override
            public void handleCacheEvictError(RuntimeException exception, Cache cache, Object key) {
                log.error("缓存清除异常，缓存: {}, 键: {}", cache.getName(), key, exception);
            }

            @Override
            public void handleCacheClearError(RuntimeException exception, Cache cache) {
                log.error("缓存清空异常，缓存: {}", cache.getName(), exception);
            }
        };
    }
}
```

##### B. 基于注解的服务实现

```java
@Service
@Slf4j
public class CmsMenuServiceImpl implements CmsMenuService {

    @Autowired
    private CmsMenuMapper menuMapper;
    
    @Autowired
    @Qualifier("multiLevelCacheManager")
    private CacheManager cacheManager;
    
    @Autowired
    private CacheSyncService cacheSyncService;

    /**
     * 获取菜单树（双层缓存）
     */
    @Cacheable(value = "cms:menu", 
               key = "'tree:' + T(cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder).getTenantId()", 
               unless = "#result == null || #result.isEmpty()",
               cacheManager = "multiLevelCacheManager")
    @Override
    public List<CmsMenuRespVO> getEnabledMenuTree() {
        log.info("从数据库加载菜单树");
        List<CmsMenuDO> menuList = menuMapper.selectList(
            new LambdaQueryWrapperX<CmsMenuDO>()
                .eq(CmsMenuDO::getStatus, CmsMenuStatusEnum.ENABLE.getStatus())
                .orderByAsc(CmsMenuDO::getSort)
        );
        return buildMenuTree(menuList);
    }

    /**
     * 根据路径获取菜单（双层缓存）
     */
    @Cacheable(value = "cms:menu:path", key = "#path", unless = "#result == null",
               cacheManager = "multiLevelCacheManager")
    @Override
    public CmsMenuDO getMenuByPath(String path) {
        log.info("从数据库查询菜单路径: {}", path);
        return menuMapper.selectOne(CmsMenuDO::getPath, path);
    }

    /**
     * 创建菜单（清除双层缓存）
     */
    @CacheEvict(value = {"cms:menu", "cms:menu:path"}, allEntries = true,
                cacheManager = "multiLevelCacheManager")
    @Override
    public Long createMenu(CmsMenuSaveReqVO createReqVO) {
        log.info("创建菜单并清除双层缓存");
        CmsMenuDO menu = CmsMenuConvert.INSTANCE.convert(createReqVO);
        menuMapper.insert(menu);
        
        // 发布缓存清除消息到其他实例
        cacheSyncService.publishCacheClear("cms:menu");
        cacheSyncService.publishCacheClear("cms:menu:path");
        
        return menu.getId();
    }

    /**
     * 更新菜单（清除缓存）
     */
    @CacheEvict(value = {"cms:menu", "cms:menu:path"}, allEntries = true)
    @Override
    public void updateMenu(CmsMenuSaveReqVO updateReqVO) {
        log.info("更新菜单并清除缓存，菜单ID: {}", updateReqVO.getId());
        CmsMenuDO updateObj = CmsMenuConvert.INSTANCE.convert(updateReqVO);
        menuMapper.updateById(updateObj);
    }

    /**
     * 删除菜单（清除缓存）
     */
    @CacheEvict(value = {"cms:menu", "cms:menu:path"}, allEntries = true)
    @Override
    public void deleteMenu(Long id) {
        log.info("删除菜单并清除缓存，菜单ID: {}", id);
        menuMapper.deleteById(id);
    }

    /**
     * 手动清除菜单缓存
     */
    @CacheEvict(value = {"cms:menu", "cms:menu:path"}, allEntries = true)
    @Override
    public void clearMenuCache(Long tenantId) {
        log.info("手动清除菜单缓存，租户ID: {}", tenantId);
    }

    /**
     * 预热菜单缓存
     */
    @CachePut(value = "cms:menu", key = "'tree:' + #tenantId")
    @Override
    public List<CmsMenuRespVO> warmupMenuCache(Long tenantId) {
        log.info("预热菜单缓存，租户ID: {}", tenantId);
        return getEnabledMenuTree();
    }

    private List<CmsMenuRespVO> buildMenuTree(List<CmsMenuDO> menuList) {
        // 构建菜单树的逻辑
        return Collections.emptyList();
    }
}
```

```java
@Service
@Slf4j
public class CmsDiyPageServiceImpl implements CmsDiyPageService {

    @Autowired
    private CmsDiyPageMapper pageMapper;

    /**
     * 根据ID获取页面（带缓存）
     */
    @Cacheable(value = "cms:page", key = "'content:' + #id", unless = "#result == null")
    @Override
    public CmsDiyPageDO getPage(Long id) {
        log.info("从数据库查询页面，ID: {}", id);
        return pageMapper.selectById(id);
    }

    /**
     * 根据UUID获取已发布页面（带缓存）
     */
    @Cacheable(value = "cms:page", key = "'uuid:' + #uuid", 
               condition = "#uuid != null", unless = "#result == null")
    @Override
    public CmsDiyPageDO getPublishedPageByUuid(String uuid) {
        log.info("从数据库查询已发布页面，UUID: {}", uuid);
        return pageMapper.selectOne(
            new LambdaQueryWrapperX<CmsDiyPageDO>()
                .eq(CmsDiyPageDO::getUuid, uuid)
                .eq(CmsDiyPageDO::getStatus, CmsDiyPageStatusEnum.PUBLISHED.getStatus())
        );
    }

    /**
     * 根据路径获取已发布页面（带缓存）
     */
    @Cacheable(value = "cms:page:path", key = "#path", 
               condition = "#path != null", unless = "#result == null")
    @Override
    public CmsDiyPageDO getPublishedPageByPath(String path) {
        log.info("从数据库查询已发布页面，路径: {}", path);
        return pageMapper.selectOne(
            new LambdaQueryWrapperX<CmsDiyPageDO>()
                .eq(CmsDiyPageDO::getPath, path)
                .eq(CmsDiyPageDO::getStatus, CmsDiyPageStatusEnum.PUBLISHED.getStatus())
        );
    }

    /**
     * 更新页面（清除缓存）
     */
    @CacheEvict(value = {"cms:page", "cms:page:path"}, 
                key = "'content:' + #updateReqVO.id", 
                beforeInvocation = true)
    @Override
    public void updatePage(CmsDiyPageSaveReqVO updateReqVO) {
        log.info("更新页面并清除缓存，页面ID: {}", updateReqVO.getId());
        // 乐观锁更新逻辑
        CmsDiyPageDO existPage = validatePageExists(updateReqVO.getId());
        if (!Objects.equals(existPage.getVersion(), updateReqVO.getVersion())) {
            throw exception(CMS_DIY_PAGE_VERSION_CONFLICT);
        }
        
        CmsDiyPageDO updateObj = CmsDiyPageConvert.INSTANCE.convert(updateReqVO);
        updateObj.setVersion(existPage.getVersion() + 1);
        
        int updateCount = pageMapper.updateByIdAndVersion(updateObj, updateReqVO.getVersion());
        if (updateCount == 0) {
            throw exception(CMS_DIY_PAGE_VERSION_CONFLICT);
        }
    }

    /**
     * 发布页面（更新缓存）
     */
    @CachePut(value = "cms:page", key = "'content:' + #id")
    @CacheEvict(value = "cms:page:path", allEntries = true) // 清除路径缓存
    @Override
    public CmsDiyPageDO publishPage(Long id, Integer version, String remark) {
        log.info("发布页面并更新缓存，页面ID: {}", id);
        
        CmsDiyPageDO page = validatePageExists(id);
        checkVersionConflict(id, version);
        
        // 创建版本快照
        diyPageVersionService.createVersionOnPublish(id, page.getContent(), remark);
        
        // 更新页面状态
        CmsDiyPageDO updateObj = new CmsDiyPageDO();
        updateObj.setId(id);
        updateObj.setStatus(CmsDiyPageStatusEnum.PUBLISHED.getStatus());
        updateObj.setPublishedVersion(page.getVersion());
        updateObj.setVersion(page.getVersion() + 1);
        
        pageMapper.updateByIdAndVersion(updateObj, version);
        
        // 返回更新后的页面用于缓存
        return pageMapper.selectById(id);
    }

    /**
     * 删除页面（清除缓存）
     */
    @CacheEvict(value = {"cms:page", "cms:page:path"}, 
                key = "'content:' + #id", 
                beforeInvocation = true)
    @Override
    public void deletePage(Long id) {
        log.info("删除页面并清除缓存，页面ID: {}", id);
        pageMapper.deleteById(id);
    }

    /**
     * 获取热门页面（带缓存）
     */
    @Cacheable(value = "cms:page:hot", key = "'list:' + #limit", unless = "#result == null || #result.isEmpty()")
    @Override
    public List<CmsDiyPageDO> getHotPages(Integer limit) {
        log.info("从数据库查询热门页面，限制数量: {}", limit);
        // 根据访问统计查询热门页面
        return pageMapper.selectHotPages(limit);
    }

    /**
     * 清除页面缓存
     */
    @CacheEvict(value = {"cms:page", "cms:page:path", "cms:page:hot"}, 
                key = "'content:' + #pageId")
    @Override
    public void clearPageCache(Long pageId) {
        log.info("手动清除页面缓存，页面ID: {}", pageId);
    }

    /**
     * 批量预热页面缓存
     */
    @Override
    public void warmupPageCache(List<Long> pageIds) {
        log.info("批量预热页面缓存，数量: {}", pageIds.size());
        for (Long pageId : pageIds) {
            try {
                getPage(pageId); // 触发缓存加载
            } catch (Exception e) {
                log.warn("预热页面缓存失败，页面ID: {}", pageId, e);
            }
        }
    }
}
```

##### C. 自定义缓存注解

```java
/**
 * 自定义缓存注解，支持更灵活的缓存控制
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CmsCache {
    
    /**
     * 缓存名称
     */
    String value() default "";
    
    /**
     * 缓存键
     */
    String key() default "";
    
    /**
     * 缓存条件
     */
    String condition() default "";
    
    /**
     * 排除条件
     */
    String unless() default "";
    
    /**
     * 缓存时间（秒）
     */
    int ttl() default 1800;
    
    /**
     * 缓存类型
     */
    CacheType type() default CacheType.REDIS;
    
    /**
     * 是否异步更新
     */
    boolean async() default false;
}

/**
 * 缓存清除注解
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CmsCacheEvict {
    
    /**
     * 缓存名称
     */
    String[] value() default {};
    
    /**
     * 缓存键
     */
    String key() default "";
    
    /**
     * 是否清除所有
     */
    boolean allEntries() default false;
    
    /**
     * 是否在方法执行前清除
     */
    boolean beforeInvocation() default false;
}

/**
 * 缓存类型枚举
 */
public enum CacheType {
    REDIS,      // Redis缓存
    CAFFEINE,   // 本地缓存
    MULTI       // 多级缓存
}
```

```java
/**
 * 自定义缓存切面
 */
@Aspect
@Component
@Slf4j
@Order(1)
public class CmsCacheAspect {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private CacheManager caffeineCacheManager;

    /**
     * 缓存切面
     */
    @Around("@annotation(cmsCache)")
    public Object cache(ProceedingJoinPoint joinPoint, CmsCache cmsCache) throws Throwable {
        String key = generateKey(joinPoint, cmsCache.key());
        String cacheName = cmsCache.value();
        
        // 检查条件
        if (!evaluateCondition(joinPoint, cmsCache.condition())) {
            return joinPoint.proceed();
        }
        
        // 尝试从缓存获取
        Object cachedValue = getCachedValue(cacheName, key, cmsCache.type());
        if (cachedValue != null) {
            // 检查排除条件
            if (!evaluateUnless(cachedValue, cmsCache.unless())) {
                log.debug("缓存命中: {}:{}", cacheName, key);
                return cachedValue;
            }
        }
        
        // 执行方法
        Object result = joinPoint.proceed();
        
        // 缓存结果
        if (result != null && !evaluateUnless(result, cmsCache.unless())) {
            if (cmsCache.async()) {
                cacheValueAsync(cacheName, key, result, cmsCache.ttl(), cmsCache.type());
            } else {
                cacheValue(cacheName, key, result, cmsCache.ttl(), cmsCache.type());
            }
        }
        
        return result;
    }

    /**
     * 缓存清除切面
     */
    @Around("@annotation(cmsCacheEvict)")
    public Object evict(ProceedingJoinPoint joinPoint, CmsCacheEvict cmsCacheEvict) throws Throwable {
        
        if (cmsCacheEvict.beforeInvocation()) {
            evictCache(joinPoint, cmsCacheEvict);
        }
        
        Object result = joinPoint.proceed();
        
        if (!cmsCacheEvict.beforeInvocation()) {
            evictCache(joinPoint, cmsCacheEvict);
        }
        
        return result;
    }

    private void evictCache(ProceedingJoinPoint joinPoint, CmsCacheEvict cmsCacheEvict) {
        for (String cacheName : cmsCacheEvict.value()) {
            if (cmsCacheEvict.allEntries()) {
                // 清除所有缓存
                clearAllCache(cacheName);
            } else {
                // 清除指定键的缓存
                String key = generateKey(joinPoint, cmsCacheEvict.key());
                clearCache(cacheName, key);
            }
        }
    }

    private Object getCachedValue(String cacheName, String key, CacheType type) {
        switch (type) {
            case REDIS:
                return redisTemplate.opsForValue().get(cacheName + ":" + key);
            case CAFFEINE:
                Cache cache = caffeineCacheManager.getCache(cacheName);
                return cache != null ? cache.get(key, Object.class) : null;
            case MULTI:
                // 先查本地缓存，再查Redis
                Object localValue = getCachedValue(cacheName, key, CacheType.CAFFEINE);
                if (localValue != null) {
                    return localValue;
                }
                return getCachedValue(cacheName, key, CacheType.REDIS);
            default:
                return null;
        }
    }

    private void cacheValue(String cacheName, String key, Object value, int ttl, CacheType type) {
        switch (type) {
            case REDIS:
                redisTemplate.opsForValue().set(cacheName + ":" + key, value, Duration.ofSeconds(ttl));
                break;
            case CAFFEINE:
                Cache cache = caffeineCacheManager.getCache(cacheName);
                if (cache != null) {
                    cache.put(key, value);
                }
                break;
            case MULTI:
                // 同时缓存到本地和Redis
                cacheValue(cacheName, key, value, ttl, CacheType.CAFFEINE);
                cacheValue(cacheName, key, value, ttl, CacheType.REDIS);
                break;
        }
    }

    @Async
    private void cacheValueAsync(String cacheName, String key, Object value, int ttl, CacheType type) {
        cacheValue(cacheName, key, value, ttl, type);
    }

    private String generateKey(ProceedingJoinPoint joinPoint, String keyExpression) {
        if (StringUtils.hasText(keyExpression)) {
            // 使用SpEL表达式解析键
            return parseSpEL(keyExpression, joinPoint);
        }
        
        // 默认键生成策略
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(joinPoint.getSignature().getName());
        
        Object[] args = joinPoint.getArgs();
        for (Object arg : args) {
            if (arg != null) {
                keyBuilder.append(":").append(arg.toString());
            }
        }
        
        return keyBuilder.toString();
    }

    private String parseSpEL(String expression, ProceedingJoinPoint joinPoint) {
        // SpEL表达式解析实现
        ExpressionParser parser = new SpelExpressionParser();
        EvaluationContext context = new StandardEvaluationContext();
        
        // 设置方法参数
        Object[] args = joinPoint.getArgs();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String[] paramNames = signature.getParameterNames();
        
        for (int i = 0; i < paramNames.length; i++) {
            context.setVariable(paramNames[i], args[i]);
        }
        
        Expression exp = parser.parseExpression(expression);
        return exp.getValue(context, String.class);
    }

    private boolean evaluateCondition(ProceedingJoinPoint joinPoint, String condition) {
        if (!StringUtils.hasText(condition)) {
            return true;
        }
        // 条件表达式评估实现
        return true;
    }

    private boolean evaluateUnless(Object result, String unless) {
        if (!StringUtils.hasText(unless)) {
            return false;
        }
        // Unless条件评估实现
        return false;
    }

    private void clearCache(String cacheName, String key) {
        redisTemplate.delete(cacheName + ":" + key);
        Cache cache = caffeineCacheManager.getCache(cacheName);
        if (cache != null) {
            cache.evict(key);
        }
    }

    private void clearAllCache(String cacheName) {
        // 清除Redis中的所有相关键
        Set<String> keys = redisTemplate.keys(cacheName + ":*");
        if (!keys.isEmpty()) {
            redisTemplate.delete(keys);
        }
        
        // 清除本地缓存
        Cache cache = caffeineCacheManager.getCache(cacheName);
        if (cache != null) {
            cache.clear();
        }
    }
}
```

##### D. 使用自定义注解的示例

```java
@Service
@Slf4j
public class CmsMenuServiceImpl implements CmsMenuService {

    /**
     * 使用自定义缓存注解
     */
    @CmsCache(value = "cms:menu", key = "'tree:' + T(cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder).getTenantId()", 
              ttl = 1800, type = CacheType.MULTI)
    @Override
    public List<CmsMenuRespVO> getEnabledMenuTree() {
        log.info("从数据库加载菜单树");
        // 查询逻辑
        return buildMenuTree();
    }

    /**
     * 使用自定义缓存清除注解
     */
    @CmsCacheEvict(value = {"cms:menu", "cms:menu:path"}, allEntries = true)
    @Override
    public Long createMenu(CmsMenuSaveReqVO createReqVO) {
        log.info("创建菜单并清除缓存");
        // 创建逻辑
        return menuId;
    }
}

@Service
@Slf4j
public class CmsDiyPageServiceImpl implements CmsDiyPageService {

    /**
     * 多级缓存 + 异步更新
     */
    @CmsCache(value = "cms:page", key = "'content:' + #id", 
              ttl = 3600, type = CacheType.MULTI, async = true)
    @Override
    public CmsDiyPageDO getPage(Long id) {
        log.info("从数据库查询页面，ID: {}", id);
        return pageMapper.selectById(id);
    }

    /**
     * 条件缓存
     */
    @CmsCache(value = "cms:page:search", key = "'keyword:' + #keyword + ':' + #pageNo", 
              condition = "#keyword != null && #keyword.length() > 2",
              unless = "#result == null || #result.isEmpty()",
              ttl = 600, type = CacheType.REDIS)
    @Override
    public PageResult<CmsDiyPageDO> searchPublishedPages(String keyword, Integer pageNo, Integer pageSize) {
        log.info("搜索页面，关键词: {}", keyword);
        // 搜索逻辑
        return searchResult;
    }
}
```

##### E. 注解缓存使用总结

| 注解类型 | 使用场景 | 示例 |
|---------|---------|------|
| `@Cacheable` | 查询方法缓存 | `@Cacheable(value = "cms:menu", key = "'tree:' + #tenantId")` |
| `@CachePut` | 更新缓存 | `@CachePut(value = "cms:page", key = "'content:' + #id")` |
| `@CacheEvict` | 清除缓存 | `@CacheEvict(value = "cms:menu", allEntries = true)` |
| `@Caching` | 组合缓存操作 | `@Caching(evict = {@CacheEvict("cache1"), @CacheEvict("cache2")})` |
| `@CmsCache` | 自定义缓存 | `@CmsCache(value = "cms:page", ttl = 3600, type = CacheType.MULTI)` |
| `@CmsCacheEvict` | 自定义清除 | `@CmsCacheEvict(value = {"cache1", "cache2"}, allEntries = true)` |

**注解缓存的优势：**

1. **声明式编程**：通过注解声明缓存策略，代码更简洁
2. **AOP支持**：基于Spring AOP，无侵入性
3. **灵活配置**：支持SpEL表达式，条件缓存等
4. **统一管理**：缓存配置集中管理，易于维护
5. **多级缓存**：支持Redis + 本地缓存的多级架构
6. **异步处理**：支持异步缓存更新，提高性能

**Caffeine + Redis 双层缓存最佳实践：**

1. **缓存层级策略**
   - L1缓存(Caffeine)：存储热点数据，过期时间较短(5-30分钟)
   - L2缓存(Redis)：存储更多数据，过期时间较长(1-24小时)
   - 数据一致性：L1缓存失效时自动从L2缓存回填

2. **缓存大小配置**
   - Caffeine最大条目数根据JVM内存合理设置
   - 避免L1缓存过大导致GC压力
   - 监控缓存命中率，调整缓存大小

3. **缓存同步策略**
   - 使用Redis发布订阅实现集群间缓存同步
   - 缓存更新时先更新数据库，再清除缓存
   - 避免缓存雪崩，设置随机过期时间

4. **性能优化**
   - 异步回写L1缓存，避免阻塞主线程
   - 批量预热热点数据
   - 使用缓存统计信息优化配置

5. **异常处理**
   - Redis不可用时降级到数据库查询
   - 缓存序列化异常时记录日志并跳过缓存
   - 提供缓存开关，支持紧急情况下关闭缓存

6. **监控告警**
   - 监控L1/L2缓存命中率
   - 监控缓存内存使用情况
   - 设置缓存异常告警

##### F. Caffeine + Redis 缓存性能监控

```java
/**
 * 缓存性能监控服务
 */
@Service
@Slf4j
public class CacheMetricsService {
    
    private final MeterRegistry meterRegistry;
    private final CacheManager caffeineCacheManager;
    private final CacheManager redisCacheManager;
    
    public CacheMetricsService(MeterRegistry meterRegistry,
                              @Qualifier("caffeineCacheManager") CacheManager caffeineCacheManager,
                              @Qualifier("redisCacheManager") CacheManager redisCacheManager) {
        this.meterRegistry = meterRegistry;
        this.caffeineCacheManager = caffeineCacheManager;
        this.redisCacheManager = redisCacheManager;
        
        // 注册缓存指标
        registerCacheMetrics();
    }

    /**
     * 注册缓存指标
     */
    private void registerCacheMetrics() {
        // Caffeine缓存指标
        caffeineCacheManager.getCacheNames().forEach(cacheName -> {
            Cache cache = caffeineCacheManager.getCache(cacheName);
            if (cache.getNativeCache() instanceof com.github.benmanes.caffeine.cache.Cache) {
                com.github.benmanes.caffeine.cache.Cache<Object, Object> caffeineCache = 
                    (com.github.benmanes.caffeine.cache.Cache<Object, Object>) cache.getNativeCache();
                
                // 注册Caffeine统计指标
                CaffeineCacheMetrics.monitor(meterRegistry, caffeineCache, cacheName, "caffeine");
            }
        });
        
        // 自定义指标
        Gauge.builder("cms.cache.l1.size")
                .description("L1缓存大小")
                .register(meterRegistry, this, CacheMetricsService::getL1CacheSize);
                
        Gauge.builder("cms.cache.l2.size")
                .description("L2缓存大小")
                .register(meterRegistry, this, CacheMetricsService::getL2CacheSize);
    }

    /**
     * 获取L1缓存大小
     */
    public long getL1CacheSize() {
        return caffeineCacheManager.getCacheNames().stream()
                .mapToLong(cacheName -> {
                    Cache cache = caffeineCacheManager.getCache(cacheName);
                    if (cache.getNativeCache() instanceof com.github.benmanes.caffeine.cache.Cache) {
                        return ((com.github.benmanes.caffeine.cache.Cache<?, ?>) cache.getNativeCache()).estimatedSize();
                    }
                    return 0;
                })
                .sum();
    }

    /**
     * 获取L2缓存大小（估算）
     */
    public long getL2CacheSize() {
        // Redis缓存大小需要通过Redis命令获取，这里返回估算值
        return redisCacheManager.getCacheNames().size() * 100; // 简化实现
    }

    /**
     * 获取缓存统计信息
     */
    public CacheStatsVO getCacheStats() {
        CacheStatsVO stats = new CacheStatsVO();
        
        // L1缓存统计
        Map<String, CaffeineCacheStats> l1Stats = new HashMap<>();
        caffeineCacheManager.getCacheNames().forEach(cacheName -> {
            Cache cache = caffeineCacheManager.getCache(cacheName);
            if (cache.getNativeCache() instanceof com.github.benmanes.caffeine.cache.Cache) {
                com.github.benmanes.caffeine.cache.Cache<Object, Object> caffeineCache = 
                    (com.github.benmanes.caffeine.cache.Cache<Object, Object>) cache.getNativeCache();
                
                CacheStats cacheStats = caffeineCache.stats();
                CaffeineCacheStats caffeineCacheStats = new CaffeineCacheStats();
                caffeineCacheStats.setHitCount(cacheStats.hitCount());
                caffeineCacheStats.setMissCount(cacheStats.missCount());
                caffeineCacheStats.setHitRate(cacheStats.hitRate());
                caffeineCacheStats.setEvictionCount(cacheStats.evictionCount());
                caffeineCacheStats.setLoadTime(cacheStats.averageLoadTime());
                
                l1Stats.put(cacheName, caffeineCacheStats);
            }
        });
        
        stats.setL1Stats(l1Stats);
        stats.setL1TotalSize(getL1CacheSize());
        stats.setL2TotalSize(getL2CacheSize());
        
        return stats;
    }
}

/**
 * 缓存统计VO
 */
@Data
public class CacheStatsVO {
    private Map<String, CaffeineCacheStats> l1Stats;
    private Map<String, RedisCacheStats> l2Stats;
    private long l1TotalSize;
    private long l2TotalSize;
    private double overallHitRate;
}

/**
 * Caffeine缓存统计
 */
@Data
public class CaffeineCacheStats {
    private long hitCount;
    private long missCount;
    private double hitRate;
    private long evictionCount;
    private double loadTime;
}

/**
 * Redis缓存统计
 */
@Data
public class RedisCacheStats {
    private long keyCount;
    private long memoryUsage;
    private long expiredKeys;
}
```

##### G. 缓存预热和刷新策略

```java
/**
 * 双层缓存预热服务
 */
@Service
@Slf4j
public class MultiLevelCacheWarmupService {
    
    @Autowired
    private CmsMenuService menuService;
    
    @Autowired
    private CmsDiyPageService pageService;
    
    @Autowired
    @Qualifier("multiLevelCacheManager")
    private CacheManager cacheManager;
    
    @Autowired
    private CacheMetricsService cacheMetricsService;

    /**
     * 系统启动预热
     */
    @EventListener(ApplicationReadyEvent.class)
    public void warmupOnStartup() {
        log.info("开始双层缓存预热...");
        
        try {
            // 预热菜单缓存
            warmupMenuCache();
            
            // 预热热门页面缓存
            warmupHotPageCache();
            
            // 输出预热统计
            logWarmupStats();
            
        } catch (Exception e) {
            log.error("缓存预热失败", e);
        }
        
        log.info("双层缓存预热完成");
    }

    /**
     * 预热菜单缓存
     */
    private void warmupMenuCache() {
        List<Long> tenantIds = getTenantIds();
        
        tenantIds.parallelStream().forEach(tenantId -> {
            try {
                TenantContextHolder.setTenantId(tenantId);
                
                // 预热菜单树 - 会同时写入L1和L2缓存
                List<CmsMenuRespVO> menuTree = menuService.getEnabledMenuTree();
                log.debug("预热菜单缓存完成，租户: {}, 菜单数量: {}", tenantId, menuTree.size());
                
            } catch (Exception e) {
                log.warn("预热菜单缓存失败，租户: {}", tenantId, e);
            } finally {
                TenantContextHolder.clear();
            }
        });
    }

    /**
     * 预热热门页面缓存
     */
    private void warmupHotPageCache() {
        try {
            // 获取热门页面列表
            List<CmsDiyPageDO> hotPages = pageService.getHotPages(50);
            
            // 并行预热页面缓存
            hotPages.parallelStream().forEach(page -> {
                try {
                    // 预热页面内容缓存
                    pageService.getPage(page.getId());
                    
                    // 预热路径映射缓存
                    pageService.getPublishedPageByPath(page.getPath());
                    
                } catch (Exception e) {
                    log.warn("预热页面缓存失败，页面ID: {}", page.getId(), e);
                }
            });
            
            log.info("预热热门页面缓存完成，数量: {}", hotPages.size());
            
        } catch (Exception e) {
            log.error("预热热门页面缓存失败", e);
        }
    }

    /**
     * 定时刷新缓存
     */
    @Scheduled(fixedRate = 1800000) // 30分钟
    public void refreshCache() {
        log.info("开始定时刷新双层缓存...");
        
        // 刷新菜单缓存
        refreshMenuCache();
        
        // 刷新热门页面缓存
        refreshHotPageCache();
        
        // 清理过期的L1缓存
        cleanupExpiredL1Cache();
        
        log.info("定时刷新双层缓存完成");
    }

    /**
     * 刷新菜单缓存
     */
    private void refreshMenuCache() {
        Cache menuCache = cacheManager.getCache("cms:menu");
        if (menuCache != null) {
            // 清除旧缓存
            menuCache.clear();
            
            // 重新预热
            warmupMenuCache();
        }
    }

    /**
     * 刷新热门页面缓存
     */
    private void refreshHotPageCache() {
        Cache pageCache = cacheManager.getCache("cms:page");
        Cache hotPageCache = cacheManager.getCache("cms:page:hot");
        
        if (hotPageCache != null) {
            hotPageCache.clear();
        }
        
        // 重新预热热门页面
        warmupHotPageCache();
    }

    /**
     * 清理过期的L1缓存
     */
    private void cleanupExpiredL1Cache() {
        // Caffeine会自动清理过期缓存，这里可以做一些额外的清理工作
        log.debug("执行L1缓存清理检查");
    }

    /**
     * 输出预热统计信息
     */
    private void logWarmupStats() {
        CacheStatsVO stats = cacheMetricsService.getCacheStats();
        log.info("缓存预热统计 - L1缓存大小: {}, L2缓存大小: {}", 
                stats.getL1TotalSize(), stats.getL2TotalSize());
    }

    private List<Long> getTenantIds() {
        // 获取所有租户ID
        return Arrays.asList(1L, 2L, 3L); // 示例实现
    }
}
```

#### 10.2.4 缓存预热策略

```java
@Component
@Slf4j
public class CmsCacheWarmupService {

    @Autowired
    private CmsMenuService menuService;
    
    @Autowired
    private CmsDiyPageService pageService;
    
    @Autowired
    private CmsCacheService cacheService;

    /**
     * 系统启动时预热缓存
     */
    @EventListener(ApplicationReadyEvent.class)
    public void warmupCacheOnStartup() {
        log.info("开始预热CMS缓存...");
        
        // 预热菜单缓存
        warmupMenuCache();
        
        // 预热热门页面缓存
        warmupHotPageCache();
        
        log.info("CMS缓存预热完成");
    }

    /**
     * 预热菜单缓存
     */
    private void warmupMenuCache() {
        try {
            // 获取所有租户的菜单树并缓存
            List<Long> tenantIds = getTenantIds();
            for (Long tenantId : tenantIds) {
                TenantContextHolder.setTenantId(tenantId);
                List<CmsMenuRespVO> menuTree = menuService.getEnabledMenuTree();
                cacheService.cacheMenuTree(tenantId, menuTree);
            }
            log.info("菜单缓存预热完成，租户数量: {}", tenantIds.size());
        } catch (Exception e) {
            log.error("菜单缓存预热失败", e);
        } finally {
            TenantContextHolder.clear();
        }
    }

    /**
     * 预热热门页面缓存
     */
    private void warmupHotPageCache() {
        try {
            // 获取热门页面列表
            List<CmsDiyPageDO> hotPages = getHotPages();
            cacheService.warmupPageCache(hotPages);
            log.info("热门页面缓存预热完成，页面数量: {}", hotPages.size());
        } catch (Exception e) {
            log.error("页面缓存预热失败", e);
        }
    }

    /**
     * 定时刷新缓存
     */
    @Scheduled(fixedRate = 1800000) // 30分钟执行一次
    public void refreshCache() {
        log.info("开始定时刷新缓存...");
        warmupMenuCache();
        warmupHotPageCache();
        log.info("定时刷新缓存完成");
    }

    private List<Long> getTenantIds() {
        // 获取所有租户ID的实现
        return Arrays.asList(1L, 2L, 3L); // 示例
    }

    private List<CmsDiyPageDO> getHotPages() {
        // 获取热门页面的实现，可以根据访问量排序
        return pageService.getHotPages(100); // 获取前100个热门页面
    }
}
```

#### 10.2.5 缓存监控

```java
@Component
@Slf4j
public class CmsCacheMonitor {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private MeterRegistry meterRegistry;

    /**
     * 监控缓存命中率
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void monitorCacheHitRate() {
        try {
            // 监控菜单缓存
            monitorCachePattern("cms:menu:*", "menu");
            
            // 监控页面缓存
            monitorCachePattern("cms:page:*", "page");
            
            // 监控路径缓存
            monitorCachePattern("cms:path:*", "path");
            
        } catch (Exception e) {
            log.error("缓存监控失败", e);
        }
    }

    private void monitorCachePattern(String pattern, String cacheType) {
        Set<String> keys = redisTemplate.keys(pattern);
        int keyCount = keys != null ? keys.size() : 0;
        
        // 记录缓存键数量指标
        Gauge.builder("cms.cache.keys")
                .tag("type", cacheType)
                .register(meterRegistry, keyCount);
        
        log.debug("缓存监控 - 类型: {}, 键数量: {}", cacheType, keyCount);
    }

    /**
     * 缓存清理
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void cleanupExpiredCache() {
        log.info("开始清理过期缓存...");
        
        try {
            // 清理过期的统计缓存
            cleanupStatsCache();
            
            log.info("过期缓存清理完成");
        } catch (Exception e) {
            log.error("清理过期缓存失败", e);
        }
    }

    private void cleanupStatsCache() {
        String pattern = "cms:stats:visit:*";
        Set<String> keys = redisTemplate.keys(pattern);
        
        if (keys != null && !keys.isEmpty()) {
            // 检查并删除过期的统计缓存
            LocalDate yesterday = LocalDate.now().minusDays(1);
            String yesterdayStr = yesterday.toString();
            
            keys.stream()
                .filter(key -> !key.contains(yesterdayStr))
                .forEach(key -> {
                    Long ttl = redisTemplate.getExpire(key);
                    if (ttl != null && ttl <= 0) {
                        redisTemplate.delete(key);
                    }
                });
        }
    }
}
```

### 10.3 查询优化

- 菜单树查询使用递归CTE或者程序内存构建
- 分页查询使用合适的索引
- 避免N+1查询问题
- App 端页面查询优化（只查询已发布状态的页面）
- 访问统计异步处理（使用消息队列）

### 10.4 App 端专项优化

- **CDN 加速**：页面内容通过 CDN 分发，提高访问速度
- **数据压缩**：页面内容 JSON 进行 Gzip 压缩
- **懒加载**：大型页面内容分块加载
- **缓存策略**：设置合理的缓存过期时间
- **访问统计优化**：批量写入、异步处理、定时汇总

## 11. 安全考虑

### 11.1 权限控制

- 所有接口都需要进行权限验证
- 支持租户隔离
- 敏感操作需要记录操作日志

### 11.2 数据验证

- 页面路径格式验证
- UUID 格式验证
- 内容长度限制
- XSS 防护

## 12. 监控和日志

### 12.1 操作日志

- 菜单的创建、更新、删除操作
- 页面的发布、下线操作
- 版本回滚操作

### 12.2 性能监控

- 接口响应时间监控
- 数据库查询性能监控
- 缓存命中率监控

### 12.3 缓存监控

#### 12.3.1 缓存指标监控

```java
// 缓存监控指标
@Component
public class CmsCacheMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter cacheHitCounter;
    private final Counter cacheMissCounter;
    private final Timer cacheLoadTimer;
    
    public CmsCacheMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.cacheHitCounter = Counter.builder("cms.cache.hit")
                .description("Cache hit count")
                .register(meterRegistry);
        this.cacheMissCounter = Counter.builder("cms.cache.miss")
                .description("Cache miss count")
                .register(meterRegistry);
        this.cacheLoadTimer = Timer.builder("cms.cache.load")
                .description("Cache load time")
                .register(meterRegistry);
    }
    
    public void recordCacheHit(String cacheType) {
        cacheHitCounter.increment(Tags.of("type", cacheType));
    }
    
    public void recordCacheMiss(String cacheType) {
        cacheMissCounter.increment(Tags.of("type", cacheType));
    }
    
    public Timer.Sample startCacheLoadTimer() {
        return Timer.start(meterRegistry);
    }
}
```

#### 12.3.2 缓存健康检查

```java
@Component
public class CmsCacheHealthIndicator implements HealthIndicator {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Override
    public Health health() {
        try {
            // 检查Redis连接
            String pong = redisTemplate.getConnectionFactory()
                    .getConnection().ping();
            
            if ("PONG".equals(pong)) {
                return Health.up()
                        .withDetail("redis", "连接正常")
                        .withDetail("cache", "缓存服务可用")
                        .build();
            } else {
                return Health.down()
                        .withDetail("redis", "连接异常")
                        .build();
            }
        } catch (Exception e) {
            return Health.down()
                    .withDetail("redis", "连接失败: " + e.getMessage())
                    .build();
        }
    }
}
```

#### 12.3.3 缓存告警配置

```yaml
# Prometheus 告警规则
groups:
  - name: cms-cache-alerts
    rules:
      - alert: CmsCacheHitRateLow
        expr: (rate(cms_cache_hit_total[5m]) / (rate(cms_cache_hit_total[5m]) + rate(cms_cache_miss_total[5m]))) < 0.8
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "CMS缓存命中率过低"
          description: "CMS缓存命中率低于80%，当前值: {{ $value }}"
      
      - alert: CmsCacheLoadTimeHigh
        expr: histogram_quantile(0.95, rate(cms_cache_load_duration_seconds_bucket[5m])) > 0.5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "CMS缓存加载时间过长"
          description: "CMS缓存95%分位数加载时间超过500ms，当前值: {{ $value }}s"
      
      - alert: RedisConnectionDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis连接断开"
          description: "Redis服务不可用，缓存功能受影响"
```

## 13. 部署说明

### 13.1 数据库脚本执行顺序

1. 创建数据表
2. 插入数据字典
3. 配置权限菜单
4. 初始化基础数据

### 13.2 配置项

```yaml
yudao:
  cms:
    # 页面路径最大长度
    max-path-length: 500
    # 版本保留数量
    max-version-count: 50
    # 是否启用缓存
    cache-enabled: true
    # 乐观锁重试次数
    optimistic-lock-retry-count: 3
    # 版本冲突提示信息
    version-conflict-message: "页面已被其他用户修改，请刷新后重新编辑"
    
    # 缓存配置
    cache:
      # Redis缓存配置（L2缓存）
      redis:
        # 菜单缓存时间（分钟）
        menu-ttl: 30
        # 页面内容缓存时间（小时）
        page-content-ttl: 1
        # 路径映射缓存时间（小时）
        path-mapping-ttl: 2
        # 访问统计缓存时间（分钟）
        visit-stats-ttl: 5
        # 缓存键前缀
        key-prefix: "cms:"
        # 是否启用事务支持
        enable-transactions: true
        # 序列化方式
        serialization: json
      
      # Caffeine本地缓存配置（L1缓存）
      caffeine:
        # 不同缓存的配置
        specs:
          # 菜单缓存配置
          cms:menu:
            maximum-size: 500
            expire-after-write: 10m
            expire-after-access: 5m
            record-stats: true
          # 页面内容缓存配置  
          cms:page:
            maximum-size: 1000
            expire-after-write: 30m
            expire-after-access: 10m
            record-stats: true
          # 路径映射缓存配置
          cms:page:path:
            maximum-size: 2000
            expire-after-write: 60m
            expire-after-access: 20m
            record-stats: true
          # 热门页面缓存配置
          cms:page:hot:
            maximum-size: 100
            expire-after-write: 15m
            expire-after-access: 5m
            record-stats: true
        # 默认配置
        default:
          maximum-size: 1000
          expire-after-write: 10m
          expire-after-access: 5m
          record-stats: true
          # 移除监听器
          removal-listener: true
      
      # 缓存预热配置
      warmup:
        # 是否启用启动预热
        enabled: true
        # 预热热门页面数量
        hot-page-count: 100
        # 定时刷新间隔（分钟）
        refresh-interval: 30
      
      # 缓存监控配置
      monitor:
        # 是否启用监控
        enabled: true
        # 监控间隔（秒）
        interval: 60
        # 缓存清理时间（cron表达式）
        cleanup-cron: "0 0 2 * * ?"
    
    # App端配置
    app:
      # 页面内容缓存时间（秒）
      page-cache-ttl: 3600
      # 菜单树缓存时间（秒）
      menu-cache-ttl: 1800
      # 访问统计批量大小
      visit-log-batch-size: 100
      # 访问统计异步处理间隔（秒）
      visit-log-process-interval: 60
      # 是否启用CDN
      cdn-enabled: true
      # CDN域名
      cdn-domain: "https://cdn.example.com"
      # 页面内容压缩
      content-compression: true

# Spring Cache 配置
spring:
  cache:
    type: redis
    redis:
      time-to-live: 1800000 # 30分钟
      cache-null-values: false
      use-key-prefix: true
      key-prefix: "cache:"
    # 缓存名称配置
    cache-names:
      - cms:menu
      - cms:page
      - cms:page:path
      - cms:page:hot
      - cms:stats
  
  # Redis 配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 2000ms

# 异步配置
async:
  executor:
    core-pool-size: 5
    max-pool-size: 20
    queue-capacity: 100
    thread-name-prefix: "cms-async-"
    keep-alive-seconds: 60
```

## 14. 测试计划

### 14.1 单元测试

- Service 层业务逻辑测试
- 路径生成算法测试
- 版本管理功能测试
- 乐观锁机制测试
- 版本冲突处理测试
- App 端页面查询逻辑测试
- 访问统计计算逻辑测试
- 缓存服务功能测试
- 缓存预热逻辑测试
- 缓存清理策略测试

### 14.2 集成测试

- API 接口测试
- 数据库事务测试
- 权限验证测试
- 乐观锁并发测试
- 版本回滚功能测试
- App 端接口功能测试
- 页面访问统计准确性测试
- 缓存一致性测试
- 缓存与数据库同步测试
- 多层缓存协调测试
- 缓存失效策略测试

### 14.3 性能测试

- 大量数据下的查询性能
- 并发操作测试
- 缓存性能测试
- 乐观锁并发冲突性能测试
- 版本历史查询性能测试
- App 端高并发访问测试
- 页面内容加载性能测试
- 访问统计写入性能测试
- CDN 缓存命中率测试
- 多层缓存性能对比测试
- 缓存预热效果测试
- 缓存穿透和雪崩测试
- Redis 集群性能测试

### 14.4 缓存专项测试

#### 14.4.1 缓存功能测试

- 缓存写入和读取正确性
- 缓存过期时间验证
- 缓存清除功能验证
- 缓存预热功能验证
- 注解缓存功能测试
- 自定义缓存注解测试
- 多级缓存协调测试
- 异步缓存更新测试

#### 14.4.2 缓存性能测试

- 缓存命中率测试
- 缓存响应时间测试
- 高并发缓存访问测试
- 缓存内存使用率测试

#### 14.4.3 缓存异常测试

- Redis 连接断开恢复测试
- 缓存数据损坏处理测试
- 缓存雪崩场景测试
- 缓存穿透防护测试

#### 14.4.4 Caffeine + Redis 双层缓存专项测试

##### A. 双层缓存功能测试

- L1缓存命中测试
- L2缓存命中测试
- L1缓存失效后L2回填测试
- 双层缓存数据一致性测试

##### B. 缓存同步测试

- 集群间缓存同步测试
- Redis发布订阅消息测试
- 缓存更新通知测试
- 分布式环境一致性测试

##### C. 性能对比测试

- 单层Redis缓存 vs 双层缓存性能对比
- 不同缓存大小配置的性能测试
- 高并发场景下的缓存性能测试
- 内存使用情况对比测试

##### D. 故障恢复测试

- Redis服务停止时的降级测试
- Caffeine内存不足时的处理测试
- 缓存序列化异常处理测试
- 网络分区情况下的缓存行为测试

##### E. 缓存配置优化测试

- 不同过期时间配置的效果测试
- 缓存大小对命中率的影响测试
- 预热策略效果验证测试
- 缓存清理策略测试

## 15. 常见问题与解决方案

### 15.1 缓存相关问题

**Q1: 双层缓存数据不一致怎么办？**
A:

- 检查缓存同步服务是否正常工作
- 确认Redis发布订阅功能正常
- 可以手动清除L1缓存强制从L2缓存重新加载
- 紧急情况下可以关闭L1缓存，只使用L2缓存

**Q2: 缓存命中率低怎么优化？**
A:

- 检查缓存过期时间设置是否合理
- 分析访问模式，调整缓存预热策略
- 增加L1缓存大小（注意JVM内存限制）
- 优化缓存键的设计，避免缓存碎片

**Q3: Redis连接异常如何处理？**
A:

- 系统会自动降级到数据库查询
- 检查Redis服务状态和网络连接
- 查看连接池配置是否合理
- 启用Redis集群提高可用性

### 15.2 版本管理问题

**Q1: 乐观锁版本冲突频繁怎么办？**
A:

- 检查并发编辑的用户数量
- 考虑增加重试机制
- 优化前端编辑体验，减少保存频率
- 提供版本合并功能

**Q2: 版本历史占用空间过大？**
A:

- 定期清理旧版本（保留最近N个版本）
- 对版本内容进行压缩存储
- 考虑将历史版本迁移到冷存储

### 15.3 性能问题

**Q1: 页面加载速度慢？**
A:

- 检查缓存命中率
- 优化数据库查询索引
- 启用CDN加速
- 压缩页面内容JSON

**Q2: 高并发下系统响应慢？**
A:

- 增加应用实例数量
- 优化数据库连接池配置
- 启用读写分离
- 使用消息队列异步处理

## 16. 升级和维护指南

### 16.1 版本升级

**升级前准备：**

1. 备份数据库和Redis数据
2. 停止所有应用实例
3. 清空所有缓存数据
4. 记录当前配置参数

**升级步骤：**

1. 执行数据库升级脚本
2. 更新应用配置文件
3. 部署新版本应用
4. 验证功能正常性
5. 重新预热缓存

**回滚方案：**

1. 恢复数据库备份
2. 部署旧版本应用
3. 恢复配置文件
4. 重启相关服务

### 16.2 日常维护

**每日检查：**

- 缓存命中率监控
- 系统错误日志检查
- 数据库性能指标
- 磁盘空间使用情况

**每周维护：**

- 清理过期缓存数据
- 分析慢查询日志
- 检查版本历史数据量
- 更新监控告警规则

**每月维护：**

- 数据库性能优化
- 缓存配置调优
- 系统容量规划
- 安全漏洞检查

### 16.3 监控告警

**关键指标：**

- 缓存命中率 < 80%
- 接口响应时间 > 500ms
- 数据库连接数 > 80%
- 内存使用率 > 85%
- 磁盘使用率 > 90%

**告警通知：**

- 邮件通知：非紧急问题
- 短信通知：紧急问题
- 钉钉/企微：团队协作
- 监控大屏：实时展示

## 17. 附录

### 17.1 相关技术文档

- [Spring Cache官方文档](https://docs.spring.io/spring-framework/docs/current/reference/html/integration.html#cache)
- [Caffeine缓存库文档](https://github.com/ben-manes/caffeine)
- [Redis官方文档](https://redis.io/documentation)
- [MyBatis Plus官方文档](https://baomidou.com/)

### 17.2 开发规范

**代码规范：**

- 遵循阿里巴巴Java开发手册
- 使用统一的代码格式化配置
- 必须编写单元测试
- 接口必须添加Swagger注解

**数据库规范：**

- 表名使用下划线命名
- 字段必须添加注释
- 必须设置合适的索引
- 遵循数据库设计三范式

**缓存使用规范：**

- 缓存键命名要有意义
- 设置合理的过期时间
- 及时清理相关缓存
- 处理缓存异常情况

### 17.3 部署清单

**环境要求：**

- JDK 8+
- MySQL 5.7+
- Redis 5.0+
- 内存 4GB+
- 磁盘 50GB+

**配置文件：**

- application.yml
- application-prod.yml
- logback-spring.xml
- mcp.json

**启动脚本：**

- start.sh
- stop.sh
- restart.sh
- health-check.sh

---

**文档版本**: v1.0  
**创建时间**: 2025-01-08  
**最后更新**: 2025-01-08  
**作者**: 系统架构师
