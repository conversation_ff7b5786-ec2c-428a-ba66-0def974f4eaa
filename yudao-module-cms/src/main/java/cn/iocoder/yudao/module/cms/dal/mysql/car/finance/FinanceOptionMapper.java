package cn.iocoder.yudao.module.cms.dal.mysql.car.finance;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.finance.FinanceOptionDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 融资选项 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface FinanceOptionMapper extends BaseMapperX<FinanceOptionDO> {

    default PageResult<FinanceOptionDO> selectPage(String name, Integer status,
                                                    Integer pageNo, Integer pageSize) {
        return selectPage(new PageParam().setPageNo(pageNo).setPageSize(pageSize),new LambdaQueryWrapperX<FinanceOptionDO>()
                .likeIfPresent(FinanceOptionDO::getName, name)
                .eqIfPresent(FinanceOptionDO::getStatus, status)
                .orderByAsc(FinanceOptionDO::getSortOrder)
                .orderByDesc(FinanceOptionDO::getId));
    }

    default FinanceOptionDO selectByCode(String optionCode) {
        return selectOne(FinanceOptionDO::getOptionCode, optionCode);
    }

    default List<FinanceOptionDO> selectListByStatus(Integer status) {
        return selectList(new LambdaQueryWrapperX<FinanceOptionDO>()
                .eqIfPresent(FinanceOptionDO::getStatus, status)
                .orderByAsc(FinanceOptionDO::getSortOrder));
    }

}