package cn.iocoder.yudao.module.cms.framework.cache.core;

import lombok.Data;

/**
 * 缓存统计信息
 *
 * <AUTHOR>
 */
@Data
public class CacheStats {

    /**
     * 缓存名称
     */
    private String cacheName;

    /**
     * L1 缓存命中次数
     */
    private Long l1HitCount = 0L;

    /**
     * L1 缓存未命中次数
     */
    private Long l1MissCount = 0L;

    /**
     * L1 缓存命中率
     */
    private Double l1HitRate = 0.0;

    /**
     * L1 缓存驱逐次数
     */
    private Long l1EvictionCount = 0L;

    /**
     * L2 缓存命中次数
     */
    private Long l2HitCount = 0L;

    /**
     * L2 缓存未命中次数
     */
    private Long l2MissCount = 0L;

    /**
     * L2 缓存命中率
     */
    private Double l2HitRate = 0.0;

    /**
     * 总命中次数
     */
    public Long getTotalHitCount() {
        return l1HitCount + l2HitCount;
    }

    /**
     * 总未命中次数
     */
    public Long getTotalMissCount() {
        return l1MissCount + l2MissCount;
    }

    /**
     * 总命中率
     */
    public Double getTotalHitRate() {
        long totalRequests = getTotalHitCount() + getTotalMissCount();
        if (totalRequests == 0) {
            return 0.0;
        }
        return (double) getTotalHitCount() / totalRequests;
    }

    /**
     * L1 缓存请求总数
     */
    public Long getL1RequestCount() {
        return l1HitCount + l1MissCount;
    }

    /**
     * L2 缓存请求总数
     */
    public Long getL2RequestCount() {
        return l2HitCount + l2MissCount;
    }

    /**
     * 总请求数
     */
    public Long getTotalRequestCount() {
        return getTotalHitCount() + getTotalMissCount();
    }
}