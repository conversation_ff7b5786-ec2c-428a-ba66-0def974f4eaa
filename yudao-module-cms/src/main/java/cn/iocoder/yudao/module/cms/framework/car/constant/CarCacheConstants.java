package cn.iocoder.yudao.module.cms.framework.car.constant;

/**
 * 车型管理缓存常量
 * 
 * 使用yudao框架的TimeoutRedisCacheManager实现
 * 缓存名称格式：cacheName#ttl
 * ttl单位：d(天)、h(小时)、m(分钟)、s(秒)，默认s(秒)
 * 
 * <AUTHOR>
 */
public interface CarCacheConstants {
    
    /**
     * 车系缓存 - 过期时间2小时
     */
    String CAR_SERIES = "car:series#2h";
    
    /**
     * 车系列表缓存 - 过期时间1小时
     */
    String CAR_SERIES_LIST = "car:series:list#1h";
    
    /**
     * 车型缓存 - 过期时间1小时
     */
    String CAR_MODEL = "car:model#1h";
    
    /**
     * 车型列表缓存（按车系）- 过期时间30分钟
     */
    String CAR_MODEL_LIST_BY_SERIES = "car:model:series#30m";
    
    /**
     * 车型列表缓存（按价格范围）- 过期时间30分钟
     */
    String CAR_MODEL_LIST_BY_PRICE = "car:model:price#30m";
    
    /**
     * 热门车型列表缓存 - 过期时间10分钟
     */
    String CAR_MODEL_HOT_LIST = "car:model:hot#10m";
    
    /**
     * 车型配置选项缓存 - 过期时间30分钟
     */
    String CAR_MODEL_OPTIONS = "car:model:options#30m";
    
    /**
     * 车型规格缓存 - 过期时间1小时
     */
    String CAR_MODEL_SPEC = "car:model:spec#1h";
    
    /**
     * 车型套餐缓存 - 过期时间30分钟
     */
    String CAR_PACKAGE = "car:package#30m";
    
    /**
     * 配置选项类型缓存 - 过期时间2小时
     */
    String CAR_OPTION_TYPE = "car:option:type#2h";
    
    /**
     * 融资方案缓存 - 过期时间4小时
     */
    String CAR_FINANCE_PLAN = "car:finance:plan#4h";
    
    /**
     * 融资方案计算结果缓存 - 过期时间5分钟
     */
    String CAR_FINANCE_CALC = "car:finance:calc#5m";
    
    /**
     * 首付选项缓存 - 过期时间2小时
     */
    String CAR_DOWN_PAYMENT = "car:down:payment#2h";
    
    /**
     * 融资期限缓存 - 过期时间2小时
     */
    String CAR_FINANCE_TERM = "car:finance:term#2h";
    
    // ===== API SQL配置缓存 =====
    
    /**
     * API SQL配置缓存 - 过期时间2小时
     */
    String API_SQL_CONFIG = "api:sql:config#2h";
    
    /**
     * API SQL配置列表缓存 - 过期时间1小时
     */
    String API_SQL_CONFIG_LIST = "api:sql:config:list#1h";
    
    /**
     * API代码列表缓存 - 过期时间30分钟
     */
    String API_SQL_CONFIG_CODES = "api:sql:config:codes#30m";
    
    /**
     * API版本列表缓存 - 过期时间30分钟
     */
    String API_SQL_CONFIG_VERSIONS = "api:sql:config:versions#30m";
    
    /**
     * API默认配置缓存 - 过期时间1小时
     */
    String API_SQL_CONFIG_DEFAULT = "api:sql:config:default#1h";
    
    /**
     * API参数列表缓存 - 过期时间10分钟
     */
    String API_SQL_CONFIG_PARAMS = "api:sql:config:params#10m";
    
    /**
     * API执行结果缓存 - 过期时间5分钟
     */
    String API_SQL_RESULT = "api:sql:result#5m";
}