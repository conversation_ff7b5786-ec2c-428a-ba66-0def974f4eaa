package cn.iocoder.yudao.module.cms.dal.mysql.car;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarPackagePageReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarPackageDO;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.List;

/**
 * 配置包 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CarPackageMapper extends BaseMapperX<CarPackageDO> {

    default PageResult<CarPackageDO> selectPage(CarPackagePageReqVO pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<CarPackageDO>()
                .likeIfPresent(CarPackageDO::getPackageCode, pageReqVO.getPackageCode())
                .likeIfPresent(CarPackageDO::getName, pageReqVO.getName())
                .geIfPresent(CarPackageDO::getPrice, pageReqVO.getMinPrice())
                .leIfPresent(CarPackageDO::getPrice, pageReqVO.getMaxPrice())
//                .eqIfPresent(CarPackageDO::getIsHot, pageReqVO.getIsHot())
                .eqIfPresent(CarPackageDO::getStatus, pageReqVO.getStatus())
                .orderByAsc(CarPackageDO::getSortOrder)
                .orderByDesc(CarPackageDO::getId));
    }

    default CarPackageDO selectByCode(String code) {
        return selectOne(CarPackageDO::getPackageCode, code);
    }

    default List<CarPackageDO> selectListByStatus(Integer status) {
        return selectList(new LambdaQueryWrapperX<CarPackageDO>()
                .eqIfPresent(CarPackageDO::getStatus, status)
                .orderByAsc(CarPackageDO::getSortOrder)
                .orderByDesc(CarPackageDO::getId));
    }

    default List<CarPackageDO> selectListByPriceRange(BigDecimal minPrice, BigDecimal maxPrice, Integer status) {
        return selectList(new LambdaQueryWrapperX<CarPackageDO>()
                .geIfPresent(CarPackageDO::getPrice, minPrice)
                .leIfPresent(CarPackageDO::getPrice, maxPrice)
                .eqIfPresent(CarPackageDO::getStatus, status)
                .orderByAsc(CarPackageDO::getPrice)
                .orderByAsc(CarPackageDO::getSortOrder));
    }

    default List<CarPackageDO> selectHotCarPackageList(Integer limit) {
        return selectList(new LambdaQueryWrapperX<CarPackageDO>()
//                .eq(CarPackageDO::getIsHot, true)
                .eq(CarPackageDO::getStatus, 1)
                .orderByAsc(CarPackageDO::getSortOrder)
                .orderByDesc(CarPackageDO::getId)
                .last("LIMIT " + (limit != null ? limit : 10)));
    }

    default List<CarPackageDO> selectListByModelId(Long modelId) {
        return selectList(new LambdaQueryWrapperX<CarPackageDO>()
                .eq(CarPackageDO::getModelId, modelId)
                .eq(CarPackageDO::getStatus, 1)
                .orderByAsc(CarPackageDO::getSortOrder)
                .orderByDesc(CarPackageDO::getId));
    }

}