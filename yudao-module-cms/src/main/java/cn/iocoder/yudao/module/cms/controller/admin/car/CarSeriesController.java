package cn.iocoder.yudao.module.cms.controller.admin.car;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarSeriesCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarSeriesPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarSeriesRespVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarSeriesUpdateReqVO;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarSeriesDO;
import cn.iocoder.yudao.module.cms.service.car.CarSeriesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 车系管理")
@RestController
@RequestMapping("/cms/car/series")
@Validated
public class CarSeriesController {

    @Resource
    private CarSeriesService carSeriesService;

    @PostMapping("/create")
    @Operation(summary = "创建车系")
    @PreAuthorize("@ss.hasPermission('cms:car-series:create')")
    public CommonResult<Long> createCarSeries(@Valid @RequestBody CarSeriesCreateReqVO createReqVO) {
        return success(carSeriesService.createCarSeries(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新车系")
    @PreAuthorize("@ss.hasPermission('cms:car-series:update')")
    public CommonResult<Boolean> updateCarSeries(@Valid @RequestBody CarSeriesUpdateReqVO updateReqVO) {
        carSeriesService.updateCarSeries(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除车系")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('cms:car-series:delete')")
    public CommonResult<Boolean> deleteCarSeries(@RequestParam("id") Long id) {
        carSeriesService.deleteCarSeries(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得车系详情")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('cms:car-series:query')")
    public CommonResult<CarSeriesRespVO> getCarSeries(@RequestParam("id") Long id) {
        CarSeriesDO carSeries = carSeriesService.getCarSeries(id);
        return success(BeanUtils.toBean(carSeries, CarSeriesRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得车系分页")
    @PreAuthorize("@ss.hasPermission('cms:car-series:query')")
    public CommonResult<PageResult<CarSeriesRespVO>> getCarSeriesPage(@Valid CarSeriesPageReqVO pageReqVO) {
        PageResult<CarSeriesDO> pageResult = carSeriesService.getCarSeriesPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CarSeriesRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得车系列表")
    @Parameter(name = "brand", description = "品牌标识", example = "CHERY")
    @Parameter(name = "status", description = "状态", example = "1")
    @PreAuthorize("@ss.hasPermission('cms:car-series:query')")
    public CommonResult<List<CarSeriesRespVO>> getCarSeriesList(@RequestParam(value = "brand", required = false) String brand,
                                                               @RequestParam(value = "status", required = false) Integer status) {
        List<CarSeriesDO> list = carSeriesService.getCarSeriesList(brand, status);
        return success(BeanUtils.toBean(list, CarSeriesRespVO.class));
    }

    @GetMapping("/get-by-code")
    @Operation(summary = "根据编码获取车系")
    @Parameter(name = "code", description = "车系编码", required = true, example = "TIGGO8_PRO")
    @PreAuthorize("@ss.hasPermission('cms:car-series:query')")
    public CommonResult<CarSeriesRespVO> getCarSeriesByCode(@RequestParam("code") String code) {
        CarSeriesDO carSeries = carSeriesService.getCarSeriesByCode(code);
        return success(BeanUtils.toBean(carSeries, CarSeriesRespVO.class));
    }

}