package cn.iocoder.yudao.module.cms.dal.mysql.dealer;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.cms.controller.admin.dealer.vo.DealerPageReqVO;
import cn.iocoder.yudao.module.cms.controller.app.dealer.vo.AppDealerSearchReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.dealer.DealerDO;
import cn.iocoder.yudao.module.cms.dal.dataobject.dealer.DealerWithDistanceDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * CMS经销商 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DealerMapper extends BaseMapperX<DealerDO> {

    default PageResult<DealerDO> selectPage(DealerPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DealerDO>()
                .likeIfPresent(DealerDO::getName, reqVO.getName())
                .likeIfPresent(DealerDO::getPostcode, reqVO.getPostcode())
                .eqIfPresent(DealerDO::getRegion, reqVO.getRegion())
                .eqIfPresent(DealerDO::getStatus, reqVO.getStatus())
                .orderByDesc(DealerDO::getId));
    }

    default List<DealerDO> selectList(String region, Integer status) {
        return selectList(new LambdaQueryWrapperX<DealerDO>()
                .eqIfPresent(DealerDO::getRegion, region)
                .eqIfPresent(DealerDO::getStatus, status));
    }

    default PageResult<DealerWithDistanceDO> selectPage(AppDealerSearchReqVO reqVO) {
        int offset = (reqVO.getPageNo() - 1) * reqVO.getPageSize();
        
        List<DealerWithDistanceDO> list = searchDealersUnified(
            reqVO.getName(),
            reqVO.getRegion(),
            reqVO.getPostcode(),
            reqVO.getServices(),
            reqVO.getLatitude(),
            reqVO.getLongitude(),
            offset,
            reqVO.getPageSize()
        );
        
        Long total = countDealersUnified(
            reqVO.getName(),
            reqVO.getRegion(),
            reqVO.getPostcode(),
            reqVO.getServices(),
            reqVO.getLatitude(),
            reqVO.getLongitude()
        );
        
        return new PageResult<>(list, total);
    }

    List<DealerWithDistanceDO> searchDealersUnified(@Param("name") String name,
                                                   @Param("region") String region,
                                                   @Param("postcode") String postcode,
                                                   @Param("services") List<String> services,
                                                   @Param("centerLat") Double centerLat,
                                                   @Param("centerLng") Double centerLng,
                                                   @Param("offset") Integer offset,
                                                   @Param("pageSize") Integer pageSize);

    Long countDealersUnified(@Param("name") String name,
                           @Param("region") String region,
                           @Param("postcode") String postcode,
                           @Param("services") List<String> services,
                           @Param("centerLat") Double centerLat,
                           @Param("centerLng") Double centerLng);


    List<DealerWithDistanceDO> selectNearbyDealers(@Param("lat") Double lat,
                                                  @Param("lng") Double lng, 
                                                  @Param("radiusMeters") Integer radiusMeters);

}