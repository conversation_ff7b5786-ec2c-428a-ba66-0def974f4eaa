package cn.iocoder.yudao.module.cms.controller.admin.diy;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.cms.controller.admin.diy.vo.page.*;
import cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageDO;
import cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageVersionDO;
import cn.iocoder.yudao.module.cms.dal.dataobject.menu.CmsMenuDO;
import cn.iocoder.yudao.module.cms.dal.mysql.diy.CmsDiyPageMapper;
import cn.iocoder.yudao.module.cms.dal.mysql.diy.CmsDiyPageVersionMapper;
import cn.iocoder.yudao.module.cms.dal.mysql.menu.CmsMenuMapper;
import cn.iocoder.yudao.module.cms.enums.diy.CmsDiyPageStatusEnum;
import cn.iocoder.yudao.module.cms.enums.menu.CmsMenuStatusEnum;
import cn.iocoder.yudao.module.cms.framework.cache.core.CmsCacheService;
import cn.iocoder.yudao.module.cms.service.diy.CmsDiyPageService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.dao.OptimisticLockingFailureException;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
class CmsDiyPageControllerIntegrationTest extends BaseDbUnitTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private CmsDiyPageService diyPageService;

    @Autowired
    private CmsDiyPageMapper diyPageMapper;

    @Autowired
    private CmsDiyPageVersionMapper versionMapper;

    @Autowired
    private CmsMenuMapper menuMapper;

    @MockBean
    private CmsCacheService cacheService;

    @Autowired
    private ObjectMapper objectMapper;

    private CmsMenuDO testMenu;
    private CmsDiyPageDO testPage;

    @BeforeEach
    void setUp() {
        // 创建测试菜单
        testMenu = new CmsMenuDO();
        testMenu.setName("测试菜单");
        testMenu.setPath("/test-menu");
        testMenu.setStatus(CmsMenuStatusEnum.ENABLED.getStatus());
        testMenu.setCreateTime(LocalDateTime.now());
        testMenu.setUpdateTime(LocalDateTime.now());
        menuMapper.insert(testMenu);

        // 创建测试页面
        testPage = new CmsDiyPageDO();
        testPage.setName("测试页面");
        testPage.setPath("/test-page");
        testPage.setMenuId(testMenu.getId());
        testPage.setUuid(UUID.randomUUID().toString());
        testPage.setContent("{\"components\": [{\"type\": \"text\", \"content\": \"Hello World\"}]}");
        testPage.setStatus(CmsDiyPageStatusEnum.DRAFT.getStatus());
        testPage.setVersion(1);
        testPage.setCreateTime(LocalDateTime.now());
        testPage.setUpdateTime(LocalDateTime.now());
        diyPageMapper.insert(testPage);
    }

    @Test
    @WithMockUser(authorities = {"cms:diy-page:create"})
    void testCreateDiyPage_Success() throws Exception {
        // Given
        CmsDiyPageCreateReqVO createReqVO = new CmsDiyPageCreateReqVO();
        createReqVO.setName("新页面");
        createReqVO.setPath("/new-page");
        createReqVO.setMenuId(testMenu.getId());
        createReqVO.setContent("{\"components\": []}");
        createReqVO.setRemark("创建测试");

        // When & Then
        MvcResult result = mockMvc.perform(post("/cms/diy-page/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createReqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data").isNumber())
                .andReturn();

        // Verify
        String responseContent = result.getResponse().getContentAsString();
        CommonResult<Long> response = objectMapper.readValue(responseContent, 
            objectMapper.getTypeFactory().constructParametricType(CommonResult.class, Long.class));
        
        Long pageId = response.getData();
        assertNotNull(pageId);

        CmsDiyPageDO createdPage = diyPageMapper.selectById(pageId);
        assertNotNull(createdPage);
        assertEquals("新页面", createdPage.getName());
        assertEquals("/test-menu/new-page", createdPage.getPath());
    }

    @Test
    @WithMockUser(authorities = {"cms:diy-page:create"})
    void testCreateDiyPage_DuplicatePath() throws Exception {
        // Given - 使用已存在的路径
        CmsDiyPageCreateReqVO createReqVO = new CmsDiyPageCreateReqVO();
        createReqVO.setName("重复路径页面");
        createReqVO.setPath("/test-page");
        createReqVO.setMenuId(testMenu.getId());
        createReqVO.setContent("{\"components\": []}");

        // When & Then
        mockMvc.perform(post("/cms/diy-page/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createReqVO)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser(authorities = {"cms:diy-page:update"})
    void testUpdateDiyPage_Success() throws Exception {
        // Given
        CmsDiyPageUpdateReqVO updateReqVO = new CmsDiyPageUpdateReqVO();
        updateReqVO.setId(testPage.getId());
        updateReqVO.setName("更新页面");
        updateReqVO.setContent("{\"components\": [{\"type\": \"text\", \"content\": \"Updated\"}]}");
        updateReqVO.setVersion(testPage.getVersion());

        // When & Then
        mockMvc.perform(put("/cms/diy-page/update")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateReqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0));

        // Verify
        CmsDiyPageDO updatedPage = diyPageMapper.selectById(testPage.getId());
        assertNotNull(updatedPage);
        assertEquals("更新页面", updatedPage.getName());
        assertEquals(2, updatedPage.getVersion()); // 版本应该递增
    }

    @Test
    @WithMockUser(authorities = {"cms:diy-page:update"})
    void testUpdateDiyPage_OptimisticLockFailure() throws Exception {
        // Given - 使用过时的版本号
        CmsDiyPageUpdateReqVO updateReqVO = new CmsDiyPageUpdateReqVO();
        updateReqVO.setId(testPage.getId());
        updateReqVO.setName("冲突更新");
        updateReqVO.setVersion(999); // 错误的版本号

        // When & Then
        mockMvc.perform(put("/cms/diy-page/update")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateReqVO)))
                .andExpect(status().isConflict()); // 409冲突状态
    }

    @Test
    @WithMockUser(authorities = {"cms:diy-page:publish"})
    void testPublishDiyPage_Success() throws Exception {
        // Given
        CmsDiyPagePublishReqVO publishReqVO = new CmsDiyPagePublishReqVO();
        publishReqVO.setId(testPage.getId());
        publishReqVO.setVersion(testPage.getVersion());

        // When & Then
        mockMvc.perform(put("/cms/diy-page/publish")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(publishReqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0));

        // Verify
        CmsDiyPageDO publishedPage = diyPageMapper.selectById(testPage.getId());
        assertNotNull(publishedPage);
        assertEquals(CmsDiyPageStatusEnum.PUBLISHED.getStatus(), publishedPage.getStatus());
        assertNotNull(publishedPage.getPublishTime());

        // Verify version created
        CmsDiyPageVersionDO version = versionMapper.selectLatestByPageId(testPage.getId());
        assertNotNull(version);
        assertEquals(testPage.getContent(), version.getContent());
    }

    @Test
    @WithMockUser(authorities = {"cms:diy-page:offline"})
    void testOfflineDiyPage_Success() throws Exception {
        // Given - 先发布页面
        testPage.setStatus(CmsDiyPageStatusEnum.PUBLISHED.getStatus());
        testPage.setPublishTime(LocalDateTime.now());
        diyPageMapper.updateById(testPage);

        // When & Then
        mockMvc.perform(put("/cms/diy-page/offline/" + testPage.getId())
                .param("version", testPage.getVersion().toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0));

        // Verify
        CmsDiyPageDO offlinePage = diyPageMapper.selectById(testPage.getId());
        assertNotNull(offlinePage);
        assertEquals(CmsDiyPageStatusEnum.OFFLINE.getStatus(), offlinePage.getStatus());
        assertNotNull(offlinePage.getOfflineTime());
    }

    @Test
    @WithMockUser(authorities = {"cms:diy-page:delete"})
    void testDeleteDiyPage_Success() throws Exception {
        // When & Then
        mockMvc.perform(delete("/cms/diy-page/delete")
                .param("id", testPage.getId().toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0));

        // Verify
        CmsDiyPageDO deletedPage = diyPageMapper.selectById(testPage.getId());
        assertNull(deletedPage);
    }

    @Test
    @WithMockUser(authorities = {"cms:diy-page:delete"})
    void testDeleteDiyPage_PublishedPage() throws Exception {
        // Given - 发布页面
        testPage.setStatus(CmsDiyPageStatusEnum.PUBLISHED.getStatus());
        diyPageMapper.updateById(testPage);

        // When & Then
        mockMvc.perform(delete("/cms/diy-page/delete")
                .param("id", testPage.getId().toString()))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser(authorities = {"cms:diy-page:query"})
    void testGetDiyPage_Success() throws Exception {
        // When & Then
        mockMvc.perform(get("/cms/diy-page/get")
                .param("id", testPage.getId().toString()))
                .andExpected(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.id").value(testPage.getId()))
                .andExpect(jsonPath("$.data.name").value("测试页面"));
    }

    @Test
    @WithMockUser(authorities = {"cms:diy-page:query"})
    void testGetDiyPagePage_Success() throws Exception {
        // When & Then
        mockMvc.perform(get("/cms/diy-page/page")
                .param("pageNo", "1")
                .param("pageSize", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.total").value(1))
                .andExpect(jsonPath("$.data.list[0].name").value("测试页面"));
    }

    @Test
    @WithMockUser(authorities = {"cms:diy-page:query"})
    void testGetDiyPagePage_WithFilter() throws Exception {
        // When & Then
        mockMvc.perform(get("/cms/diy-page/page")
                .param("pageNo", "1")
                .param("pageSize", "10")
                .param("name", "测试")
                .param("status", CmsDiyPageStatusEnum.DRAFT.getStatus().toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.total").value(1));
    }

    @Test
    void testConcurrentUpdate() throws InterruptedException {
        // Given
        int threadCount = 5;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch endLatch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger conflictCount = new AtomicInteger(0);

        // When - 模拟并发更新
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    startLatch.await(); // 等待所有线程就绪

                    CmsDiyPageUpdateReqVO updateReqVO = new CmsDiyPageUpdateReqVO();
                    updateReqVO.setId(testPage.getId());
                    updateReqVO.setName("并发更新-" + threadId);
                    updateReqVO.setVersion(testPage.getVersion());

                    try {
                        diyPageService.updateDiyPage(updateReqVO);
                        successCount.incrementAndGet();
                    } catch (OptimisticLockingFailureException e) {
                        conflictCount.incrementAndGet();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    endLatch.countDown();
                }
            });
        }

        startLatch.countDown(); // 启动所有线程
        endLatch.await(); // 等待所有线程完成

        // Then - 应该只有一个成功，其他的都冲突
        assertEquals(1, successCount.get());
        assertEquals(threadCount - 1, conflictCount.get());

        executor.shutdown();
    }

    @Test
    void testCompleteWorkflow() {
        // Given
        Long pageId = testPage.getId();

        // When & Then - 完整流程测试
        
        // 1. 创建页面 (已在setUp中完成)
        CmsDiyPageDO createdPage = diyPageMapper.selectById(pageId);
        assertNotNull(createdPage);
        assertEquals(CmsDiyPageStatusEnum.DRAFT.getStatus(), createdPage.getStatus());

        // 2. 编辑页面
        CmsDiyPageUpdateReqVO updateReqVO = new CmsDiyPageUpdateReqVO();
        updateReqVO.setId(pageId);
        updateReqVO.setName("编辑后页面");
        updateReqVO.setContent("{\"components\": [{\"type\": \"text\", \"content\": \"Updated Content\"}]}");
        updateReqVO.setVersion(createdPage.getVersion());

        diyPageService.updateDiyPage(updateReqVO);

        CmsDiyPageDO updatedPage = diyPageMapper.selectById(pageId);
        assertEquals("编辑后页面", updatedPage.getName());
        assertEquals(2, updatedPage.getVersion());

        // 3. 发布页面
        CmsDiyPagePublishReqVO publishReqVO = new CmsDiyPagePublishReqVO();
        publishReqVO.setId(pageId);
        publishReqVO.setVersion(updatedPage.getVersion());

        diyPageService.publishDiyPage(publishReqVO);

        CmsDiyPageDO publishedPage = diyPageMapper.selectById(pageId);
        assertEquals(CmsDiyPageStatusEnum.PUBLISHED.getStatus(), publishedPage.getStatus());
        assertNotNull(publishedPage.getPublishTime());

        // 4. 验证版本创建
        CmsDiyPageVersionDO version = versionMapper.selectLatestByPageId(pageId);
        assertNotNull(version);
        assertEquals("编辑后页面", version.getName());

        // 5. 下线页面
        diyPageService.offlineDiyPage(pageId, publishedPage.getVersion());

        CmsDiyPageDO offlinePage = diyPageMapper.selectById(pageId);
        assertEquals(CmsDiyPageStatusEnum.OFFLINE.getStatus(), offlinePage.getStatus());
        assertNotNull(offlinePage.getOfflineTime());
    }

    @Test
    void testPageAccessAndCaching() {
        // Given - 发布页面
        testPage.setStatus(CmsDiyPageStatusEnum.PUBLISHED.getStatus());
        testPage.setPublishTime(LocalDateTime.now());
        diyPageMapper.updateById(testPage);

        // When & Then - 测试应用端访问
        CmsDiyPageDO publishedPage = diyPageService.getPublishedPageByPath(testPage.getPath());
        assertNotNull(publishedPage);
        assertEquals(testPage.getId(), publishedPage.getId());

        // 测试UUID访问
        CmsDiyPageDO pageByUuid = diyPageService.getPublishedPageByUuid(testPage.getUuid());
        assertNotNull(pageByUuid);
        assertEquals(testPage.getId(), pageByUuid.getId());

        // 测试已发布页面列表
        var publishedPages = diyPageService.getPublishedPages();
        assertFalse(publishedPages.isEmpty());
        assertTrue(publishedPages.stream().anyMatch(p -> p.getId().equals(testPage.getId())));
    }

    @Test
    void testErrorScenarios() {
        // 测试页面不存在
        assertThrows(RuntimeException.class, () -> diyPageService.getDiyPage(999999L));

        // 测试版本冲突
        CmsDiyPageUpdateReqVO conflictReqVO = new CmsDiyPageUpdateReqVO();
        conflictReqVO.setId(testPage.getId());
        conflictReqVO.setVersion(999); // 错误版本
        conflictReqVO.setName("冲突更新");

        assertThrows(OptimisticLockingFailureException.class, () -> 
            diyPageService.updateDiyPage(conflictReqVO));

        // 测试删除已发布页面
        testPage.setStatus(CmsDiyPageStatusEnum.PUBLISHED.getStatus());
        diyPageMapper.updateById(testPage);

        assertThrows(RuntimeException.class, () -> diyPageService.deleteDiyPage(testPage.getId()));
    }
}