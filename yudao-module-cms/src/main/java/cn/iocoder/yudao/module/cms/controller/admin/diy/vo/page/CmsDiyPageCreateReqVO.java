package cn.iocoder.yudao.module.cms.controller.admin.diy.vo.page;

import cn.iocoder.yudao.module.cms.framework.validation.annotation.ValidJsonContent;
import cn.iocoder.yudao.module.cms.framework.validation.annotation.ValidPath;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;


/**
 * CMS DIY页面创建 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - CMS DIY页面创建 Request VO")
@Data
public class CmsDiyPageCreateReqVO {

    @Schema(description = "页面名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "首页")
    @NotBlank(message = "页面名称不能为空")
    @Size(max = 100, message = "页面名称长度不能超过100个字符")
    @Pattern(regexp = "^[^<>\"'&]*$", message = "页面名称不能包含特殊字符")
    private String name;

    @Schema(description = "关联菜单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "关联菜单ID不能为空")
    private Long menuId;

    @Schema(description = "上级页面ID", example = "1025")
    private Long parentId;

    @Schema(description = "页面路径", requiredMode = Schema.RequiredMode.REQUIRED, example = "index")
    @NotBlank(message = "页面路径不能为空")
    @Size(max = 200, message = "页面路径长度不能超过200个字符")
    @ValidPath(message = "页面路径格式不正确，只允许字母、数字、中划线和下划线")
    private String path;

    @Schema(description = "关键词", example = "首页,主页")
    @Size(max = 200, message = "关键词长度不能超过200个字符")
    @Pattern(regexp = "^[^<>\"']*$", message = "关键词不能包含HTML标签")
    private String keywords;

    @Schema(description = "页面描述", example = "这是网站首页")
    @Size(max = 500, message = "页面描述长度不能超过500个字符")
    @Pattern(regexp = "^[^<>]*$", message = "页面描述不能包含HTML标签")
    private String description;

    @Schema(description = "页面内容JSON", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "页面内容不能为空")
    @ValidJsonContent(message = "页面内容必须是有效的JSON格式，且不能超过5MB")
    private String content;
}