package cn.iocoder.yudao.module.cms.controller.admin.menu;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.controller.admin.menu.vo.CmsMenuListReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.menu.vo.CmsMenuRespVO;
import cn.iocoder.yudao.module.cms.controller.admin.menu.vo.CmsMenuSaveReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.menu.CmsMenuDO;
import cn.iocoder.yudao.module.cms.service.menu.CmsMenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * CMS菜单管理 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - CMS菜单管理")
@RestController
@RequestMapping("/cms/menu")
@Validated
@Slf4j
public class CmsMenuController {

    @Resource
    private CmsMenuService menuService;

    @PostMapping("/create")
    @Operation(summary = "创建菜单")
    @PreAuthorize("@ss.hasPermission('cms:menu:create')")
    public CommonResult<Long> createMenu(@Valid @RequestBody CmsMenuSaveReqVO createReqVO) {
        return success(menuService.createMenu(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新菜单")
    @PreAuthorize("@ss.hasPermission('cms:menu:update')")
    public CommonResult<Boolean> updateMenu(@Valid @RequestBody CmsMenuSaveReqVO updateReqVO) {
        menuService.updateMenu(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除菜单")
    @Parameter(name = "id", description = "菜单编号", required = true)
    @PreAuthorize("@ss.hasPermission('cms:menu:delete')")
    public CommonResult<Boolean> deleteMenu(@RequestParam("id") Long id) {
        menuService.deleteMenu(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得菜单")
    @Parameter(name = "id", description = "菜单编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('cms:menu:query')")
    public CommonResult<CmsMenuRespVO> getMenu(@RequestParam("id") Long id) {
        CmsMenuDO menu = menuService.getMenu(id);
        return success(BeanUtils.toBean(menu, CmsMenuRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得菜单列表")
    @PreAuthorize("@ss.hasPermission('cms:menu:query')")
    public CommonResult<List<CmsMenuRespVO>> getMenuList(@Valid CmsMenuListReqVO listReqVO) {
        List<CmsMenuDO> list = menuService.getMenuList(listReqVO);
        return success(BeanUtils.toBean(list, CmsMenuRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得菜单分页")
    @PreAuthorize("@ss.hasPermission('cms:menu:query')")
    public CommonResult<PageResult<CmsMenuRespVO>> getMenuPage(@Valid CmsMenuListReqVO pageVO) {
        PageResult<CmsMenuDO> pageResult = menuService.getMenuPage(pageVO);
        return success(BeanUtils.toBean(pageResult, CmsMenuRespVO.class));
    }

    @GetMapping("/tree")
    @Operation(summary = "获得菜单树")
    @PreAuthorize("@ss.hasPermission('cms:menu:query')")
    public CommonResult<List<CmsMenuRespVO>> getMenuTree(@Valid CmsMenuListReqVO listReqVO) {
        List<CmsMenuRespVO> tree = menuService.getMenuTree(listReqVO);
        return success(tree);
    }

    @GetMapping("/simple-tree")
    @Operation(summary = "获得简单菜单树（用于下拉选择）")
    @PreAuthorize("@ss.hasPermission('cms:menu:query')")
    public CommonResult<List<CmsMenuRespVO>> getSimpleMenuTree() {
        List<CmsMenuRespVO> tree = menuService.getMenuTree(new CmsMenuListReqVO());
        return success(tree);
    }

    @PostMapping("/refresh-cache")
    @Operation(summary = "刷新菜单缓存")
    @PreAuthorize("@ss.hasPermission('cms:menu:update')")
    public CommonResult<Boolean> refreshMenuCache() {
        menuService.clearMenuCache();
        menuService.warmupMenuCache();
        return success(true);
    }

    @GetMapping("/build-path")
    @Operation(summary = "构建菜单路径")
    @Parameter(name = "id", description = "菜单编号", required = true)
    @PreAuthorize("@ss.hasPermission('cms:menu:query')")
    public CommonResult<String> buildMenuPath(@RequestParam("id") Long id) {
        String path = menuService.buildMenuPath(id);
        return success(path);
    }

    @GetMapping("/validate-path")
    @Operation(summary = "校验菜单路径唯一性")
    @Parameter(name = "path", description = "菜单路径", required = true)
    @Parameter(name = "id", description = "菜单编号（更新时传入）", required = false)
    @PreAuthorize("@ss.hasPermission('cms:menu:query')")
    public CommonResult<Boolean> validatePathUnique(@RequestParam("path") String path,
                                                     @RequestParam(value = "id", required = false) Long id) {
        menuService.validatePathUnique(id, path);
        return success(true);
    }
}