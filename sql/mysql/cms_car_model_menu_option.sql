-- ===================================
-- 车型配置选项管理菜单 SQL 脚本
-- 版本: v1.0
-- 创建时间: 2025-01-09
-- 说明: 为 Week 8 配置选项管理界面创建菜单和权限
-- ===================================

-- 菜单ID分配说明:
-- 5180-5199: 配置选项管理相关菜单
-- 5180: 配置选项管理 (一级菜单)
-- 5181: 选项类型管理 (二级菜单)
-- 5182-5186: 选项类型管理功能权限
-- 5187: 选项实例管理 (二级菜单) 
-- 5188-5195: 选项实例管理功能权限

-- ===================================
-- 1. Configuration Options Management (一级菜单)
-- ===================================
INSERT INTO system_menu (
    id, name, permission, type, sort, parent_id,
    path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator,
    create_time, updater, update_time, deleted
) VALUES (
    5180, 'Configuration Options', '', 1, 80, 5013,
    'option', 'ep:setting', '', '', 0,
    true, true, true, '1',
    '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false
);

-- ===================================
-- 2. Option Type Management (二级菜单)
-- ===================================
INSERT INTO system_menu (
    id, name, permission, type, sort, parent_id,
    path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator,
    create_time, updater, update_time, deleted
) VALUES (
    5181, 'Option Types', 'cms:car-option-type:query', 2, 1, 5180,
    'type', 'ep:collection-tag', 'cms/carmodel/option/type/index', 'CmsCarOptionType', 0,
    true, true, false, '1',
    '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false
);

-- ===================================
-- 3. Option Type Management - Function Permissions
-- ===================================

-- 3.1 Create Option Type
INSERT INTO system_menu (
    id, name, permission, type, sort, parent_id,
    path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator,
    create_time, updater, update_time, deleted
) VALUES (
    5182, 'Create Option Type', 'cms:car-option-type:create', 3, 1, 5181,
    '#', '', '', '', 0,
    false, true, false, '1',
    '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false
);

-- 3.2 Update Option Type
INSERT INTO system_menu (
    id, name, permission, type, sort, parent_id,
    path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator,
    create_time, updater, update_time, deleted
) VALUES (
    5183, 'Update Option Type', 'cms:car-option-type:update', 3, 2, 5181,
    '#', '', '', '', 0,
    false, true, false, '1',
    '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false
);

-- 3.3 Delete Option Type
INSERT INTO system_menu (
    id, name, permission, type, sort, parent_id,
    path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator,
    create_time, updater, update_time, deleted
) VALUES (
    5184, 'Delete Option Type', 'cms:car-option-type:delete', 3, 3, 5181,
    '#', '', '', '', 0,
    false, true, false, '1',
    '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false
);

-- 3.4 Export Option Types
INSERT INTO system_menu (
    id, name, permission, type, sort, parent_id,
    path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator,
    create_time, updater, update_time, deleted
) VALUES (
    5185, 'Export Option Types', 'cms:car-option-type:export', 3, 4, 5181,
    '#', '', '', '', 0,
    false, true, false, '1',
    '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false
);

-- 3.5 Schema Designer
INSERT INTO system_menu (
    id, name, permission, type, sort, parent_id,
    path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator,
    create_time, updater, update_time, deleted
) VALUES (
    5186, 'Schema Designer', 'cms:car-option-type:design-schema', 3, 5, 5181,
    '#', '', '', '', 0,
    false, true, false, '1',
    '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false
);

-- ===================================
-- 4. Option Instance Management (二级菜单)
-- ===================================
INSERT INTO system_menu (
    id, name, permission, type, sort, parent_id,
    path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator,
    create_time, updater, update_time, deleted
) VALUES (
    5187, 'Option Instances', 'cms:car-model-option:query', 2, 2, 5180,
    'instance', 'ep:document', 'cms/carmodel/option/instance/index', 'CmsCarModelOption', 0,
    true, true, false, '1',
    '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false
);

-- ===================================
-- 5. Option Instance Management - Function Permissions
-- ===================================

-- 5.1 Create Instance
INSERT INTO system_menu (
    id, name, permission, type, sort, parent_id,
    path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator,
    create_time, updater, update_time, deleted
) VALUES (
    5188, 'Create Instance', 'cms:car-model-option:create', 3, 1, 5187,
    '#', '', '', '', 0,
    false, true, false, '1',
    '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false
);

-- 5.2 Update Instance
INSERT INTO system_menu (
    id, name, permission, type, sort, parent_id,
    path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator,
    create_time, updater, update_time, deleted
) VALUES (
    5189, 'Update Instance', 'cms:car-model-option:update', 3, 2, 5187,
    '#', '', '', '', 0,
    false, true, false, '1',
    '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false
);

-- 5.3 Delete Instance
INSERT INTO system_menu (
    id, name, permission, type, sort, parent_id,
    path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator,
    create_time, updater, update_time, deleted
) VALUES (
    5190, 'Delete Instance', 'cms:car-model-option:delete', 3, 3, 5187,
    '#', '', '', '', 0,
    false, true, false, '1',
    '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false
);

-- 5.4 Config Data Management
INSERT INTO system_menu (
    id, name, permission, type, sort, parent_id,
    path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator,
    create_time, updater, update_time, deleted
) VALUES (
    5191, 'Config Data Management', 'cms:car-model-option:config-data', 3, 4, 5187,
    '#', '', '', '', 0,
    false, true, false, '1',
    '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false
);

-- 5.5 Batch Configuration
INSERT INTO system_menu (
    id, name, permission, type, sort, parent_id,
    path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator,
    create_time, updater, update_time, deleted
) VALUES (
    5192, 'Batch Configuration', 'cms:car-model-option:batch-update', 3, 5, 5187,
    '#', '', '', '', 0,
    false, true, false, '1',
    '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false
);

-- 5.6 Data Preview
INSERT INTO system_menu (
    id, name, permission, type, sort, parent_id,
    path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator,
    create_time, updater, update_time, deleted
) VALUES (
    5193, 'Data Preview', 'cms:car-model-option:preview', 3, 6, 5187,
    '#', '', '', '', 0,
    false, true, false, '1',
    '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false
);

-- 5.7 Data Export
INSERT INTO system_menu (
    id, name, permission, type, sort, parent_id,
    path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator,
    create_time, updater, update_time, deleted
) VALUES (
    5194, 'Data Export', 'cms:car-model-option:export', 3, 7, 5187,
    '#', '', '', '', 0,
    false, true, false, '1',
    '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false
);

-- 5.8 Data Validation
INSERT INTO system_menu (
    id, name, permission, type, sort, parent_id,
    path, icon, component, component_name, status,
    visible, keep_alive, always_show, creator,
    create_time, updater, update_time, deleted
) VALUES (
    5195, 'Data Validation', 'cms:car-model-option:validate', 3, 8, 5187,
    '#', '', '', '', 0,
    false, true, false, '1',
    '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false
);

-- ===================================
-- 6. 给超级管理员角色分配权限
-- ===================================

-- 插入角色菜单关系 (角色ID=1 为超级管理员)
INSERT INTO system_role_menu (role_id, menu_id, creator, create_time, updater, update_time, deleted) VALUES
-- 配置选项管理主菜单
(1, 5180, '1', '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false),

-- 选项类型管理
(1, 5181, '1', '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false),
(1, 5182, '1', '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false),
(1, 5183, '1', '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false),
(1, 5184, '1', '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false),
(1, 5185, '1', '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false),
(1, 5186, '1', '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false),

-- 选项实例管理
(1, 5187, '1', '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false),
(1, 5188, '1', '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false),
(1, 5189, '1', '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false),
(1, 5190, '1', '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false),
(1, 5191, '1', '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false),
(1, 5192, '1', '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false),
(1, 5193, '1', '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false),
(1, 5194, '1', '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false),
(1, 5195, '1', '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false);

-- ===================================
-- 7. Dictionary Data Supplement (if needed)
-- ===================================

-- Configuration Option Category Dictionary (CMS_CAR_OPTION_CATEGORY)
INSERT INTO system_dict_data (
    id, sort, label, value, dict_type, status, color_type, css_class, remark,
    creator, create_time, updater, update_time, deleted
) VALUES 
(2301, 1, 'Exterior', 'exterior', 'CMS_CAR_OPTION_CATEGORY', 0, 'primary', '', 'Vehicle exterior related configurations', '1', '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false),
(2302, 2, 'Interior', 'interior', 'CMS_CAR_OPTION_CATEGORY', 0, 'success', '', 'Vehicle interior related configurations', '1', '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false),
(2303, 3, 'Technology', 'technology', 'CMS_CAR_OPTION_CATEGORY', 0, 'info', '', 'Technology feature related configurations', '1', '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false),
(2304, 4, 'Safety', 'safety', 'CMS_CAR_OPTION_CATEGORY', 0, 'warning', '', 'Safety feature related configurations', '1', '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false),
(2305, 5, 'Comfort', 'comfort', 'CMS_CAR_OPTION_CATEGORY', 0, 'success', '', 'Comfort related configurations', '1', '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false),
(2306, 6, 'Performance', 'performance', 'CMS_CAR_OPTION_CATEGORY', 0, 'danger', '', 'Performance related configurations', '1', '2025-01-09 10:00:00', '1', '2025-01-09 10:00:00', false);

-- ===================================
-- 8. Verification Scripts
-- ===================================

-- Verify Menu Data
SELECT 
    id, 
    name, 
    permission, 
    type,
    CASE type 
        WHEN 1 THEN 'Directory'
        WHEN 2 THEN 'Menu'
        WHEN 3 THEN 'Button'
        ELSE 'Unknown'
    END as type_name,
    path, 
    component,
    parent_id
FROM system_menu 
WHERE id BETWEEN 5180 AND 5195
ORDER BY id;

-- Verify Role Menu Relationships
SELECT 
    rm.role_id,
    r.name as role_name,
    m.id as menu_id,
    m.name as menu_name,
    m.permission
FROM system_role_menu rm
LEFT JOIN system_role r ON rm.role_id = r.id  
LEFT JOIN system_menu m ON rm.menu_id = m.id
WHERE rm.menu_id BETWEEN 5180 AND 5195
ORDER BY rm.menu_id;

-- ===================================
-- Script Execution Completed
-- ===================================