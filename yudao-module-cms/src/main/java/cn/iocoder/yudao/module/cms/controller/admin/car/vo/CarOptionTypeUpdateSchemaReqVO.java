package cn.iocoder.yudao.module.cms.controller.admin.car.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Map;

@Schema(description = "管理后台 - 配置选项类型更新 Request VO")
@Data
@ToString(callSuper = true)
public class CarOptionTypeUpdateSchemaReqVO {

    @Schema(description = "选项类型ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "选项类型ID不能为空")
    private Long id;

    @Schema(description = "配置字段定义", example = "{\"type\":\"object\",\"properties\":{\"colorCode\":{\"type\":\"string\"}}}")
    @NotNull(message = "配置字段定义不能为空")
    private Map<String, Object> configSchema;
}