-- 完整修复API SQL配置问题
-- 作者：开发团队
-- 日期：2025-01-10
-- 问题1：JSON_EXTRACT的路径参数被截断
-- 问题2：确保参数占位符格式正确

SET NAMES utf8mb4;

-- 开始事务
START TRANSACTION;

-- 1. 直接更新car_configurator的SQL配置为正确版本
UPDATE cms_api_sql_configs 
SET sql_content = 'SELECT 
    JSON_OBJECT(
        ''model'', JSON_OBJECT(
            ''id'', m.code,
            ''name'', m.name,
            ''category'', m.category,
            ''description'', m.description,
            ''base_price'', m.base_price,
            ''image'', m.image_url,
            ''ev_icon'', m.ev_icon_url,
            ''badge'', m.badge,
            ''features'', CASE WHEN m.features IS NOT NULL THEN JSON_EXTRACT(m.features, ''$'') ELSE JSON_ARRAY() END
        ),
        ''packages'', COALESCE((
            SELECT JSON_ARRAYAGG(
                JSON_OBJECT(
                    ''id'', cp.package_code,
                    ''name'', cp.name,
                    ''price'', cp.price,
                    ''features_title'', cp.features_title,
                    ''features'', CASE WHEN cp.features IS NOT NULL THEN JSON_EXTRACT(cp.features, ''$'') ELSE JSON_ARRAY() END
                )
            )
            FROM cms_car_packages cp 
            WHERE cp.model_id = m.id AND cp.status = 0 AND cp.deleted = 0
            ORDER BY cp.sort_order
        ), JSON_ARRAY()),
        ''options'', JSON_OBJECT(
            ''colors'', COALESCE((
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                        ''id'', mo.option_code,
                        ''name'', mo.name,
                        ''price'', mo.price,
                        ''image'', mo.image_url,
                        ''config_data'', CASE WHEN mo.config_data IS NOT NULL THEN JSON_EXTRACT(mo.config_data, ''$'') ELSE JSON_OBJECT() END
                    )
                )
                FROM cms_car_model_options mo
                JOIN cms_car_option_types ot ON mo.option_type_id = ot.id
                WHERE mo.model_id = m.id AND ot.type_code = ''colors'' AND mo.status = 0 AND mo.deleted = 0
                ORDER BY mo.sort_order
            ), JSON_ARRAY()),
            ''interiors'', COALESCE((
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                        ''id'', mo.option_code,
                        ''name'', mo.name,
                        ''price'', mo.price,
                        ''image'', mo.image_url,
                        ''config_data'', CASE WHEN mo.config_data IS NOT NULL THEN JSON_EXTRACT(mo.config_data, ''$'') ELSE JSON_OBJECT() END
                    )
                )
                FROM cms_car_model_options mo
                JOIN cms_car_option_types ot ON mo.option_type_id = ot.id
                WHERE mo.model_id = m.id AND ot.type_code = ''interiors'' AND mo.status = 0 AND mo.deleted = 0
                ORDER BY mo.sort_order
            ), JSON_ARRAY()),
            ''wheels'', COALESCE((
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                        ''id'', mo.option_code,
                        ''name'', mo.name,
                        ''price'', mo.price,
                        ''image'', mo.image_url,
                        ''config_data'', CASE WHEN mo.config_data IS NOT NULL THEN JSON_EXTRACT(mo.config_data, ''$'') ELSE JSON_OBJECT() END
                    )
                )
                FROM cms_car_model_options mo
                JOIN cms_car_option_types ot ON mo.option_type_id = ot.id
                WHERE mo.model_id = m.id AND ot.type_code = ''wheels'' AND mo.status = 0 AND mo.deleted = 0
                ORDER BY mo.sort_order
            ), JSON_ARRAY())
        ),
        ''finance_plans'', COALESCE((
            SELECT JSON_ARRAYAGG(
                JSON_OBJECT(
                    ''id'', cmfp.id,
                    ''finance_option'', JSON_OBJECT(
                        ''id'', fo.option_code,
                        ''name'', fo.name,
                        ''description'', fo.description
                    ),
                    ''term'', CASE WHEN ft.id IS NOT NULL THEN JSON_OBJECT(
                        ''name'', ft.name,
                        ''rate'', ft.rate
                    ) ELSE NULL END,
                    ''down_payment'', CASE WHEN dpo.id IS NOT NULL THEN JSON_OBJECT(
                        ''name'', dpo.name,
                        ''value'', dpo.value
                    ) ELSE NULL END,
                    ''weekly_payment'', cmfp.weekly_payment,
                    ''monthly_payment'', cmfp.monthly_payment,
                    ''gfv'', cmfp.gfv,
                    ''km_allowance'', cmfp.km_allowance,
                    ''is_featured'', CASE WHEN cmfp.is_featured = 1 THEN true ELSE false END
                )
            )
            FROM cms_car_model_finance_plans cmfp
            JOIN cms_finance_options fo ON cmfp.finance_option_id = fo.id
            LEFT JOIN cms_finance_terms ft ON cmfp.term_id = ft.id
            LEFT JOIN cms_down_payment_options dpo ON cmfp.down_payment_id = dpo.id
            WHERE cmfp.model_id = m.id AND cmfp.status = 0 AND cmfp.deleted = 0
            ORDER BY cmfp.is_featured DESC, cmfp.sort_order
        ), JSON_ARRAY())
    ) as api_response
FROM cms_car_models m 
WHERE m.code = #{code} AND m.status = 0 AND m.deleted = 0',
updated_time = NOW()
WHERE api_code = 'car_configurator' AND version = 'default';

-- 2. 更新car_configurator的v1版本
UPDATE cms_api_sql_configs 
SET sql_content = 'SELECT 
    JSON_OBJECT(
        ''model'', JSON_OBJECT(
            ''id'', m.code,
            ''name'', m.name,
            ''category'', m.category,
            ''description'', m.description,
            ''base_price'', m.base_price,
            ''image'', m.image_url,
            ''ev_icon'', m.ev_icon_url,
            ''badge'', m.badge,
            ''features'', CASE WHEN m.features IS NOT NULL THEN JSON_EXTRACT(m.features, ''$'') ELSE JSON_ARRAY() END
        ),
        ''packages'', COALESCE((
            SELECT JSON_ARRAYAGG(
                JSON_OBJECT(
                    ''id'', cp.package_code,
                    ''name'', cp.name,
                    ''price'', cp.price,
                    ''features_title'', cp.features_title,
                    ''features'', CASE WHEN cp.features IS NOT NULL THEN JSON_EXTRACT(cp.features, ''$'') ELSE JSON_ARRAY() END
                )
            )
            FROM cms_car_packages cp 
            WHERE cp.model_id = m.id AND cp.status = 0 AND cp.deleted = 0
            ORDER BY cp.sort_order
        ), JSON_ARRAY()),
        ''options'', JSON_OBJECT(
            ''colors'', COALESCE((
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                        ''id'', mo.option_code,
                        ''name'', mo.name,
                        ''price'', mo.price,
                        ''image'', mo.image_url,
                        ''config_data'', CASE WHEN mo.config_data IS NOT NULL THEN JSON_EXTRACT(mo.config_data, ''$'') ELSE JSON_OBJECT() END
                    )
                )
                FROM cms_car_model_options mo
                JOIN cms_car_option_types ot ON mo.option_type_id = ot.id
                WHERE mo.model_id = m.id AND ot.type_code = ''colors'' AND mo.status = 0 AND mo.deleted = 0
                ORDER BY mo.sort_order
            ), JSON_ARRAY()),
            ''interiors'', COALESCE((
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                        ''id'', mo.option_code,
                        ''name'', mo.name,
                        ''price'', mo.price,
                        ''image'', mo.image_url,
                        ''config_data'', CASE WHEN mo.config_data IS NOT NULL THEN JSON_EXTRACT(mo.config_data, ''$'') ELSE JSON_OBJECT() END
                    )
                )
                FROM cms_car_model_options mo
                JOIN cms_car_option_types ot ON mo.option_type_id = ot.id
                WHERE mo.model_id = m.id AND ot.type_code = ''interiors'' AND mo.status = 0 AND mo.deleted = 0
                ORDER BY mo.sort_order
            ), JSON_ARRAY()),
            ''wheels'', COALESCE((
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                        ''id'', mo.option_code,
                        ''name'', mo.name,
                        ''price'', mo.price,
                        ''image'', mo.image_url,
                        ''config_data'', CASE WHEN mo.config_data IS NOT NULL THEN JSON_EXTRACT(mo.config_data, ''$'') ELSE JSON_OBJECT() END
                    )
                )
                FROM cms_car_model_options mo
                JOIN cms_car_option_types ot ON mo.option_type_id = ot.id
                WHERE mo.model_id = m.id AND ot.type_code = ''wheels'' AND mo.status = 0 AND mo.deleted = 0
                ORDER BY mo.sort_order
            ), JSON_ARRAY())
        ),
        ''finance_plans'', COALESCE((
            SELECT JSON_ARRAYAGG(
                JSON_OBJECT(
                    ''id'', cmfp.id,
                    ''finance_option'', JSON_OBJECT(
                        ''id'', fo.option_code,
                        ''name'', fo.name,
                        ''description'', fo.description
                    ),
                    ''term'', CASE WHEN ft.id IS NOT NULL THEN JSON_OBJECT(
                        ''name'', ft.name,
                        ''rate'', ft.rate
                    ) ELSE NULL END,
                    ''down_payment'', CASE WHEN dpo.id IS NOT NULL THEN JSON_OBJECT(
                        ''name'', dpo.name,
                        ''value'', dpo.value
                    ) ELSE NULL END,
                    ''weekly_payment'', cmfp.weekly_payment,
                    ''monthly_payment'', cmfp.monthly_payment,
                    ''gfv'', cmfp.gfv,
                    ''km_allowance'', cmfp.km_allowance,
                    ''is_featured'', CASE WHEN cmfp.is_featured = 1 THEN true ELSE false END
                )
            )
            FROM cms_car_model_finance_plans cmfp
            JOIN cms_finance_options fo ON cmfp.finance_option_id = fo.id
            LEFT JOIN cms_finance_terms ft ON cmfp.term_id = ft.id
            LEFT JOIN cms_down_payment_options dpo ON cmfp.down_payment_id = dpo.id
            WHERE cmfp.model_id = m.id AND cmfp.status = 0 AND cmfp.deleted = 0
            ORDER BY cmfp.is_featured DESC, cmfp.sort_order
        ), JSON_ARRAY())
    ) as api_response
FROM cms_car_models m 
WHERE m.code = #{code} AND m.status = 0 AND m.deleted = 0',
updated_time = NOW()
WHERE api_code = 'car_configurator' AND version = 'v1';

-- 3. 验证修复结果
SELECT 
    api_code, 
    version,
    CASE 
        WHEN sql_content LIKE '%JSON_EXTRACT%\'\')%' 
          OR sql_content LIKE '%JSON_EXTRACT%\')%'
          OR sql_content LIKE '%JSON_EXTRACT%, \'\'%'
          OR sql_content LIKE '%JSON_EXTRACT%, \'%' AND sql_content NOT LIKE '%JSON_EXTRACT%, ''$''%'
        THEN '❌ 仍需修复'
        WHEN sql_content LIKE '%JSON_EXTRACT%''$''%'
        THEN '✅ 已修复'
        ELSE '⚠️ 无JSON_EXTRACT'
    END as fix_status,
    CASE 
        WHEN sql_content LIKE '%#{%}%'
        THEN '✅ 参数占位符正确'
        ELSE '⚠️ 检查参数占位符'
    END as param_status
FROM cms_api_sql_configs
WHERE api_code IN ('car_configurator', 'car_detail', 'car_models_list')
ORDER BY api_code, version;

-- 提交事务
COMMIT;

-- 输出修复完成信息
SELECT '修复完成！请重新测试API SQL配置。' as message;