package cn.iocoder.yudao.module.cms.service.car.finance;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 融资方案计算器
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class FinancePlanCalculator {

    /**
     * 计算融资方案
     *
     * @param carPrice 车辆价格
     * @param downPaymentRatio 首付比例 (0-1之间)
     * @param interestRate 年利率 (例如0.05表示5%)
     * @param termMonths 期限月数
     * @param gfv 保证未来价值（可选）
     * @return 计算结果
     */
    public FinancePlanResult calculate(BigDecimal carPrice, BigDecimal downPaymentRatio, 
                                     BigDecimal interestRate, Integer termMonths, BigDecimal gfv) {
        
        try {
            // 首付金额
            BigDecimal downPaymentAmount = carPrice.multiply(downPaymentRatio)
                    .setScale(2, RoundingMode.HALF_UP);
            
            // 融资金额 = 车价 - 首付
            BigDecimal financeAmount = carPrice.subtract(downPaymentAmount);
            
            // 如果有GFV，需要从融资金额中减去
            if (gfv != null && gfv.compareTo(BigDecimal.ZERO) > 0) {
                financeAmount = financeAmount.subtract(gfv);
            }
            
            // 月利率
            BigDecimal monthlyRate = interestRate.divide(BigDecimal.valueOf(12), 8, RoundingMode.HALF_UP);
            
            BigDecimal monthlyPayment;
            
            if (monthlyRate.compareTo(BigDecimal.ZERO) == 0) {
                // 无息贷款
                monthlyPayment = financeAmount.divide(BigDecimal.valueOf(termMonths), 2, RoundingMode.HALF_UP);
            } else {
                // 等额本息计算公式: M = P * [r * (1+r)^n] / [(1+r)^n - 1]
                BigDecimal onePlusRate = BigDecimal.ONE.add(monthlyRate);
                BigDecimal powerTerm = power(onePlusRate, termMonths);
                
                BigDecimal numerator = financeAmount.multiply(monthlyRate).multiply(powerTerm);
                BigDecimal denominator = powerTerm.subtract(BigDecimal.ONE);
                
                monthlyPayment = numerator.divide(denominator, 2, RoundingMode.HALF_UP);
            }
            
            // 周供 = 月供 * 12 / 52
            BigDecimal weeklyPayment = monthlyPayment
                    .multiply(BigDecimal.valueOf(12))
                    .divide(BigDecimal.valueOf(52), 2, RoundingMode.HALF_UP);
            
            // 总支付金额 = 首付 + 月供*期数 + GFV（如果有）
            BigDecimal totalPayment = downPaymentAmount
                    .add(monthlyPayment.multiply(BigDecimal.valueOf(termMonths)));
            
            if (gfv != null && gfv.compareTo(BigDecimal.ZERO) > 0) {
                totalPayment = totalPayment.add(gfv);
            }
            
            return new FinancePlanResult(
                    downPaymentAmount,
                    financeAmount,
                    monthlyPayment,
                    weeklyPayment,
                    totalPayment,
                    gfv
            );
            
        } catch (Exception e) {
            log.error("融资方案计算失败", e);
            throw new RuntimeException("融资方案计算失败: " + e.getMessage());
        }
    }

    /**
     * 计算幂运算
     */
    private BigDecimal power(BigDecimal base, int exponent) {
        BigDecimal result = BigDecimal.ONE;
        for (int i = 0; i < exponent; i++) {
            result = result.multiply(base);
        }
        return result.setScale(8, RoundingMode.HALF_UP);
    }

    /**
     * 批量计算融资方案
     *
     * @param requests 计算请求列表
     * @return 计算结果列表
     */
    public java.util.List<FinancePlanResult> batchCalculate(java.util.List<FinancePlanRequest> requests) {
        java.util.List<FinancePlanResult> results = new java.util.ArrayList<>();
        
        for (FinancePlanRequest request : requests) {
            try {
                FinancePlanResult result = calculate(
                        request.getCarPrice(),
                        request.getDownPaymentRatio(),
                        request.getInterestRate(),
                        request.getTermMonths(),
                        request.getGfv()
                );
                result.setRequestId(request.getRequestId());
                results.add(result);
            } catch (Exception e) {
                log.error("批量计算融资方案失败，请求ID: {}", request.getRequestId(), e);
                FinancePlanResult errorResult = new FinancePlanResult();
                errorResult.setRequestId(request.getRequestId());
                errorResult.setError(true);
                errorResult.setErrorMessage("计算失败: " + e.getMessage());
                results.add(errorResult);
            }
        }
        
        return results;
    }

    /**
     * 计算请求类
     */
    public static class FinancePlanRequest {
        private String requestId;
        private BigDecimal carPrice;
        private BigDecimal downPaymentRatio;
        private BigDecimal interestRate;
        private Integer termMonths;
        private BigDecimal gfv;

        public FinancePlanRequest() {}

        public FinancePlanRequest(String requestId, BigDecimal carPrice, BigDecimal downPaymentRatio,
                                BigDecimal interestRate, Integer termMonths, BigDecimal gfv) {
            this.requestId = requestId;
            this.carPrice = carPrice;
            this.downPaymentRatio = downPaymentRatio;
            this.interestRate = interestRate;
            this.termMonths = termMonths;
            this.gfv = gfv;
        }

        // Getters and setters
        public String getRequestId() { return requestId; }
        public void setRequestId(String requestId) { this.requestId = requestId; }

        public BigDecimal getCarPrice() { return carPrice; }
        public void setCarPrice(BigDecimal carPrice) { this.carPrice = carPrice; }

        public BigDecimal getDownPaymentRatio() { return downPaymentRatio; }
        public void setDownPaymentRatio(BigDecimal downPaymentRatio) { this.downPaymentRatio = downPaymentRatio; }

        public BigDecimal getInterestRate() { return interestRate; }
        public void setInterestRate(BigDecimal interestRate) { this.interestRate = interestRate; }

        public Integer getTermMonths() { return termMonths; }
        public void setTermMonths(Integer termMonths) { this.termMonths = termMonths; }

        public BigDecimal getGfv() { return gfv; }
        public void setGfv(BigDecimal gfv) { this.gfv = gfv; }
    }

    /**
     * 计算结果类
     */
    public static class FinancePlanResult {
        private String requestId;
        private BigDecimal downPaymentAmount;
        private BigDecimal financeAmount;
        private BigDecimal monthlyPayment;
        private BigDecimal weeklyPayment;
        private BigDecimal totalPayment;
        private BigDecimal gfv;
        private boolean error = false;
        private String errorMessage;

        public FinancePlanResult() {}

        public FinancePlanResult(BigDecimal downPaymentAmount, BigDecimal financeAmount,
                               BigDecimal monthlyPayment, BigDecimal weeklyPayment,
                               BigDecimal totalPayment, BigDecimal gfv) {
            this.downPaymentAmount = downPaymentAmount;
            this.financeAmount = financeAmount;
            this.monthlyPayment = monthlyPayment;
            this.weeklyPayment = weeklyPayment;
            this.totalPayment = totalPayment;
            this.gfv = gfv;
        }

        // Getters and setters
        public String getRequestId() { return requestId; }
        public void setRequestId(String requestId) { this.requestId = requestId; }

        public BigDecimal getDownPaymentAmount() { return downPaymentAmount; }
        public void setDownPaymentAmount(BigDecimal downPaymentAmount) { this.downPaymentAmount = downPaymentAmount; }

        public BigDecimal getFinanceAmount() { return financeAmount; }
        public void setFinanceAmount(BigDecimal financeAmount) { this.financeAmount = financeAmount; }

        public BigDecimal getMonthlyPayment() { return monthlyPayment; }
        public void setMonthlyPayment(BigDecimal monthlyPayment) { this.monthlyPayment = monthlyPayment; }

        public BigDecimal getWeeklyPayment() { return weeklyPayment; }
        public void setWeeklyPayment(BigDecimal weeklyPayment) { this.weeklyPayment = weeklyPayment; }

        public BigDecimal getTotalPayment() { return totalPayment; }
        public void setTotalPayment(BigDecimal totalPayment) { this.totalPayment = totalPayment; }

        public BigDecimal getGfv() { return gfv; }
        public void setGfv(BigDecimal gfv) { this.gfv = gfv; }

        public boolean isError() { return error; }
        public void setError(boolean error) { this.error = error; }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }

}