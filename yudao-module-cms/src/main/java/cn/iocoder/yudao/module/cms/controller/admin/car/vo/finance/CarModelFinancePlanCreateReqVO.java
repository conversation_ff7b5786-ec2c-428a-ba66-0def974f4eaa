package cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 车型融资方案创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CarModelFinancePlanCreateReqVO extends CarModelFinancePlanBaseVO {

    @Schema(description = "融资选项列表", example = "[]")
    private List<FinanceOptionReqVO> financeOptions;

}