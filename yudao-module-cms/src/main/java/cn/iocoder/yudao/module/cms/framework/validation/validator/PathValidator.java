package cn.iocoder.yudao.module.cms.framework.validation.validator;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.module.cms.framework.validation.CmsValidationUtils;
import cn.iocoder.yudao.module.cms.framework.validation.annotation.ValidPath;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * 路径格式验证器
 *
 * <AUTHOR>
 */
public class PathValidator implements ConstraintValidator<ValidPath, String> {

    private boolean allowEmpty;
    private int maxDepth;

    @Override
    public void initialize(ValidPath constraintAnnotation) {
        this.allowEmpty = constraintAnnotation.allowEmpty();
        this.maxDepth = constraintAnnotation.maxDepth();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // 允许为空的情况
        if (StrUtil.isBlank(value)) {
            return allowEmpty;
        }
        
        // 验证路径格式
        return CmsValidationUtils.isValidPath(value);
    }
}