package cn.iocoder.yudao.module.cms.controller.app.menu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * App - CMS 菜单 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "App - CMS 菜单 Response VO")
@Data
public class AppCmsMenuRespVO {

    @Schema(description = "菜单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "菜单名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "首页")
    private String name;

    @Schema(description = "菜单路径", requiredMode = Schema.RequiredMode.REQUIRED, example = "home")
    private String path;

    @Schema(description = "完整路径", requiredMode = Schema.RequiredMode.REQUIRED, example = "/home")
    private String fullPath;

    @Schema(description = "菜单图标", example = "home")
    private String icon;

    @Schema(description = "菜单URL", example = "https://example.com")
    private String url;

    @Schema(description = "显示顺序", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer sort;

    @Schema(description = "子菜单列表")
    private List<AppCmsMenuRespVO> children;
}