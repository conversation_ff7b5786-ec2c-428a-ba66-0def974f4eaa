package cn.iocoder.yudao.module.cms.framework.car.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.concurrent.Callable;

/**
 * 车型管理缓存辅助工具类
 * 
 * 提供统一的缓存操作方法，与yudao框架的缓存机制兼容
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class CarCacheHelper {
    
    @Resource
    private CacheManager cacheManager;
    
    /**
     * 获取缓存数据，如果不存在则执行valueLoader加载数据并缓存
     * 
     * @param cacheName 缓存名称（包含过期时间，如：car:model#1h）
     * @param key 缓存键
     * @param valueLoader 数据加载器
     * @return 缓存数据
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String cacheName, Object key, Callable<T> valueLoader) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache == null) {
                log.warn("缓存 {} 不存在，直接执行数据加载", cacheName);
                return valueLoader.call();
            }
            
            Cache.ValueWrapper wrapper = cache.get(key);
            if (wrapper != null) {
                log.debug("缓存命中: {}:{}", cacheName, key);
                return (T) wrapper.get();
            }
            
            log.debug("缓存未命中，执行数据加载: {}:{}", cacheName, key);
            T value = valueLoader.call();
            if (value != null) {
                cache.put(key, value);
            }
            return value;
            
        } catch (Exception e) {
            log.error("缓存操作失败: {}:{}, 错误: {}", cacheName, key, e.getMessage());
            try {
                return valueLoader.call();
            } catch (Exception ex) {
                throw new RuntimeException("数据加载失败", ex);
            }
        }
    }
    
    /**
     * 清除指定缓存
     * 
     * @param cacheName 缓存名称
     * @param key 缓存键
     */
    public void evict(String cacheName, Object key) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.evict(key);
                log.debug("缓存清除: {}:{}", cacheName, key);
            }
        } catch (Exception e) {
            log.error("缓存清除失败: {}:{}, 错误: {}", cacheName, key, e.getMessage());
        }
    }
    
    /**
     * 清除指定缓存的所有数据
     * 
     * @param cacheName 缓存名称
     */
    public void clear(String cacheName) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
                log.info("缓存全部清除: {}", cacheName);
            }
        } catch (Exception e) {
            log.error("缓存全部清除失败: {}, 错误: {}", cacheName, e.getMessage());
        }
    }
    
    /**
     * 批量清除缓存
     * 
     * @param cacheName 缓存名称
     * @param keys 缓存键集合
     */
    public void evictBatch(String cacheName, Collection<?> keys) {
        if (keys == null || keys.isEmpty()) {
            return;
        }
        
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                for (Object key : keys) {
                    cache.evict(key);
                }
                log.debug("批量缓存清除: {}, 数量: {}", cacheName, keys.size());
            }
        } catch (Exception e) {
            log.error("批量缓存清除失败: {}, 错误: {}", cacheName, e.getMessage());
        }
    }
    
    /**
     * 手动设置缓存
     * 
     * @param cacheName 缓存名称
     * @param key 缓存键
     * @param value 缓存值
     */
    public void put(String cacheName, Object key, Object value) {
        if (value == null) {
            return;
        }
        
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.put(key, value);
                log.debug("缓存设置: {}:{}", cacheName, key);
            }
        } catch (Exception e) {
            log.error("缓存设置失败: {}:{}, 错误: {}", cacheName, key, e.getMessage());
        }
    }
    
    /**
     * 判断缓存是否存在
     * 
     * @param cacheName 缓存名称
     * @param key 缓存键
     * @return 是否存在
     */
    public boolean exists(String cacheName, Object key) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                return cache.get(key) != null;
            }
        } catch (Exception e) {
            log.error("缓存检查失败: {}:{}, 错误: {}", cacheName, key, e.getMessage());
        }
        return false;
    }
    
    /**
     * 生成缓存键
     * 可以根据多个参数生成唯一的缓存键
     * 
     * @param params 参数列表
     * @return 缓存键
     */
    public static String generateKey(Object... params) {
        if (params == null || params.length == 0) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < params.length; i++) {
            if (i > 0) {
                sb.append(":");
            }
            sb.append(params[i] != null ? params[i].toString() : "null");
        }
        return sb.toString();
    }
}