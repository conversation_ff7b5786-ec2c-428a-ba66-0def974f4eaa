package cn.iocoder.yudao.module.cms.service.car;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelOptionCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelOptionPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelOptionUpdateReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarModelOptionDO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 车型配置选项 Service 接口
 *
 * <AUTHOR>
 */
public interface CarModelOptionService {

    /**
     * 创建车型配置选项
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCarModelOption(@Valid CarModelOptionCreateReqVO createReqVO);

    /**
     * 更新车型配置选项
     *
     * @param updateReqVO 更新信息
     */
    void updateCarModelOption(@Valid CarModelOptionUpdateReqVO updateReqVO);

    /**
     * 删除车型配置选项
     *
     * @param id 编号
     */
    void deleteCarModelOption(Long id);

    /**
     * 获得车型配置选项
     *
     * @param id 编号
     * @return 车型配置选项
     */
    CarModelOptionDO getCarModelOption(Long id);

    /**
     * 获得车型配置选项分页
     *
     * @param pageReqVO 分页查询
     * @return 车型配置选项分页
     */
    PageResult<CarModelOptionDO> getCarModelOptionPage(CarModelOptionPageReqVO pageReqVO);

    /**
     * 根据车型ID获得配置选项列表
     *
     * @param modelId 车型ID
     * @return 配置选项列表
     */
    List<CarModelOptionDO> getCarModelOptionListByModelId(Long modelId);

    /**
     * 根据车型ID和选项类型ID获得配置选项列表
     *
     * @param modelId 车型ID
     * @param optionTypeId 选项类型ID
     * @return 配置选项列表
     */
    List<CarModelOptionDO> getCarModelOptionListByModelIdAndTypeId(Long modelId, Long optionTypeId);

    /**
     * 获得车型的所有配置选项（关联选项类型信息）
     *
     * @param modelId 车型ID
     * @return 配置选项列表
     */
    List<CarModelOptionDO> getCarModelOptionsWithType(Long modelId);

    /**
     * 批量创建车型配置选项
     *
     * @param createReqVOList 创建信息列表
     * @return 创建成功的ID列表
     */
    List<Long> batchCreateCarModelOptions(@Valid List<CarModelOptionCreateReqVO> createReqVOList);

    /**
     * 批量更新车型配置选项
     *
     * @param updateReqVOList 更新信息列表
     */
    void batchUpdateCarModelOptions(@Valid List<CarModelOptionUpdateReqVO> updateReqVOList);

    /**
     * 根据车型ID删除所有配置选项
     *
     * @param modelId 车型ID
     */
    void deleteCarModelOptionsByModelId(Long modelId);

    /**
     * 验证配置选项的配置数据是否符合Schema
     *
     * @param optionTypeId 选项类型ID
     * @param configData 配置数据
     * @return 验证结果
     */
    boolean validateOptionConfigData(Long optionTypeId, Object configData);

}