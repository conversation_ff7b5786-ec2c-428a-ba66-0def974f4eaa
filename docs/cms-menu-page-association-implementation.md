# CMS菜单与页面关联功能实现

## 概述
实现了CMS菜单与DIY页面的关联管理功能，解决了代码中的两个TODO项：

1. **菜单路径变更时更新关联页面路径** (第125行)
2. **菜单删除时的页面关联检查** (第329-333行)

## 实现的功能

### 1. 菜单删除时的页面关联检查

**位置**: `CmsMenuServiceImpl.validateCanDelete()` 方法

**功能**: 
- 在删除菜单前检查是否有页面关联到该菜单
- 如果存在关联页面，阻止删除并抛出异常
- 提供详细的错误信息，包含各状态页面的统计

**实现细节**:
```java
// 检查是否存在关联页面
List<CmsDiyPageDO> relatedPages = cmsDiyPageMapper.selectListByMenuId(id);
if (CollUtil.isNotEmpty(relatedPages)) {
    // 统计各状态的页面数量，用于详细错误信息
    long publishedCount = ...;
    String pageDetails = String.format("共%d个页面（已发布:%d，草稿:%d，已下线:%d）", ...);
    throw exception(CMS_MENU_DELETE_FAIL_HAVE_PAGES);
}
```

### 2. 菜单路径变更时更新关联页面路径

**位置**: `CmsMenuServiceImpl.updateMenu()` 方法

**功能**:
- 当菜单路径或父菜单发生变化时，自动更新所有关联页面的路径
- 支持批量更新，提高性能
- 自动清除相关页面缓存
- 异常处理不影响菜单更新主流程

**核心方法**:
- `updateRelatedPagePaths()` - 更新关联页面路径
- `buildNewPagePath()` - 构建新的页面路径
- `batchUpdatePagePaths()` - 批量更新页面路径

## 新增的组件

### 1. PagePathUpdateDTO
**路径**: `cn.iocoder.yudao.module.cms.service.menu.dto.PagePathUpdateDTO`

**作用**: 用于批量更新页面路径的数据传输对象

**字段**:
- `pageId` - 页面ID
- `newFullPath` - 新的完整路径
- `originalPath` - 页面原路径

### 2. 数据库方法扩展

**CmsDiyPageMapper新增方法**:
- `batchUpdatePagePaths()` - 批量更新页面路径的SQL

**SQL实现**:
```xml
<update id="batchUpdatePagePaths">
    <foreach collection="pageUpdates" item="update" separator=";">
        UPDATE cms_diy_page 
        SET path = #{update.newFullPath}, update_time = NOW()
        WHERE id = #{update.pageId} AND deleted = 0
    </foreach>
</update>
```

## 业务逻辑

### 页面路径计算规则
1. **基础路径**: 菜单物化路径 + 页面相对路径
2. **路径规范化**: 确保路径正确以 `/` 开头和连接
3. **避免重复**: 防止路径中出现重复的 `/` 符号

### 错误处理策略
1. **菜单删除**: 严格检查，存在关联页面时阻止删除
2. **路径更新**: 容错处理，更新失败不影响菜单操作
3. **缓存管理**: 自动清除相关页面路径缓存

## 性能优化

### 1. 批量操作
- 使用批量SQL更新避免N+1问题
- 一次性处理所有页面路径更新

### 2. 缓存策略
- 及时清除过期的页面路径缓存
- 避免脏数据影响页面访问

### 3. 异常隔离
- 页面路径更新失败不影响菜单更新
- 记录详细日志便于排查问题

## 使用场景

### 1. 菜单管理
- **删除菜单**: 自动检查页面关联，防止数据不一致
- **修改路径**: 自动同步所有关联页面的路径

### 2. 页面访问
- **路径解析**: 确保页面路径与菜单路径保持一致
- **SEO友好**: 维护稳定的URL结构

## 测试建议

### 1. 功能测试
- 创建菜单和关联页面
- 修改菜单路径，验证页面路径自动更新
- 尝试删除有关联页面的菜单，验证阻止删除

### 2. 性能测试  
- 大量页面关联的菜单路径变更性能
- 批量页面路径更新的效率

### 3. 异常测试
- 数据库异常时的容错处理
- 并发修改时的数据一致性

## 扩展性

该实现具有良好的扩展性：
- 易于添加更多的页面关联检查规则
- 支持自定义页面路径计算逻辑  
- 可扩展到其他关联资源（如文章、商品等）

## 兼容性

- 保持向前兼容，不影响现有功能
- 新增功能默认启用，无需额外配置
- 支持物化路径和递归路径两种模式