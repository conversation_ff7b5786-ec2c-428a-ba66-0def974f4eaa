package cn.iocoder.yudao.module.cms.controller.app.diy.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * App - CMS DIY页面搜索结果 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "App - CMS DIY页面搜索结果 Response VO")
@Data
public class AppCmsDiyPageSearchRespVO {

    @Schema(description = "页面ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "页面UUID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String uuid;

    @Schema(description = "页面名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "首页")
    private String name;

    @Schema(description = "完整路径", requiredMode = Schema.RequiredMode.REQUIRED, example = "/home/<USER>")
    private String fullPath;

    @Schema(description = "关键词", example = "首页,主页")
    private String keywords;

    @Schema(description = "页面描述", example = "这是网站首页")
    private String description;

    @Schema(description = "发布时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime publishedTime;
}