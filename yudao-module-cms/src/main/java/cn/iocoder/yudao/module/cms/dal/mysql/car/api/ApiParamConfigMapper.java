package cn.iocoder.yudao.module.cms.dal.mysql.car.api;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.api.ApiParamConfigDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * API参数配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ApiParamConfigMapper extends BaseMapperX<ApiParamConfigDO> {

    default PageResult<ApiParamConfigDO> selectPage(Long apiConfigId, String paramName,
                                                     Integer pageNo, Integer pageSize) {
        return selectPage(new PageParam().setPageNo(pageNo).setPageSize(pageSize),new LambdaQueryWrapperX<ApiParamConfigDO>()
                .eqIfPresent(ApiParamConfigDO::getApiConfigId, apiConfigId)
                .likeIfPresent(ApiParamConfigDO::getParamName, paramName)
                .orderByDesc(ApiParamConfigDO::getIsRequired)
                .orderByAsc(ApiParamConfigDO::getParamName));
    }

    default List<ApiParamConfigDO> selectListByApiConfigId(Long apiConfigId) {
        return selectList(new LambdaQueryWrapperX<ApiParamConfigDO>()
                .eq(ApiParamConfigDO::getApiConfigId, apiConfigId)
                .orderByDesc(ApiParamConfigDO::getIsRequired)
                .orderByAsc(ApiParamConfigDO::getParamName));
    }

    default ApiParamConfigDO selectByApiConfigIdAndParamName(Long apiConfigId, String paramName) {
        return selectOne(new LambdaQueryWrapperX<ApiParamConfigDO>()
                .eq(ApiParamConfigDO::getApiConfigId, apiConfigId)
                .eq(ApiParamConfigDO::getParamName, paramName));
    }

    default List<ApiParamConfigDO> selectRequiredParams(Long apiConfigId) {
        return selectList(new LambdaQueryWrapperX<ApiParamConfigDO>()
                .eq(ApiParamConfigDO::getApiConfigId, apiConfigId)
                .eq(ApiParamConfigDO::getIsRequired, true)
                .orderByAsc(ApiParamConfigDO::getParamName));
    }

    default void deleteByApiConfigId(Long apiConfigId) {
        delete(new LambdaQueryWrapperX<ApiParamConfigDO>()
                .eq(ApiParamConfigDO::getApiConfigId, apiConfigId));
    }

}