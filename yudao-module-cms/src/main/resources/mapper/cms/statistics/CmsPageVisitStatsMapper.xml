<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.cms.dal.mysql.statistics.CmsPageVisitStatsMapper">

    <!-- 查询热门页面统计 -->
    <select id="selectHotPageStats" resultType="cn.iocoder.yudao.module.cms.dal.dataobject.statistics.CmsPageVisitStatsDO">
        SELECT
            page_id,
            SUM(total_visits) as totalVisits,
            SUM(unique_visitors) as uniqueVisitors,
            AVG(avg_stay_time) as avgStayTime,
            SUM(bounce_count) as bounceCount
        FROM cms_page_visit_stats
        WHERE stat_date &gt;= #{startDate} AND stat_date &lt;= #{endDate} AND deleted = 0
        GROUP BY page_id
        ORDER BY totalVisits DESC
        LIMIT #{limit}
    </select>

    <!-- 统计页面总访问量 -->
    <select id="selectTotalVisitsByPageId" resultType="java.lang.Long">
        SELECT COALESCE(SUM(total_visits), 0)
        FROM cms_page_visit_stats
        WHERE page_id = #{pageId} AND deleted = 0
    </select>

    <!-- 统计页面在指定日期范围内的总访问量 -->
    <select id="selectTotalVisitsByPageIdAndDateRange" resultType="java.lang.Long">
        SELECT COALESCE(SUM(total_visits), 0)
        FROM cms_page_visit_stats
        WHERE page_id = #{pageId} AND stat_date &gt;= #{startDate} AND stat_date &lt;= #{endDate} AND deleted = 0
    </select>

    <!-- 统计指定日期范围内的总访问量 -->
    <select id="selectTotalVisitsByDateRange" resultType="java.lang.Long">
        SELECT COALESCE(SUM(total_visits), 0)
        FROM cms_page_visit_stats
        WHERE stat_date &gt;= #{startDate} AND stat_date &lt;= #{endDate} AND deleted = 0
    </select>

    <!-- 统计指定日期范围内的独立访客数 -->
    <select id="selectTotalUniqueVisitorsByDateRange" resultType="java.lang.Long">
        SELECT COALESCE(SUM(unique_visitors), 0)
        FROM cms_page_visit_stats
        WHERE stat_date &gt;= #{startDate} AND stat_date &lt;= #{endDate} AND deleted = 0
    </select>

    <!-- 查询需要统计的日期列表 -->
    <select id="selectMissingStatDates" resultType="java.time.LocalDate">
        SELECT DISTINCT DATE(visit_time) as stat_date
        FROM cms_page_visit_log
        WHERE DATE(visit_time) NOT IN (
            SELECT DISTINCT stat_date FROM cms_page_visit_stats WHERE deleted = 0
        ) AND deleted = 0
        ORDER BY stat_date
    </select>

    <!-- 批量插入或更新统计数据 -->
    <insert id="insertOrUpdateBatch" parameterType="java.util.List">
        INSERT INTO cms_page_visit_stats (
            page_id, stat_date, total_visits, unique_visitors, avg_stay_time, bounce_count,
            creator, create_time, updater, update_time, deleted, tenant_id
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.pageId}, #{item.statDate}, #{item.totalVisits}, #{item.uniqueVisitors},
                #{item.avgStayTime}, #{item.bounceCount}, #{item.creator}, #{item.createTime},
                #{item.updater}, #{item.updateTime}, #{item.deleted}, #{item.tenantId}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            total_visits = VALUES(total_visits),
            unique_visitors = VALUES(unique_visitors),
            avg_stay_time = VALUES(avg_stay_time),
            bounce_count = VALUES(bounce_count),
            updater = VALUES(updater),
            update_time = VALUES(update_time)
    </insert>

    <!-- 查询热门页面 -->
    <select id="selectHotPages" resultType="cn.iocoder.yudao.module.cms.dal.dataobject.statistics.CmsPageVisitStatsDO">
        SELECT 
            page_id as pageId,
            SUM(total_visits) as totalVisits,
            SUM(unique_visitors) as uniqueVisitors,
            AVG(avg_stay_time) as avgStayTime,
            SUM(bounce_count) as bounceCount
        FROM cms_page_visit_stats
        WHERE stat_date >= #{startDate} AND stat_date &lt;= #{endDate} AND deleted = 0
        GROUP BY page_id
        ORDER BY totalVisits DESC
        LIMIT #{limit}
    </select>

    <!-- 删除指定日期的统计数据 -->
    <update id="deleteByStatDate">
        UPDATE cms_page_visit_stats 
        SET deleted = 1, update_time = NOW()
        WHERE stat_date = #{statDate} AND deleted = 0
    </update>

    <!-- 批量插入统计数据 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO cms_page_visit_stats (
            page_id, stat_date, total_visits, unique_visitors, avg_stay_time, bounce_count,
            creator, create_time, updater, update_time, deleted, tenant_id
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.pageId}, #{item.statDate}, #{item.totalVisits}, #{item.uniqueVisitors},
                #{item.avgStayTime}, #{item.bounceCount},
                #{item.creator}, #{item.createTime}, #{item.updater}, #{item.updateTime},
                #{item.deleted}, #{item.tenantId}
            )
        </foreach>
    </insert>

</mapper>