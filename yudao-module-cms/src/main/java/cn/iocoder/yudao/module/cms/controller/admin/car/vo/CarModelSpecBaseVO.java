package cn.iocoder.yudao.module.cms.controller.admin.car.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotNull;

/**
 * 车型规格管理 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class CarModelSpecBaseVO {

    @Schema(description = "车型ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "车型ID不能为空")
    private Long modelId;

    @Schema(description = "规格分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "ENGINE")
    @NotNull(message = "规格分类不能为空")
    @Length(max = 50, message = "规格分类长度不能超过50个字符")
    private String category;

    @Schema(description = "规格名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "发动机")
    @NotNull(message = "规格名称不能为空")
    @Length(max = 100, message = "规格名称长度不能超过100个字符")
    private String name;

    @Schema(description = "规格值", requiredMode = Schema.RequiredMode.REQUIRED, example = "1.5T涡轮增压发动机")
    @NotNull(message = "规格值不能为空")
    @Length(max = 200, message = "规格值长度不能超过200个字符")
    private String value;

    @Schema(description = "单位", example = "L")
    @Length(max = 20, message = "单位长度不能超过20个字符")
    private String unit;

    @Schema(description = "规格分组", example = "动力系统")
    @Length(max = 50, message = "规格分组长度不能超过50个字符")
    private String specGroup;

    @Schema(description = "显示顺序", example = "1")
    private Integer sort;

    @Schema(description = "状态：0-禁用，1-启用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

}