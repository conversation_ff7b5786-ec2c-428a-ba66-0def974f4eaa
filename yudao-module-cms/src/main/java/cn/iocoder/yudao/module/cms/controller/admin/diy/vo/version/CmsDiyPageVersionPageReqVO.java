package cn.iocoder.yudao.module.cms.controller.admin.diy.vo.version;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - CMS DIY页面版本分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CmsDiyPageVersionPageReqVO extends PageParam {

    @Schema(description = "页面ID", example = "1024")
    private Long pageId;

    @Schema(description = "版本名称", example = "v1.0")
    private String name;

    @Schema(description = "是否已发布", example = "true")
    private Boolean isPublished;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}