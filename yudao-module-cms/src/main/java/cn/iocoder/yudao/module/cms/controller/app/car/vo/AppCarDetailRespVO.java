package cn.iocoder.yudao.module.cms.controller.app.car.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

/**
 * App车型详情响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "App API - 车型详情响应")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppCarDetailRespVO {

    @Schema(description = "车型信息")
    private ModelInfo model;

    @Schema(description = "车型规格")
    private SpecsInfo specs;

    @Schema(description = "默认融资方案")
    private DefaultFinancePlanInfo defaultFinancePlan;

    @Schema(description = "车型基础信息")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ModelInfo {
        
        @Schema(description = "车型ID", example = "tiggo-7")
        private String id;
        
        @Schema(description = "车型名称", example = "TIGGO 7 PRO")
        private String name;
        
        @Schema(description = "车型描述")
        private String description;
        
        @Schema(description = "基础价格", example = "25900.00")
        private BigDecimal basePrice;
        
        @Schema(description = "车型图片URL")
        private String image;
    }

    @Schema(description = "车型规格信息")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SpecsInfo {
        
        @Schema(description = "发动机信息", example = "1.5T TGDI")
        private String engine;
        
        @Schema(description = "发动机类型", example = "Turbo")
        private String engineType;
        
        @Schema(description = "燃油消耗", example = "7.4L/100km")
        private String fuelConsumption;
        
        @Schema(description = "功率", example = "115kW")
        private String power;
        
        @Schema(description = "扭矩", example = "230Nm")
        private String torque;
        
        @Schema(description = "座位数", example = "5")
        private String seatsNum;
        
        @Schema(description = "车身尺寸")
        private Map<String, Object> dimensions;
        
        @Schema(description = "性能参数")
        private Map<String, Object> performance;
    }

    @Schema(description = "默认融资方案")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DefaultFinancePlanInfo {
        
        @Schema(description = "融资选项")
        private FinanceOptionInfo financeOption;
        
        @Schema(description = "每周付款", example = "89.00")
        private BigDecimal weeklyPayment;
        
        @Schema(description = "比较利率", example = "5.99")
        private BigDecimal comparisonRate;
        
        @Schema(description = "首付比例", example = "20")
        private BigDecimal depositPercentage;
        
        @Schema(description = "期限", example = "48 months")
        private String term;
        
        @Schema(description = "保证未来价值", example = "12000.00")
        private BigDecimal gfv;
        
        @Schema(description = "公里数限制", example = "15000")
        private Integer kmAllowance;
    }

    @Schema(description = "融资选项信息")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FinanceOptionInfo {
        
        @Schema(description = "选项名称", example = "Personal Contract Purchase")
        private String name;
        
        @Schema(description = "选项描述")
        private String description;
    }
}