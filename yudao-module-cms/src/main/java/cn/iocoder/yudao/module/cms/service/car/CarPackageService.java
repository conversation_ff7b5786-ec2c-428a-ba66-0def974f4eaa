package cn.iocoder.yudao.module.cms.service.car;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarPackageCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarPackagePageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarPackageUpdateReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarPackageDO;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 配置包管理 Service 接口
 *
 * <AUTHOR>
 */
public interface CarPackageService {

    /**
     * 创建配置包
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCarPackage(@Valid CarPackageCreateReqVO createReqVO);

    /**
     * 更新配置包
     *
     * @param updateReqVO 更新信息
     */
    void updateCarPackage(@Valid CarPackageUpdateReqVO updateReqVO);

    /**
     * 删除配置包
     *
     * @param id 编号
     */
    void deleteCarPackage(Long id);

    /**
     * 获得配置包
     *
     * @param id 编号
     * @return 配置包
     */
    CarPackageDO getCarPackage(Long id);

    /**
     * 获得配置包分页
     *
     * @param pageReqVO 分页查询
     * @return 配置包分页
     */
    PageResult<CarPackageDO> getCarPackagePage(CarPackagePageReqVO pageReqVO);

    /**
     * 获得配置包列表
     *
     * @param status 状态
     * @return 配置包列表
     */
    List<CarPackageDO> getCarPackageList(Integer status);

    /**
     * 根据编码获取配置包
     *
     * @param code 配置包编码
     * @return 配置包信息
     */
    CarPackageDO getCarPackageByCode(String code);

    /**
     * 获得价格区间内的配置包列表
     *
     * @param minPrice 最小价格
     * @param maxPrice 最大价格
     * @param status 状态
     * @return 配置包列表
     */
    List<CarPackageDO> getCarPackageListByPriceRange(BigDecimal minPrice, BigDecimal maxPrice, Integer status);

    /**
     * 获得热门配置包列表
     *
     * @param limit 限制数量
     * @return 热门配置包列表
     */
    List<CarPackageDO> getHotCarPackageList(Integer limit);

    /**
     * 根据车型ID获得配置包列表
     *
     * @param modelId 车型ID
     * @return 配置包列表
     */
    List<CarPackageDO> getCarPackageListByModelId(Long modelId);

}