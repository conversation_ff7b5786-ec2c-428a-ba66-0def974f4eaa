package cn.iocoder.yudao.module.cms.service.dealer;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.dealer.vo.DealerCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.dealer.vo.DealerPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.dealer.vo.DealerUpdateReqVO;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.dal.dataobject.dealer.DealerDO;
import cn.iocoder.yudao.module.cms.dal.dataobject.dealer.DealerWithDistanceDO;
import cn.iocoder.yudao.module.cms.dal.mysql.dealer.DealerMapper;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.cms.enums.ErrorCodeConstants.DEALER_NOT_EXISTS;

/**
 * CMS经销商 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DealerServiceImpl implements DealerService {

    @Resource
    private DealerMapper dealerMapper;

    @Override
    public Long createDealer(DealerCreateReqVO createReqVO) {
        // 插入
        DealerDO dealer = BeanUtils.toBean(createReqVO, DealerDO.class);
        dealerMapper.insert(dealer);
        // 返回
        return dealer.getId();
    }

    @Override
    public void updateDealer(DealerUpdateReqVO updateReqVO) {
        // 校验存在
        validateDealerExists(updateReqVO.getId());

        // 更新
        DealerDO updateObj = BeanUtils.toBean(updateReqVO, DealerDO.class);
        dealerMapper.updateById(updateObj);
    }

    @Override
    public void deleteDealer(Long id) {
        // 校验存在
        validateDealerExists(id);
        // 删除
        dealerMapper.deleteById(id);
    }

    private void validateDealerExists(Long id) {
        if (dealerMapper.selectById(id) == null) {
            throw exception(DEALER_NOT_EXISTS);
        }
    }

    @Override
    public DealerDO getDealer(Long id) {
        return dealerMapper.selectById(id);
    }

    @Override
    public PageResult<DealerDO> getDealerPage(DealerPageReqVO pageReqVO) {
        return dealerMapper.selectPage(pageReqVO);
    }

    @Override
    public List<DealerDO> getDealerList(String region, Integer status) {
        return dealerMapper.selectList(region, status);
    }

    @Override
    public List<DealerWithDistanceDO> getNearbyDealers(Double lat, Double lng, Integer radius) {
        // 参数校验
        if (lat == null || lng == null || radius == null || radius <= 0) {
            return Lists.newArrayList();
        }
        
        // 将半径从公里转换为米
        Integer radiusMeters = radius * 1000;
        // 使用MySQL地理函数查询指定半径内的经销商
        return dealerMapper.selectNearbyDealers(lat, lng, radiusMeters);
    }

}