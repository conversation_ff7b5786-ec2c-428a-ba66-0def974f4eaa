package cn.iocoder.yudao.module.cms.framework.cache.core;

import cn.iocoder.yudao.module.cms.framework.cache.config.CmsCacheProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 缓存预热服务
 * 
 * 在应用启动后预热常用缓存数据：
 * 1. 预热菜单树数据
 * 2. 预热热门页面数据
 * 3. 预热统计数据
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CacheWarmupService {

    private final CmsCacheProperties cacheProperties;
    private final MultiLevelCacheManager cacheManager;

    public CacheWarmupService(CmsCacheProperties cacheProperties,
                             MultiLevelCacheManager cacheManager) {
        this.cacheProperties = cacheProperties;
        this.cacheManager = cacheManager;
    }

    /**
     * 应用启动完成后执行缓存预热
     */
    @EventListener(ApplicationReadyEvent.class)
    @Async
    public void onApplicationReady() {
        if (!cacheProperties.getEnabled() || !cacheProperties.getWarmup().getEnabled()) {
            log.info("Cache warmup is disabled");
            return;
        }

        try {
            // 延迟执行预热，避免影响应用启动
            long delaySeconds = cacheProperties.getWarmup().getDelay().getSeconds();
            log.info("Cache warmup will start in {} seconds", delaySeconds);
            
            TimeUnit.SECONDS.sleep(delaySeconds);
            
            log.info("Starting cache warmup...");
            long startTime = System.currentTimeMillis();

            // 并行执行各种预热任务
            CompletableFuture<Void> menuWarmup = warmupMenuCache();
            CompletableFuture<Void> pageWarmup = warmupPageCache();
            CompletableFuture<Void> statsWarmup = warmupStatsCache();

            // 等待所有预热任务完成
            CompletableFuture.allOf(menuWarmup, pageWarmup, statsWarmup).join();

            long duration = System.currentTimeMillis() - startTime;
            log.info("Cache warmup completed in {} ms", duration);

        } catch (Exception e) {
            log.error("Cache warmup failed", e);
        }
    }

    /**
     * 预热菜单缓存
     */
    private CompletableFuture<Void> warmupMenuCache() {
        return CompletableFuture.runAsync(() -> {
            if (!cacheProperties.getWarmup().getMenuTree()) {
                return;
            }

            try {
                log.debug("Starting menu cache warmup...");
                
                // TODO: 这里需要注入 CmsMenuService 来预热菜单数据
                // 由于现在还没有实现 Service 层，这里先预留接口
                // menuService.warmupMenuCache();
                
                log.debug("Menu cache warmup completed");
            } catch (Exception e) {
                log.error("Menu cache warmup failed", e);
            }
        });
    }

    /**
     * 预热页面缓存
     */
    private CompletableFuture<Void> warmupPageCache() {
        return CompletableFuture.runAsync(() -> {
            try {
                log.debug("Starting page cache warmup...");
                
                Integer hotPagesCount = cacheProperties.getWarmup().getHotPagesCount();
                
                // TODO: 这里需要注入 CmsDiyPageService 来预热热门页面数据
                // 由于现在还没有实现 Service 层，这里先预留接口
                // pageService.warmupHotPages(hotPagesCount);
                
                log.debug("Page cache warmup completed for {} hot pages", hotPagesCount);
            } catch (Exception e) {
                log.error("Page cache warmup failed", e);
            }
        });
    }

    /**
     * 预热统计缓存
     */
    private CompletableFuture<Void> warmupStatsCache() {
        return CompletableFuture.runAsync(() -> {
            try {
                log.debug("Starting stats cache warmup...");
                
                // TODO: 这里需要注入统计服务来预热统计数据
                // 由于现在还没有实现 Service 层，这里先预留接口
                // statsService.warmupStatsCache();
                
                log.debug("Stats cache warmup completed");
            } catch (Exception e) {
                log.error("Stats cache warmup failed", e);
            }
        });
    }

    /**
     * 手动触发缓存预热
     */
    public void manualWarmup() {
        log.info("Manual cache warmup triggered");
        onApplicationReady();
    }

    /**
     * 预热指定缓存
     */
    public void warmupCache(String cacheName) {
        try {
            log.info("Warming up cache: {}", cacheName);
            
            // 根据缓存名称执行相应的预热逻辑
            switch (cacheName) {
                case "cms:menu":
                    warmupMenuCache().join();
                    break;
                case "cms:page":
                    warmupPageCache().join();
                    break;
                case "cms:stats":
                    warmupStatsCache().join();
                    break;
                default:
                    log.warn("Unknown cache name for warmup: {}", cacheName);
            }
            
            log.info("Cache warmup completed for: {}", cacheName);
        } catch (Exception e) {
            log.error("Failed to warmup cache: {}", cacheName, e);
        }
    }
}