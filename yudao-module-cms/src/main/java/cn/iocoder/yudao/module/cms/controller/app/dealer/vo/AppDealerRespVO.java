package cn.iocoder.yudao.module.cms.controller.app.dealer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "应用 App - 经销商列表 Response VO")
@Data
public class AppDealerRespVO {

    @Schema(description = "经销商ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "经销商名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "Allen Motor Romford")
    private String name;

    @Schema(description = "详细地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "17 London Rd, Romford RM7 9QB Greater London")
    private String address;

    @Schema(description = "联系电话", example = "01708 123456")
    private String phone;

    @Schema(description = "纬度", requiredMode = Schema.RequiredMode.REQUIRED, example = "51.5758311")
    private Double latitude;

    @Schema(description = "经度", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.1770876")
    private Double longitude;

    @Schema(description = "地区", requiredMode = Schema.RequiredMode.REQUIRED, example = "London")
    private String region;

    @Schema(description = "服务列表", example = "[\"Sales\", \"Service\", \"Parts\"]")
    private List<String> services;

    @Schema(description = "距离（公里）", example = "5.2")
    private Double distance;

}