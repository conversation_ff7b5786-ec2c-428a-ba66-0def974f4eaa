package cn.iocoder.yudao.module.cms.framework.validation;

import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.module.cms.enums.ErrorCodeConstants;
import org.jsoup.Jsoup;
import org.jsoup.safety.Safelist;

import java.util.UUID;
import java.util.regex.Pattern;

/**
 * CMS 模块验证工具类
 *
 * <AUTHOR>
 */
public class CmsValidationUtils {

    /**
     * URL 路径安全字符正则表达式
     * 只允许字母、数字、中划线、下划线
     */
    private static final Pattern PATH_PATTERN = Pattern.compile("^[a-zA-Z0-9\\-_]+$");

    /**
     * UUID 格式正则表达式
     */
    private static final Pattern UUID_PATTERN = Pattern.compile(
        "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"
    );

    /**
     * 最大内容长度（5MB）
     */
    private static final int MAX_CONTENT_LENGTH = 5 * 1024 * 1024;

    /**
     * 最大路径深度
     */
    private static final int MAX_PATH_DEPTH = 10;

    /**
     * 验证路径格式
     *
     * @param path 路径
     * @return 是否有效
     */
    public static boolean isValidPath(String path) {
        if (StrUtil.isBlank(path)) {
            return false;
        }
        if (path.length() > 200) {
            return false;
        }
        // 路径段验证
        String[] segments = path.split("/");
        if (segments.length > MAX_PATH_DEPTH) {
            return false;
        }
        for (String segment : segments) {
            if (StrUtil.isNotBlank(segment) && !PATH_PATTERN.matcher(segment).matches()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 验证 UUID 格式
     *
     * @param uuid UUID 字符串
     * @return 是否有效
     */
    public static boolean isValidUuid(String uuid) {
        if (StrUtil.isBlank(uuid)) {
            return false;
        }
        try {
            UUID.fromString(uuid);
            return UUID_PATTERN.matcher(uuid).matches();
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    /**
     * 清理和验证 HTML 内容，防止 XSS 攻击
     *
     * @param content HTML 内容
     * @return 清理后的内容
     */
    public static String sanitizeHtmlContent(String content) {
        if (StrUtil.isBlank(content)) {
            return content;
        }
        
        // 检查内容长度
        if (content.length() > MAX_CONTENT_LENGTH) {
            throw new ServiceException(ErrorCodeConstants.CMS_PAGE_CONTENT_TOO_LARGE);
        }

        // 使用 Jsoup 清理 HTML，允许基本的格式化标签
        Safelist safelist = Safelist.relaxed()
            .addTags("div", "span", "section", "article", "header", "footer", "nav", "aside")
            .addAttributes(":all", "class", "id", "style", "data-*")
            .addProtocols("img", "src", "http", "https", "data")
            .addProtocols("a", "href", "http", "https", "mailto", "tel");

        return Jsoup.clean(content, safelist);
    }

    /**
     * 验证 JSON 内容格式
     *
     * @param jsonContent JSON 内容
     * @return 是否有效
     */
    public static boolean isValidJsonContent(String jsonContent) {
        if (StrUtil.isBlank(jsonContent)) {
            return false;
        }
        
        // 检查内容长度
        if (jsonContent.length() > MAX_CONTENT_LENGTH) {
            return false;
        }

        try {
            // 简单验证 JSON 格式
            return jsonContent.trim().startsWith("{") || jsonContent.trim().startsWith("[");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 验证菜单名称
     *
     * @param name 菜单名称
     * @return 是否有效
     */
    public static boolean isValidMenuName(String name) {
        if (StrUtil.isBlank(name)) {
            return false;
        }
        // 菜单名称长度限制
        if (name.length() > 50) {
            return false;
        }
        // 不允许特殊字符
        return !ReUtil.contains("[<>\"'&]", name);
    }

    /**
     * 验证页面名称
     *
     * @param name 页面名称
     * @return 是否有效
     */
    public static boolean isValidPageName(String name) {
        if (StrUtil.isBlank(name)) {
            return false;
        }
        // 页面名称长度限制
        if (name.length() > 100) {
            return false;
        }
        // 不允许特殊字符
        return !ReUtil.contains("[<>\"'&]", name);
    }

    /**
     * 验证关键词
     *
     * @param keywords 关键词
     * @return 是否有效
     */
    public static boolean isValidKeywords(String keywords) {
        if (StrUtil.isBlank(keywords)) {
            return true; // 关键词可以为空
        }
        // 关键词长度限制
        if (keywords.length() > 200) {
            return false;
        }
        // 不允许脚本标签
        return !ReUtil.contains("<script", keywords.toLowerCase());
    }

    /**
     * 验证描述内容
     *
     * @param description 描述
     * @return 是否有效
     */
    public static boolean isValidDescription(String description) {
        if (StrUtil.isBlank(description)) {
            return true; // 描述可以为空
        }
        // 描述长度限制
        if (description.length() > 500) {
            return false;
        }
        // 不允许脚本标签
        return !ReUtil.contains("<script", description.toLowerCase());
    }

    /**
     * 验证排序值
     *
     * @param sort 排序值
     * @return 是否有效
     */
    public static boolean isValidSort(Integer sort) {
        if (sort == null) {
            return true; // 排序可以为空
        }
        // 排序值范围限制
        return sort >= 0 && sort <= 9999;
    }

    /**
     * 生成安全的文件名
     *
     * @param fileName 原始文件名
     * @return 安全的文件名
     */
    public static String sanitizeFileName(String fileName) {
        if (StrUtil.isBlank(fileName)) {
            return "file_" + System.currentTimeMillis();
        }
        // 移除特殊字符，只保留字母、数字、点、中划线、下划线
        String sanitized = fileName.replaceAll("[^a-zA-Z0-9._-]", "_");
        // 限制长度
        if (sanitized.length() > 100) {
            sanitized = sanitized.substring(0, 100);
        }
        return sanitized;
    }

    /**
     * 验证 URL 格式
     *
     * @param url URL 字符串
     * @return 是否有效
     */
    public static boolean isValidUrl(String url) {
        if (StrUtil.isBlank(url)) {
            return true; // URL 可以为空
        }
        if (url.length() > 500) {
            return false;
        }
        // 简单的 URL 格式验证
        return ReUtil.isMatch("^(https?://)?[\\w.-]+(/[\\w.-]*)*/?$", url);
    }
}