-- 修复API SQL配置语法错误
-- 作者：开发团队
-- 日期：2025-01-10
-- 描述：将所有字符串字面量从双单引号('')改为单引号(')

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 更新car_configurator API的SQL内容（将双单引号改为单引号）
UPDATE cms_api_sql_configs 
SET sql_content = REPLACE(REPLACE(sql_content, "''", "'"), "#{code}", ":code")
WHERE api_code = 'car_configurator';

-- 更新car_detail API的SQL内容
UPDATE cms_api_sql_configs 
SET sql_content = REPLACE(REPLACE(sql_content, "''", "'"), "#{code}", ":code")
WHERE api_code = 'car_detail';

-- 更新car_models_list API的SQL内容
UPDATE cms_api_sql_configs 
SET sql_content = REPLACE(REPLACE(sql_content, "''", "'"), "#{", ":"),
    sql_content = REPLACE(sql_content, "}", "")
WHERE api_code = 'car_models_list';

-- 更新car_series_list API的SQL内容
UPDATE cms_api_sql_configs 
SET sql_content = REPLACE(sql_content, "''", "'")
WHERE api_code = 'car_series_list';

-- 更新finance_options API的SQL内容
UPDATE cms_api_sql_configs 
SET sql_content = REPLACE(sql_content, "''", "'")
WHERE api_code = 'finance_options';

-- 更新price_calculator API的SQL内容
UPDATE cms_api_sql_configs 
SET sql_content = REPLACE(REPLACE(sql_content, "''", "'"), "#{", ":"),
    sql_content = REPLACE(sql_content, "}", "")
WHERE api_code = 'price_calculator';

-- 验证更新结果
SELECT 
    api_code, 
    version, 
    SUBSTRING(sql_content, 1, 100) as sql_preview,
    LENGTH(sql_content) - LENGTH(REPLACE(sql_content, "''", "")) as double_quotes_count,
    LENGTH(sql_content) - LENGTH(REPLACE(sql_content, "#{", "")) as param_count
FROM cms_api_sql_configs 
WHERE api_code IN ('car_configurator', 'car_detail', 'car_models_list', 'car_series_list', 'finance_options', 'price_calculator');

COMMIT;
SET FOREIGN_KEY_CHECKS = 1;