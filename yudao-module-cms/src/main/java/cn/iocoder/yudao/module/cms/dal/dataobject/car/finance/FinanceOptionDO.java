package cn.iocoder.yudao.module.cms.dal.dataobject.car.finance;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 融资选项 DO
 *
 * <AUTHOR>
 */
@TableName("cms_finance_options")
@KeySequence("cms_finance_options_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FinanceOptionDO extends BaseDO {

    /**
     * 融资选项ID
     */
    @TableId
    private Long id;

    /**
     * 融资选项代码
     */
    private String optionCode;

    /**
     * 融资选项名称
     */
    private String name;

    /**
     * 融资选项描述
     */
    private String description;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 状态
     *
     * 枚举 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer status;

}