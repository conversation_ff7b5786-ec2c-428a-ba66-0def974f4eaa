package cn.iocoder.yudao.module.cms.service.car.finance;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.DownPaymentOptionCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.DownPaymentOptionPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.DownPaymentOptionUpdateReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.finance.DownPaymentOptionDO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 首付选项 Service 接口
 *
 * <AUTHOR>
 */
public interface DownPaymentOptionService {

    /**
     * 创建首付选项
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDownPaymentOption(@Valid DownPaymentOptionCreateReqVO createReqVO);

    /**
     * 更新首付选项
     *
     * @param updateReqVO 更新信息
     */
    void updateDownPaymentOption(@Valid DownPaymentOptionUpdateReqVO updateReqVO);

    /**
     * 删除首付选项
     *
     * @param id 编号
     */
    void deleteDownPaymentOption(Long id);

    /**
     * 获得首付选项
     *
     * @param id 编号
     * @return 首付选项
     */
    DownPaymentOptionDO getDownPaymentOption(Long id);

    /**
     * 获得首付选项分页
     *
     * @param pageReqVO 分页查询
     * @return 首付选项分页
     */
    PageResult<DownPaymentOptionDO> getDownPaymentOptionPage(DownPaymentOptionPageReqVO pageReqVO);

    /**
     * 获得首付选项列表
     *
     * @param status 状态
     * @return 首付选项列表
     */
    List<DownPaymentOptionDO> getDownPaymentOptionList(Integer status);

    /**
     * 根据融资选项ID获得首付选项列表
     *
     * @param optionId 融资选项ID
     * @return 首付选项列表
     */
    List<DownPaymentOptionDO> getDownPaymentOptionListByOptionId(Long optionId);

    /**
     * 根据代码获取首付选项
     *
     * @param paymentCode 首付代码
     * @return 首付选项信息
     */
    DownPaymentOptionDO getDownPaymentOptionByCode(String paymentCode);

}