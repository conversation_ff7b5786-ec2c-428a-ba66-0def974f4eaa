# 动态表单开源组件方案对比

## 1. 推荐方案

### 1.1 Formily（阿里巴巴）⭐⭐⭐⭐⭐
**最适合复杂业务场景**

```typescript
// 安装
npm install @formily/core @formily/react @formily/antd

// 使用示例
import { createForm } from '@formily/core'
import { FormProvider, FormConsumer, Field } from '@formily/react'
import { Form, FormItem, Input, Select, ArrayTable } from '@formily/antd'

const schema = {
  type: 'object',
  properties: {
    color: {
      type: 'string',
      title: '颜色',
      'x-decorator': 'FormItem',
      'x-component': 'Select',
      'x-component-props': {
        placeholder: '请选择颜色'
      },
      enum: [
        { label: '珍珠白', value: 'white' },
        { label: '碳晶黑', value: 'black' }
      ]
    },
    features: {
      type: 'array',
      title: '配置特性',
      'x-decorator': 'FormItem',
      'x-component': 'ArrayTable',
      items: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            'x-component': 'Input'
          },
          included: {
            type: 'boolean',
            'x-component': 'Checkbox'
          }
        }
      }
    }
  }
}

// 车型配置表单
const ConfigForm = () => {
  const form = useMemo(() => createForm(), [])
  
  return (
    <FormProvider form={form}>
      <SchemaField schema={schema} />
    </FormProvider>
  )
}
```

**优势：**
- 强大的联动能力（字段依赖、显隐控制）
- 完整的表单协议（JSON Schema）
- 丰富的组件库（支持Ant Design、Element等）
- 高性能（局部渲染）
- 完善的文档和生态

**适用场景：**
- 复杂的车型配置表单
- 需要字段联动的场景
- 大型表单性能要求高

### 1.2 XRender（阿里飞猪）⭐⭐⭐⭐⭐
**最容易上手，适合快速开发**

```typescript
// 安装
npm install xrender form-render

// 使用示例
import FormRender, { useForm } from 'form-render';

const schema = {
  type: 'object',
  properties: {
    configType: {
      title: '配置类型',
      type: 'string',
      enum: ['color', 'interior', 'wheel', 'audio'],
      enumNames: ['外观颜色', '内饰', '轮毂', '音响'],
      widget: 'select'
    },
    configItems: {
      title: '配置项',
      type: 'array',
      widget: 'simpleList',
      items: {
        type: 'object',
        properties: {
          name: {
            title: '名称',
            type: 'string'
          },
          price: {
            title: '价格',
            type: 'number',
            widget: 'inputNumber'
          },
          image: {
            title: '图片',
            type: 'string',
            widget: 'imageUpload'
          }
        }
      }
    }
  },
  displayType: 'row',
  labelWidth: 120
};

const CarConfigForm = () => {
  const form = useForm();
  
  const watch = {
    configType: (val) => {
      // 根据配置类型动态改变表单
      if (val === 'color') {
        form.setSchema(colorSchema);
      }
    }
  };
  
  return (
    <FormRender
      form={form}
      schema={schema}
      watch={watch}
      onFinish={(data) => {
        console.log('提交数据:', data);
      }}
    />
  );
};
```

**优势：**
- 极简API，快速上手
- 可视化表单设计器
- 丰富的内置组件
- 支持自定义组件
- 良好的文档

**适用场景：**
- 管理端配置表单
- 需要可视化设计的场景
- 快速原型开发

### 1.3 react-jsonschema-form (Mozilla)⭐⭐⭐⭐
**国际化标准，生态成熟**

```typescript
// 安装
npm install @rjsf/core @rjsf/antd @rjsf/validator-ajv8

// 使用示例
import Form from '@rjsf/antd';
import validator from '@rjsf/validator-ajv8';

const schema = {
  title: "车型配置",
  type: "object",
  required: ["modelId", "configType"],
  properties: {
    modelId: {
      type: "string",
      title: "车型"
    },
    configType: {
      type: "string",
      title: "配置类型",
      enum: ["package", "color", "interior", "wheel"],
      enumNames: ["配置包", "颜色", "内饰", "轮毂"]
    },
    attributes: {
      title: "配置属性",
      type: "object",
      properties: {}  // 动态生成
    }
  }
};

const uiSchema = {
  configType: {
    "ui:widget": "select",
    "ui:placeholder": "选择配置类型"
  },
  attributes: {
    "ui:field": "dynamicAttributes"  // 自定义字段
  }
};

// 自定义动态字段
const DynamicAttributesField = ({ formData, onChange }) => {
  const configType = formData?.configType;
  
  // 根据配置类型返回不同的表单结构
  if (configType === 'color') {
    return (
      <ColorConfigFields 
        value={formData.attributes}
        onChange={onChange}
      />
    );
  }
  // ... 其他类型
};

const ConfigurationForm = () => {
  return (
    <Form
      schema={schema}
      uiSchema={uiSchema}
      validator={validator}
      fields={{
        dynamicAttributes: DynamicAttributesField
      }}
      onSubmit={({formData}) => console.log(formData)}
    />
  );
};
```

**优势：**
- 符合JSON Schema标准
- 强大的验证能力
- 国际化支持好
- 社区活跃，插件丰富

**适用场景：**
- 需要标准化的场景
- 跨平台数据交换
- 国际化项目

## 2. 其他备选方案

### 2.1 Formik + Yup
```typescript
// 轻量级，适合简单表单
import { Formik, Field, FieldArray } from 'formik';
import * as Yup from 'yup';

const DynamicForm = () => {
  return (
    <Formik
      initialValues={{ configs: [] }}
      validationSchema={Yup.object({
        configs: Yup.array().of(
          Yup.object({
            type: Yup.string().required(),
            value: Yup.mixed().required()
          })
        )
      })}
      onSubmit={values => console.log(values)}
    >
      {({ values, setFieldValue }) => (
        <Form>
          <FieldArray name="configs">
            {({ push, remove }) => (
              // 动态渲染字段
            )}
          </FieldArray>
        </Form>
      )}
    </Formik>
  );
};
```

### 2.2 React Hook Form
```typescript
// 高性能，非受控组件
import { useForm, useFieldArray } from 'react-hook-form';

const DynamicForm = () => {
  const { control, register, watch } = useForm();
  const { fields, append, remove } = useFieldArray({
    control,
    name: "configs"
  });
  
  const configType = watch("configType");
  
  // 根据类型动态渲染
  return (
    <form>
      {renderFieldsByType(configType)}
    </form>
  );
};
```

## 3. 车型配置系统推荐架构

### 3.1 技术选型建议

```typescript
// 推荐组合方案
{
  "管理端": {
    "主框架": "XRender/FormRender",
    "原因": "可视化设计器，快速搭建",
    "备选": "Formily（复杂联动需求）"
  },
  "用户端": {
    "主框架": "Formily",
    "原因": "性能好，交互流畅",
    "备选": "自定义组件（完全控制UI）"
  },
  "API层": {
    "Schema管理": "JSON Schema",
    "验证": "Ajv",
    "存储": "PostgreSQL JSONB"
  }
}
```

### 3.2 完整实现示例

```typescript
// 使用 Formily 实现车型配置动态表单
import { createForm, onFieldReact } from '@formily/core'
import { 
  FormProvider, 
  createSchemaField,
  RecursionField 
} from '@formily/react'
import { Form, FormItem, Input, Select, Radio } from '@formily/antd'

// 配置类型定义
const CONFIG_SCHEMAS = {
  color: {
    type: 'object',
    properties: {
      colorCode: {
        type: 'string',
        title: '颜色代码',
        'x-component': 'ColorPicker',
        'x-decorator': 'FormItem'
      },
      isTwoTone: {
        type: 'boolean',
        title: '是否双色',
        'x-component': 'Switch',
        'x-decorator': 'FormItem'
      },
      secondaryColor: {
        type: 'string',
        title: '第二颜色',
        'x-component': 'ColorPicker',
        'x-decorator': 'FormItem',
        'x-reactions': {
          dependencies: ['isTwoTone'],
          fulfill: {
            state: {
              visible: '{{$deps[0] === true}}'
            }
          }
        }
      }
    }
  },
  wheel: {
    type: 'object',
    properties: {
      size: {
        type: 'string',
        title: '轮毂尺寸',
        'x-component': 'Select',
        'x-decorator': 'FormItem',
        enum: ['17', '18', '19', '20', '21'],
        'x-component-props': {
          suffix: '英寸'
        }
      },
      style: {
        type: 'string',
        title: '轮毂样式',
        'x-component': 'Radio.Group',
        'x-decorator': 'FormItem',
        enum: [
          { label: '运动型', value: 'sport' },
          { label: '豪华型', value: 'luxury' },
          { label: '越野型', value: 'offroad' }
        ]
      }
    }
  },
  audio: {
    type: 'object',
    properties: {
      brand: {
        type: 'string',
        title: '音响品牌',
        'x-component': 'Select',
        'x-decorator': 'FormItem',
        enum: ['BOSE', 'Harman Kardon', 'Sony', 'JBL', 'Bang & Olufsen']
      },
      speakers: {
        type: 'number',
        title: '扬声器数量',
        'x-component': 'NumberPicker',
        'x-decorator': 'FormItem',
        'x-component-props': {
          min: 4,
          max: 24,
          step: 2
        }
      },
      features: {
        type: 'array',
        title: '音响特性',
        'x-component': 'Checkbox.Group',
        'x-decorator': 'FormItem',
        enum: [
          { label: '主动降噪', value: 'anc' },
          { label: '环绕音效', value: 'surround' },
          { label: '低音炮', value: 'subwoofer' },
          { label: '独立功放', value: 'amplifier' }
        ]
      }
    }
  }
};

// 动态配置表单组件
const DynamicConfigForm = ({ modelId, onSubmit }) => {
  const [configType, setConfigType] = useState('color');
  const [schema, setSchema] = useState(null);
  
  const form = useMemo(() => 
    createForm({
      effects: () => {
        // 字段联动逻辑
        onFieldReact('configType', (field) => {
          const type = field.value;
          if (type && CONFIG_SCHEMAS[type]) {
            setSchema(CONFIG_SCHEMAS[type]);
          }
        });
      }
    }), 
  []);
  
  const SchemaField = createSchemaField({
    components: {
      FormItem,
      Input,
      Select,
      ColorPicker: CustomColorPicker,
      NumberPicker: InputNumber,
      // ... 其他自定义组件
    }
  });
  
  return (
    <FormProvider form={form}>
      <Form 
        labelCol={6}
        wrapperCol={16}
        onAutoSubmit={onSubmit}
      >
        <FormItem label="配置类型" required>
          <Select
            value={configType}
            onChange={setConfigType}
            options={[
              { label: '外观颜色', value: 'color' },
              { label: '轮毂', value: 'wheel' },
              { label: '音响系统', value: 'audio' },
              { label: '添加新类型...', value: 'custom' }
            ]}
          />
        </FormItem>
        
        {schema && (
          <SchemaField schema={schema} />
        )}
        
        <FormItem>
          <Button type="primary" htmlType="submit">
            保存配置
          </Button>
        </FormItem>
      </Form>
    </FormProvider>
  );
};
```

## 4. 最终推荐

### 对于车型配置系统，我推荐使用：

1. **主选方案：XRender (FormRender)**
   - 原因：快速开发、可视化设计、易维护
   - 适合：管理端快速搭建

2. **备选方案：Formily**
   - 原因：性能优秀、功能强大、生态完整
   - 适合：复杂业务逻辑、高性能要求

3. **混合使用：**
   - 管理端：XRender（快速搭建）
   - 用户端：Formily（用户体验）
   - Schema：统一使用 JSON Schema 标准

### 实施建议：
1. 先用 XRender 快速原型
2. 复杂场景迁移到 Formily
3. 保持 Schema 标准化，便于切换