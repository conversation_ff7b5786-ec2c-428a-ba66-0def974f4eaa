package cn.iocoder.yudao.module.cms.controller.admin.diy.vo.version;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * CMS DIY页面版本 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - CMS DIY页面版本 Response VO")
@Data
public class CmsDiyPageVersionRespVO {

    @Schema(description = "版本ID", example = "1024")
    private Long id;

    @Schema(description = "页面ID", example = "2048")
    private Long pageId;

    @Schema(description = "版本号", example = "3")
    private Integer version;

    @Schema(description = "版本名称", example = "V3 - 双十一活动版本")
    private String name;

    @Schema(description = "页面内容JSON快照")
    private String content;

    @Schema(description = "发布时间")
    private LocalDateTime publishTime;

    @Schema(description = "是否已发布", example = "true")
    private Boolean isPublished;

    @Schema(description = "版本备注", example = "修复了首页轮播图问题")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}