package cn.iocoder.yudao.module.cms.controller.admin.car.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotNull;

/**
 * 车系管理 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class CarSeriesBaseVO {

    @Schema(description = "车系编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "TIGGO8_PRO")
    @NotNull(message = "车系编码不能为空")
    @Length(max = 50, message = "车系编码长度不能超过50个字符")
    private String code;

    @Schema(description = "车系名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "瑞虎8 PRO")
    @NotNull(message = "车系名称不能为空")
    @Length(max = 100, message = "车系名称长度不能超过100个字符")
    private String name;

    @Schema(description = "英文名称", example = "TIGGO 8 PRO")
    @Length(max = 100, message = "英文名称长度不能超过100个字符")
    private String englishName;

    @Schema(description = "车系描述", example = "全新一代家用SUV")
    @Length(max = 500, message = "车系描述长度不能超过500个字符")
    private String description;

    @Schema(description = "品牌标识", requiredMode = Schema.RequiredMode.REQUIRED, example = "CHERY")
    @NotNull(message = "品牌标识不能为空")
    @Length(max = 50, message = "品牌标识长度不能超过50个字符")
    private String brand;

    @Schema(description = "车系图片URL", example = "https://example.com/image.jpg")
    @Length(max = 200, message = "车系图片URL长度不能超过200个字符")
    private String imageUrl;

    @Schema(description = "显示顺序", example = "1")
    private Integer sort;

    @Schema(description = "状态：0-禁用，1-启用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

}