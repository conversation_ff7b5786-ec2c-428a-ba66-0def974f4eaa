package cn.iocoder.yudao.module.cms.controller.admin.car;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarPackageCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarPackagePageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarPackageRespVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarPackageUpdateReqVO;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarPackageDO;
import cn.iocoder.yudao.module.cms.service.car.CarPackageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 配置包管理")
@RestController
@RequestMapping("/cms/car/package")
@Validated
public class CarPackageController {

    @Resource
    private CarPackageService carPackageService;

    @PostMapping("/create")
    @Operation(summary = "创建配置包")
    @PreAuthorize("@ss.hasPermission('cms:car-package:create')")
    public CommonResult<Long> createCarPackage(@Valid @RequestBody CarPackageCreateReqVO createReqVO) {
        return success(carPackageService.createCarPackage(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新配置包")
    @PreAuthorize("@ss.hasPermission('cms:car-package:update')")
    public CommonResult<Boolean> updateCarPackage(@Valid @RequestBody CarPackageUpdateReqVO updateReqVO) {
        carPackageService.updateCarPackage(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除配置包")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('cms:car-package:delete')")
    public CommonResult<Boolean> deleteCarPackage(@RequestParam("id") Long id) {
        carPackageService.deleteCarPackage(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得配置包详情")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('cms:car-package:query')")
    public CommonResult<CarPackageRespVO> getCarPackage(@RequestParam("id") Long id) {
        CarPackageDO carPackage = carPackageService.getCarPackage(id);
        return success(BeanUtils.toBean(carPackage, CarPackageRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得配置包分页")
    @PreAuthorize("@ss.hasPermission('cms:car-package:query')")
    public CommonResult<PageResult<CarPackageRespVO>> getCarPackagePage(@Valid CarPackagePageReqVO pageReqVO) {
        PageResult<CarPackageDO> pageResult = carPackageService.getCarPackagePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CarPackageRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得配置包列表")
    @Parameter(name = "status", description = "状态", example = "1")
    @PreAuthorize("@ss.hasPermission('cms:car-package:query')")
    public CommonResult<List<CarPackageRespVO>> getCarPackageList(@RequestParam(value = "status", required = false) Integer status) {
        List<CarPackageDO> list = carPackageService.getCarPackageList(status);
        return success(BeanUtils.toBean(list, CarPackageRespVO.class));
    }

    @GetMapping("/get-by-code")
    @Operation(summary = "根据编码获取配置包")
    @Parameter(name = "code", description = "配置包编码", required = true, example = "COMFORT_PACKAGE")
    @PreAuthorize("@ss.hasPermission('cms:car-package:query')")
    public CommonResult<CarPackageRespVO> getCarPackageByCode(@RequestParam("code") String code) {
        CarPackageDO carPackage = carPackageService.getCarPackageByCode(code);
        return success(BeanUtils.toBean(carPackage, CarPackageRespVO.class));
    }

    @GetMapping("/list-by-price-range")
    @Operation(summary = "获得价格区间内的配置包列表")
    @Parameter(name = "minPrice", description = "最小价格", example = "10000.00")
    @Parameter(name = "maxPrice", description = "最大价格", example = "50000.00")
    @Parameter(name = "status", description = "状态", example = "1")
    @PreAuthorize("@ss.hasPermission('cms:car-package:query')")
    public CommonResult<List<CarPackageRespVO>> getCarPackageListByPriceRange(@RequestParam(value = "minPrice", required = false) BigDecimal minPrice,
                                                                             @RequestParam(value = "maxPrice", required = false) BigDecimal maxPrice,
                                                                             @RequestParam(value = "status", required = false) Integer status) {
        List<CarPackageDO> list = carPackageService.getCarPackageListByPriceRange(minPrice, maxPrice, status);
        return success(BeanUtils.toBean(list, CarPackageRespVO.class));
    }

    @GetMapping("/hot-list")
    @Operation(summary = "获得热门配置包列表")
    @Parameter(name = "limit", description = "限制数量", example = "10")
    @PreAuthorize("@ss.hasPermission('cms:car-package:query')")
    public CommonResult<List<CarPackageRespVO>> getHotCarPackageList(@RequestParam(value = "limit", required = false) Integer limit) {
        List<CarPackageDO> list = carPackageService.getHotCarPackageList(limit);
        return success(BeanUtils.toBean(list, CarPackageRespVO.class));
    }

    @GetMapping("/list-by-model")
    @Operation(summary = "根据车型ID获得配置包列表")
    @Parameter(name = "modelId", description = "车型ID", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('cms:car-package:query')")
    public CommonResult<List<CarPackageRespVO>> getCarPackageListByModelId(@RequestParam("modelId") Long modelId) {
        List<CarPackageDO> list = carPackageService.getCarPackageListByModelId(modelId);
        return success(BeanUtils.toBean(list, CarPackageRespVO.class));
    }

}