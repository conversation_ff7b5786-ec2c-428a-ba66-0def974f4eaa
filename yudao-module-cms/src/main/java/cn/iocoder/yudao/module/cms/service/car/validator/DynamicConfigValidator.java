package cn.iocoder.yudao.module.cms.service.car.validator;

import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarOptionTypeDO;
import cn.iocoder.yudao.module.cms.service.car.CarOptionTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.*;

/**
 * 动态配置数据验证器
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class DynamicConfigValidator {

    @Resource
    private CarOptionTypeService carOptionTypeService;

    @Resource
    private JsonSchemaValidator jsonSchemaValidator;

    /**
     * 验证配置数据
     *
     * @param typeCode 选项类型编码
     * @param configData 配置数据
     * @return 验证结果
     */
    public ValidationResult validateConfigData(String typeCode, Map<String, Object> configData) {
        try {
            // 获取选项类型
            CarOptionTypeDO optionType = carOptionTypeService.getCarOptionTypeByCode(typeCode);
            if (optionType == null) {
                return ValidationResult.failure("配置选项类型不存在: " + typeCode);
            }

            // 如果没有Schema定义，则认为验证通过
            if (optionType.getConfigSchema() == null || optionType.getConfigSchema().isEmpty()) {
                return ValidationResult.success();
            }

            // 使用JSON Schema验证
            boolean isValid = jsonSchemaValidator.validateData(optionType.getConfigSchema(), configData);
            
            if (isValid) {
                return ValidationResult.success();
            } else {
                return ValidationResult.failure("配置数据不符合Schema定义");
            }
        } catch (Exception e) {
            log.error("验证配置数据时发生异常", e);
            return ValidationResult.failure("验证失败: " + e.getMessage());
        }
    }

    /**
     * 批量验证配置数据
     *
     * @param configDataList 配置数据列表
     * @return 验证结果列表
     */
    public List<ValidationResult> batchValidateConfigData(List<ConfigDataItem> configDataList) {
        List<ValidationResult> results = new ArrayList<>();
        
        for (ConfigDataItem item : configDataList) {
            ValidationResult result = validateConfigData(item.getTypeCode(), item.getConfigData());
            result.setItemId(item.getItemId());
            results.add(result);
        }
        
        return results;
    }

    /**
     * 验证字段完整性
     * 检查必填字段是否都有值
     *
     * @param typeCode 选项类型编码
     * @param configData 配置数据
     * @return 验证结果
     */
    public ValidationResult validateFieldCompleteness(String typeCode, Map<String, Object> configData) {
        try {
            CarOptionTypeDO optionType = carOptionTypeService.getCarOptionTypeByCode(typeCode);
            if (optionType == null) {
                return ValidationResult.failure("配置选项类型不存在: " + typeCode);
            }

            Map<String, Object> schema = optionType.getConfigSchema();
            if (schema == null || !schema.containsKey("required")) {
                return ValidationResult.success();
            }

            @SuppressWarnings("unchecked")
            List<String> requiredFields = (List<String>) schema.get("required");
            List<String> missingFields = new ArrayList<>();

            for (String field : requiredFields) {
                if (!configData.containsKey(field) || configData.get(field) == null) {
                    missingFields.add(field);
                }
            }

            if (missingFields.isEmpty()) {
                return ValidationResult.success();
            } else {
                return ValidationResult.failure("缺少必填字段: " + String.join(", ", missingFields));
            }
        } catch (Exception e) {
            log.error("验证字段完整性时发生异常", e);
            return ValidationResult.failure("验证失败: " + e.getMessage());
        }
    }

    /**
     * 验证数据类型一致性
     *
     * @param typeCode 选项类型编码
     * @param configData 配置数据
     * @return 验证结果
     */
    public ValidationResult validateDataTypeConsistency(String typeCode, Map<String, Object> configData) {
        try {
            CarOptionTypeDO optionType = carOptionTypeService.getCarOptionTypeByCode(typeCode);
            if (optionType == null) {
                return ValidationResult.failure("配置选项类型不存在: " + typeCode);
            }

            Map<String, Object> schema = optionType.getConfigSchema();
            if (schema == null || !schema.containsKey("properties")) {
                return ValidationResult.success();
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> properties = (Map<String, Object>) schema.get("properties");
            List<String> typeErrors = new ArrayList<>();

            for (Map.Entry<String, Object> entry : configData.entrySet()) {
                String fieldName = entry.getKey();
                Object fieldValue = entry.getValue();
                
                if (properties.containsKey(fieldName)) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> fieldSchema = (Map<String, Object>) properties.get(fieldName);
                    
                    if (!validateFieldType(fieldValue, fieldSchema)) {
                        typeErrors.add(fieldName + "类型不匹配");
                    }
                }
            }

            if (typeErrors.isEmpty()) {
                return ValidationResult.success();
            } else {
                return ValidationResult.failure("数据类型错误: " + String.join(", ", typeErrors));
            }
        } catch (Exception e) {
            log.error("验证数据类型一致性时发生异常", e);
            return ValidationResult.failure("验证失败: " + e.getMessage());
        }
    }

    /**
     * 验证字段类型
     */
    private boolean validateFieldType(Object value, Map<String, Object> fieldSchema) {
        if (value == null) {
            return true; // null值由必填验证处理
        }

        String expectedType = (String) fieldSchema.get("type");
        if (expectedType == null) {
            return true;
        }

        switch (expectedType) {
            case "string":
                return value instanceof String;
            case "number":
                return value instanceof Number;
            case "integer":
                return value instanceof Integer || (value instanceof Number && ((Number) value).doubleValue() == ((Number) value).longValue());
            case "boolean":
                return value instanceof Boolean;
            case "array":
                return value instanceof List;
            case "object":
                return value instanceof Map;
            default:
                return true;
        }
    }

    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private boolean success;
        private String message;
        private String itemId;

        private ValidationResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public static ValidationResult success() {
            return new ValidationResult(true, "验证通过");
        }

        public static ValidationResult failure(String message) {
            return new ValidationResult(false, message);
        }

        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }

        public String getItemId() {
            return itemId;
        }

        public void setItemId(String itemId) {
            this.itemId = itemId;
        }
    }

    /**
     * 配置数据项
     */
    public static class ConfigDataItem {
        private String itemId;
        private String typeCode;
        private Map<String, Object> configData;

        public ConfigDataItem(String itemId, String typeCode, Map<String, Object> configData) {
            this.itemId = itemId;
            this.typeCode = typeCode;
            this.configData = configData;
        }

        public String getItemId() {
            return itemId;
        }

        public String getTypeCode() {
            return typeCode;
        }

        public Map<String, Object> getConfigData() {
            return configData;
        }
    }

}