package cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;

@Schema(description = "管理后台 - 融资期限更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FinanceTermUpdateReqVO extends FinanceTermBaseVO {

    @Schema(description = "期限ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "期限ID不能为空")
    private Long id;

}