-- Fixed SQL with proper JSON_EXTRACT syntax
SELECT 
    JSON_OBJECT(
        'model', JSON_OBJECT(
            'id', m.code,
            'name', m.name,
            'category', m.category,
            'description', m.description,
            'base_price', m.base_price,
            'image', m.image_url,
            'ev_icon', m.ev_icon_url,
            'badge', m.badge,
            'features', CASE WHEN m.features IS NOT NULL THEN JSON_EXTRACT(m.features, '$') ELSE JSON_ARRAY() END
        ),
        'packages', COALESCE((
            SELECT JSON_ARRAYAGG(
                JSON_OBJECT(
                    'id', cp.package_code,
                    'name', cp.name,
                    'price', cp.price,
                    'features_title', cp.features_title,
                    'features', CASE WHEN cp.features IS NOT NULL THEN JSON_EXTRACT(cp.features, '$') ELSE JSON_ARRAY() END
                )
            )
            FROM cms_car_packages cp 
            WHERE cp.model_id = m.id AND cp.status = 0 AND cp.deleted = 0
            ORDER BY cp.sort_order
        ), JSON_ARRAY()),
        'options', JSON_OBJECT(
            'colors', COALESCE((
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                        'id', mo.option_code,
                        'name', mo.name,
                        'price', mo.price,
                        'image', mo.image_url,
                        'config_data', CASE WHEN mo.config_data IS NOT NULL THEN JSON_EXTRACT(mo.config_data, '$') ELSE JSON_OBJECT() END
                    )
                )
                FROM cms_car_model_options mo
                JOIN cms_car_option_types ot ON mo.option_type_id = ot.id
                WHERE mo.model_id = m.id AND ot.type_code = 'colors' AND mo.status = 0 AND mo.deleted = 0
                ORDER BY mo.sort_order
            ), JSON_ARRAY()),
            'interiors', COALESCE((
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                        'id', mo.option_code,
                        'name', mo.name,
                        'price', mo.price,
                        'image', mo.image_url,
                        'config_data', CASE WHEN mo.config_data IS NOT NULL THEN JSON_EXTRACT(mo.config_data, '$') ELSE JSON_OBJECT() END
                    )
                )
                FROM cms_car_model_options mo
                JOIN cms_car_option_types ot ON mo.option_type_id = ot.id
                WHERE mo.model_id = m.id AND ot.type_code = 'interiors' AND mo.status = 0 AND mo.deleted = 0
                ORDER BY mo.sort_order
            ), JSON_ARRAY()),
            'wheels', COALESCE((
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                        'id', mo.option_code,
                        'name', mo.name,
                        'price', mo.price,
                        'image', mo.image_url,
                        'config_data', CASE WHEN mo.config_data IS NOT NULL THEN JSON_EXTRACT(mo.config_data, '$') ELSE JSON_OBJECT() END
                    )
                )
                FROM cms_car_model_options mo
                JOIN cms_car_option_types ot ON mo.option_type_id = ot.id
                WHERE mo.model_id = m.id AND ot.type_code = 'wheels' AND mo.status = 0 AND mo.deleted = 0
                ORDER BY mo.sort_order
            ), JSON_ARRAY()),
            'services', COALESCE((
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                        'id', mo.option_code,
                        'name', mo.name,
                        'price', mo.price,
                        'image', mo.image_url,
                        'config_data', CASE WHEN mo.config_data IS NOT NULL THEN JSON_EXTRACT(mo.config_data, '$') ELSE JSON_OBJECT() END
                    )
                )
                FROM cms_car_model_options mo
                JOIN cms_car_option_types ot ON mo.option_type_id = ot.id
                WHERE mo.model_id = m.id AND ot.type_code = 'services' AND mo.status = 0 AND mo.deleted = 0
                ORDER BY mo.sort_order
            ), JSON_ARRAY())
        )
    ) AS result
FROM cms_car_models m
WHERE m.code = #{modelCode} 
    AND m.status = 0 
    AND m.deleted = 0;