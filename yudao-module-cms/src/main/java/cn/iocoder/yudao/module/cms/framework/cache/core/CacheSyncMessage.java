package cn.iocoder.yudao.module.cms.framework.cache.core;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 缓存同步消息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CacheSyncMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 发送实例 ID
     */
    private String instanceId;

    /**
     * 租户 ID
     */
    private Long tenantId;

    /**
     * 缓存名称
     */
    private String cacheName;

    /**
     * 缓存键
     */
    private Object key;

    /**
     * 缓存值
     */
    private Object value;

    /**
     * 操作类型
     */
    private CacheOperation operation;

    /**
     * 时间戳
     */
    private Long timestamp;
}