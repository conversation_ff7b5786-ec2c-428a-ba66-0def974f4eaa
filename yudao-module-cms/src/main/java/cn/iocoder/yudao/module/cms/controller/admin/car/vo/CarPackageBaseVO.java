package cn.iocoder.yudao.module.cms.controller.admin.car.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 配置包管理 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class CarPackageBaseVO {

    @Schema(description = "车型ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long modelId;

    @Schema(description = "配置包编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "COMFORT_PACKAGE")
    @NotNull(message = "配置包编码不能为空")
    @Length(max = 50, message = "配置包编码长度不能超过50个字符")
    private String packageCode;

    @Schema(description = "配置包名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "舒适性配置包")
    @NotNull(message = "配置包名称不能为空")
    @Length(max = 100, message = "配置包名称长度不能超过100个字符")
    private String name;


    @Schema(description = "配置包价格", example = "15000.00")
    private BigDecimal price;

    @Schema(description = "特性标题", example = "主要特性")
    private String featuresTitle;

    @Schema(description = "配置列表")
    private List<CarPackageFeatureVO> features;

    @Schema(description = "排序", example = "1")
    private Integer sortOrder;

    @Schema(description = "状态：0-禁用，1-启用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

}