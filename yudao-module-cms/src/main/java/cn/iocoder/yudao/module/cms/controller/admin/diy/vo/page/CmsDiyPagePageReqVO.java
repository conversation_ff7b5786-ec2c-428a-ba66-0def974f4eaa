package cn.iocoder.yudao.module.cms.controller.admin.diy.vo.page;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * CMS DIY页面分页 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - CMS DIY页面分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CmsDiyPagePageReqVO extends PageParam {

    @Schema(description = "页面名称", example = "首页")
    private String name;

    @Schema(description = "页面UUID", example = "550e8400-e29b-41d4-a716-************")
    private String uuid;

    @Schema(description = "关联菜单ID", example = "1024")
    private Long menuId;

    @Schema(description = "上级页面ID", example = "1025")
    private Long parentId;

    @Schema(description = "页面路径", example = "/home/<USER>")
    private String path;

    @Schema(description = "状态", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
}