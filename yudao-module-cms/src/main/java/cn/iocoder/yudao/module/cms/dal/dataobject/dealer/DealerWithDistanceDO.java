package cn.iocoder.yudao.module.cms.dal.dataobject.dealer;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * CMS经销商带距离信息 DO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DealerWithDistanceDO extends DealerDO {
    
    /**
     * 距离（公里）
     */
    private Double distance;
    
}