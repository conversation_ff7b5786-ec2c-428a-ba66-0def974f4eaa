package cn.iocoder.yudao.module.cms.dal.mysql.statistics;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.cms.controller.admin.statistics.vo.CmsPageVisitStatsPageReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.statistics.CmsPageVisitStatsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;

/**
 * CMS页面访问统计汇总 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CmsPageVisitStatsMapper extends BaseMapperX<CmsPageVisitStatsDO> {

    default PageResult<CmsPageVisitStatsDO> selectPage(CmsPageVisitStatsPageReqVO pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<CmsPageVisitStatsDO>()
                .eqIfPresent(CmsPageVisitStatsDO::getPageId, pageReqVO.getPageId())
                .betweenIfPresent(CmsPageVisitStatsDO::getStatDate, pageReqVO.getStatDate())
                .orderByDesc(CmsPageVisitStatsDO::getStatDate));
    }

    /**
     * 根据页面ID和日期查询统计数据
     */
    default CmsPageVisitStatsDO selectByPageIdAndDate(Long pageId, LocalDate statDate) {
        return selectOne(new LambdaQueryWrapperX<CmsPageVisitStatsDO>()
                .eq(CmsPageVisitStatsDO::getPageId, pageId)
                .eq(CmsPageVisitStatsDO::getStatDate, statDate));
    }

    /**
     * 根据页面ID查询统计数据（分页）
     */
    default PageResult<CmsPageVisitStatsDO> selectPageByPageId(Long pageId, PageParam pageReq) {
        return selectPage(pageReq, new LambdaQueryWrapperX<CmsPageVisitStatsDO>()
                .eq(CmsPageVisitStatsDO::getPageId, pageId)
                .orderByDesc(CmsPageVisitStatsDO::getStatDate));
    }

    /**
     * 根据日期范围查询统计数据
     */
    default List<CmsPageVisitStatsDO> selectListByDateRange(LocalDate startDate, LocalDate endDate) {
        return selectList(new LambdaQueryWrapperX<CmsPageVisitStatsDO>()
                .between(CmsPageVisitStatsDO::getStatDate, startDate, endDate)
                .orderByDesc(CmsPageVisitStatsDO::getStatDate));
    }

    /**
     * 根据页面ID和日期范围查询统计数据
     */
    default List<CmsPageVisitStatsDO> selectListByPageIdAndDateRange(Long pageId, LocalDate startDate, LocalDate endDate) {
        return selectList(new LambdaQueryWrapperX<CmsPageVisitStatsDO>()
                .eq(CmsPageVisitStatsDO::getPageId, pageId)
                .between(CmsPageVisitStatsDO::getStatDate, startDate, endDate)
                .orderByDesc(CmsPageVisitStatsDO::getStatDate));
    }

    /**
     * 查询热门页面统计（根据总访问量排序）
     */
    List<CmsPageVisitStatsDO> selectHotPageStats(@Param("startDate") LocalDate startDate, 
                                                 @Param("endDate") LocalDate endDate, 
                                                 @Param("limit") Integer limit);

    /**
     * 统计页面总访问量
     */
    Long selectTotalVisitsByPageId(@Param("pageId") Long pageId);

    /**
     * 统计页面在指定日期范围内的总访问量
     */
    Long selectTotalVisitsByPageIdAndDateRange(@Param("pageId") Long pageId, 
                                              @Param("startDate") LocalDate startDate, 
                                              @Param("endDate") LocalDate endDate);

    /**
     * 统计指定日期范围内的总访问量
     */
    Long selectTotalVisitsByDateRange(@Param("startDate") LocalDate startDate, 
                                     @Param("endDate") LocalDate endDate);

    /**
     * 统计指定日期范围内的独立访客数
     */
    Long selectTotalUniqueVisitorsByDateRange(@Param("startDate") LocalDate startDate, 
                                             @Param("endDate") LocalDate endDate);

    /**
     * 批量插入或更新统计数据
     */
    int insertOrUpdateBatch(List<CmsPageVisitStatsDO> stats);

    /**
     * 删除指定日期之前的统计数据
     */
    default int deleteByDateBefore(LocalDate beforeDate) {
        return delete(new LambdaQueryWrapperX<CmsPageVisitStatsDO>()
                .lt(CmsPageVisitStatsDO::getStatDate, beforeDate));
    }

    /**
     * 检查统计数据是否存在
     */
    default boolean existsByPageIdAndDate(Long pageId, LocalDate statDate) {
        return selectCount(new LambdaQueryWrapperX<CmsPageVisitStatsDO>()
                .eq(CmsPageVisitStatsDO::getPageId, pageId)
                .eq(CmsPageVisitStatsDO::getStatDate, statDate)) > 0;
    }

    /**
     * 查询需要统计的日期列表（没有统计数据的日期）
     */
    List<LocalDate> selectMissingStatDates();

    /**
     * 查询热门页面（根据访问量排序）
     */
    List<CmsPageVisitStatsDO> selectHotPages(@Param("startDate") LocalDate startDate, 
                                           @Param("endDate") LocalDate endDate, 
                                           @Param("limit") Integer limit);

    /**
     * 删除指定日期的统计数据
     */
    Integer deleteByStatDate(@Param("statDate") LocalDate statDate);

    /**
     * 批量插入统计数据
     */
    Integer insertBatch(@Param("list") List<CmsPageVisitStatsDO> statsList);

    /**
     * 获取页面总访问量
     */
    @Select("SELECT COALESCE(SUM(total_visits), 0) FROM cms_page_visit_stats " +
            "WHERE page_id = #{pageId} AND deleted = 0")
    Long selectTotalVisitCount(@Param("pageId") Long pageId);

}