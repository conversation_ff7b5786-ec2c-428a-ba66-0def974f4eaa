package cn.iocoder.yudao.module.cms.service.diy;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.diy.vo.version.CmsDiyPageVersionPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.diy.vo.version.CmsDiyPageVersionRollbackReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageVersionDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * CMS DIY页面版本 Service 接口
 *
 * <AUTHOR>
 */
public interface CmsDiyPageVersionService {

    /**
     * 创建页面版本
     * 在页面发布时自动调用
     *
     * @param pageId 页面ID
     * @param versionName 版本名称
     * @param content 页面内容
     * @param remark 备注
     * @return 版本ID
     */
    Long createPageVersion(Long pageId, String versionName, String content, String remark);

    /**
     * 获得页面版本
     *
     * @param id 版本ID
     * @return 版本信息
     */
    CmsDiyPageVersionDO getPageVersion(Long id);

    /**
     * 获得页面的版本历史分页
     *
     * @param pageReqVO 分页查询
     * @return 版本历史分页
     */
    PageResult<CmsDiyPageVersionDO> getPageVersionPage(CmsDiyPageVersionPageReqVO pageReqVO);

    /**
     * 获得页面的所有版本历史
     *
     * @param pageId 页面ID
     * @return 版本历史列表
     */
    List<CmsDiyPageVersionDO> getPageVersionList(Long pageId);

    /**
     * 获得页面的最新版本
     *
     * @param pageId 页面ID
     * @return 最新版本
     */
    CmsDiyPageVersionDO getLatestPageVersion(Long pageId);

    /**
     * 获得页面的已发布版本
     *
     * @param pageId 页面ID
     * @param version 版本号
     * @return 版本信息
     */
    CmsDiyPageVersionDO getPublishedVersion(Long pageId, Integer version);

    /**
     * 回滚页面到指定版本
     * 1. 获取指定版本的内容
     * 2. 创建新版本（内容为回滚版本的内容）
     * 3. 更新页面内容为回滚版本的内容
     *
     * @param rollbackReqVO 回滚请求
     */
    void rollbackPageVersion(@Valid CmsDiyPageVersionRollbackReqVO rollbackReqVO);

    /**
     * 清理页面的旧版本
     * 保留最近N个版本
     *
     * @param pageId 页面ID
     * @param keepCount 保留的版本数量
     * @return 清理的版本数量
     */
    Integer cleanupOldVersions(Long pageId, Integer keepCount);

    /**
     * 批量清理所有页面的旧版本
     * 定时任务调用
     *
     * @param keepCount 每个页面保留的版本数量
     * @return 总共清理的版本数量
     */
    Integer cleanupAllOldVersions(Integer keepCount);

    /**
     * 比较两个版本的差异
     *
     * @param versionId1 版本1 ID
     * @param versionId2 版本2 ID
     * @return 差异信息（JSON格式）
     */
    String compareVersions(Long versionId1, Long versionId2);

    /**
     * 删除页面的所有版本
     * 删除页面时调用
     *
     * @param pageId 页面ID
     */
    void deletePageVersions(Long pageId);

    /**
     * 更新版本备注
     *
     * @param id 版本ID
     * @param remark 新备注
     */
    void updateVersionRemark(Long id, String remark);

    /**
     * 标记版本为已发布
     *
     * @param pageId 页面ID
     * @param version 版本号
     */
    void markVersionAsPublished(Long pageId, Integer version);

    /**
     * 获得页面版本数量
     *
     * @param pageId 页面ID
     * @return 版本数量
     */
    Long getVersionCount(Long pageId);

    /**
     * 校验版本是否存在
     *
     * @param id 版本ID
     * @return 版本信息
     */
    CmsDiyPageVersionDO validateVersionExists(Long id);

    /**
     * 校验是否可以回滚到指定版本
     *
     * @param pageId 页面ID
     * @param versionId 版本ID
     */
    void validateCanRollback(Long pageId, Long versionId);
}