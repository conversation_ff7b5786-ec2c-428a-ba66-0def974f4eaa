package cn.iocoder.yudao.module.cms.controller.admin.car.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 配置包特性 VO
 *
 * <AUTHOR>
 */
@Schema(description = "配置包特性 VO")
@Data
public class CarPackageFeatureVO {

    @Schema(description = "特性名称", example = "18\" alloy wheel")
    private String name;

    @Schema(description = "是否包含", example = "true")
    private Boolean included;

}