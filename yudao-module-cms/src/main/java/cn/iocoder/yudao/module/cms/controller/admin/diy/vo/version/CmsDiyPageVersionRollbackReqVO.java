package cn.iocoder.yudao.module.cms.controller.admin.diy.vo.version;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * CMS DIY页面版本回滚 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - CMS DIY页面版本回滚 Request VO")
@Data
public class CmsDiyPageVersionRollbackReqVO {

    @Schema(description = "版本ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "版本ID不能为空")
    private Long versionId;

    @Schema(description = "当前页面版本号（用于乐观锁）", requiredMode = Schema.RequiredMode.REQUIRED, example = "5")
    @NotNull(message = "当前版本号不能为空")
    private Integer currentVersion;
}