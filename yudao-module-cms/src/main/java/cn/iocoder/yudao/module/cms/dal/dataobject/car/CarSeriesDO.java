package cn.iocoder.yudao.module.cms.dal.dataobject.car;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 车系 DO
 *
 * <AUTHOR>
 */
@TableName("cms_car_series")
@KeySequence("cms_car_series_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CarSeriesDO extends BaseDO {

    /**
     * 车系ID
     */
    @TableId
    private Long id;

    /**
     * 车系代码
     *
     * 如：tiggo-7, tiggo-8
     */
    private String code;

    /**
     * 车系名称
     */
    private String name;

    /**
     * 英文名称
     */
    private String nameEn;

    /**
     * 车系描述
     */
    private String description;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 状态
     *
     * 枚举 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer status;

}