package cn.iocoder.yudao.module.cms.service.car.validator;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * JSON Schema 验证器
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class JsonSchemaValidator {

    private final ObjectMapper objectMapper;

    public JsonSchemaValidator() {
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 验证Schema格式是否正确
     *
     * @param schema Schema配置
     * @return 验证结果
     */
    public boolean validateSchema(Map<String, Object> schema) {
        if (schema == null || schema.isEmpty()) {
            return true;
        }

        try {
            // 检查基本结构
            if (!schema.containsKey("type")) {
                log.warn("Schema缺少type字段");
                return false;
            }

            String type = (String) schema.get("type");
            
            // 根据类型验证
            switch (type) {
                case "object":
                    return validateObjectSchema(schema);
                case "array":
                    return validateArraySchema(schema);
                case "string":
                case "number":
                case "integer":
                case "boolean":
                    return validatePrimitiveSchema(schema, type);
                default:
                    log.warn("不支持的Schema类型: {}", type);
                    return false;
            }
        } catch (Exception e) {
            log.error("Schema验证失败", e);
            return false;
        }
    }

    /**
     * 验证数据是否符合Schema
     *
     * @param schema Schema配置
     * @param data   数据
     * @return 验证结果
     */
    public boolean validateData(Map<String, Object> schema, Map<String, Object> data) {
        if (schema == null || schema.isEmpty()) {
            return true;
        }

        try {
            return validateDataBySchema(schema, data);
        } catch (Exception e) {
            log.error("数据验证失败", e);
            return false;
        }
    }

    /**
     * 验证对象类型Schema
     */
    private boolean validateObjectSchema(Map<String, Object> schema) {
        // 检查properties字段
        if (schema.containsKey("properties")) {
            Object properties = schema.get("properties");
            if (!(properties instanceof Map)) {
                log.warn("properties必须是对象类型");
                return false;
            }
            
            @SuppressWarnings("unchecked")
            Map<String, Object> propertiesMap = (Map<String, Object>) properties;
            
            // 验证每个属性的Schema
            for (Map.Entry<String, Object> entry : propertiesMap.entrySet()) {
                if (!(entry.getValue() instanceof Map)) {
                    log.warn("属性{}的Schema必须是对象", entry.getKey());
                    return false;
                }
                
                @SuppressWarnings("unchecked")
                Map<String, Object> propertySchema = (Map<String, Object>) entry.getValue();
                if (!validateSchema(propertySchema)) {
                    return false;
                }
            }
        }

        // 检查required字段
        if (schema.containsKey("required")) {
            Object required = schema.get("required");
            if (!(required instanceof List)) {
                log.warn("required必须是数组类型");
                return false;
            }
        }

        return true;
    }

    /**
     * 验证数组类型Schema
     */
    private boolean validateArraySchema(Map<String, Object> schema) {
        // 检查items字段
        if (schema.containsKey("items")) {
            Object items = schema.get("items");
            if (!(items instanceof Map)) {
                log.warn("items必须是对象类型");
                return false;
            }
            
            @SuppressWarnings("unchecked")
            Map<String, Object> itemsSchema = (Map<String, Object>) items;
            return validateSchema(itemsSchema);
        }

        return true;
    }

    /**
     * 验证基本类型Schema
     */
    private boolean validatePrimitiveSchema(Map<String, Object> schema, String type) {
        switch (type) {
            case "string":
                return validateStringSchema(schema);
            case "number":
            case "integer":
                return validateNumberSchema(schema);
            case "boolean":
                return true;
            default:
                return false;
        }
    }

    /**
     * 验证字符串Schema
     */
    private boolean validateStringSchema(Map<String, Object> schema) {
        // 检查enum
        if (schema.containsKey("enum")) {
            Object enumValue = schema.get("enum");
            if (!(enumValue instanceof List)) {
                log.warn("enum必须是数组类型");
                return false;
            }
        }

        // 检查minLength和maxLength
        if (schema.containsKey("minLength")) {
            Object minLength = schema.get("minLength");
            if (!(minLength instanceof Number)) {
                log.warn("minLength必须是数字类型");
                return false;
            }
        }

        if (schema.containsKey("maxLength")) {
            Object maxLength = schema.get("maxLength");
            if (!(maxLength instanceof Number)) {
                log.warn("maxLength必须是数字类型");
                return false;
            }
        }

        return true;
    }

    /**
     * 验证数字Schema
     */
    private boolean validateNumberSchema(Map<String, Object> schema) {
        // 检查minimum和maximum
        if (schema.containsKey("minimum")) {
            Object minimum = schema.get("minimum");
            if (!(minimum instanceof Number)) {
                log.warn("minimum必须是数字类型");
                return false;
            }
        }

        if (schema.containsKey("maximum")) {
            Object maximum = schema.get("maximum");
            if (!(maximum instanceof Number)) {
                log.warn("maximum必须是数字类型");
                return false;
            }
        }

        return true;
    }

    /**
     * 根据Schema验证数据
     */
    private boolean validateDataBySchema(Map<String, Object> schema, Object data) {
        String type = (String) schema.get("type");
        
        switch (type) {
            case "object":
                return validateObjectData(schema, data);
            case "array":
                return validateArrayData(schema, data);
            case "string":
                return validateStringData(schema, data);
            case "number":
                return validateNumberData(schema, data, false);
            case "integer":
                return validateNumberData(schema, data, true);
            case "boolean":
                return data instanceof Boolean;
            default:
                return false;
        }
    }

    /**
     * 验证对象数据
     */
    @SuppressWarnings("unchecked")
    private boolean validateObjectData(Map<String, Object> schema, Object data) {
        if (!(data instanceof Map)) {
            return false;
        }

        Map<String, Object> dataMap = (Map<String, Object>) data;

        // 检查required字段
        if (schema.containsKey("required")) {
            List<String> required = (List<String>) schema.get("required");
            for (String requiredField : required) {
                if (!dataMap.containsKey(requiredField)) {
                    log.warn("缺少必填字段: {}", requiredField);
                    return false;
                }
            }
        }

        // 检查properties
        if (schema.containsKey("properties")) {
            Map<String, Object> properties = (Map<String, Object>) schema.get("properties");
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                
                if (properties.containsKey(key)) {
                    Map<String, Object> propertySchema = (Map<String, Object>) properties.get(key);
                    if (!validateDataBySchema(propertySchema, value)) {
                        log.warn("字段{}验证失败", key);
                        return false;
                    }
                }
            }
        }

        return true;
    }

    /**
     * 验证数组数据
     */
    @SuppressWarnings("unchecked")
    private boolean validateArrayData(Map<String, Object> schema, Object data) {
        if (!(data instanceof List)) {
            return false;
        }

        List<Object> dataList = (List<Object>) data;

        // 检查items
        if (schema.containsKey("items")) {
            Map<String, Object> itemsSchema = (Map<String, Object>) schema.get("items");
            for (Object item : dataList) {
                if (!validateDataBySchema(itemsSchema, item)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 验证字符串数据
     */
    @SuppressWarnings("unchecked")
    private boolean validateStringData(Map<String, Object> schema, Object data) {
        if (!(data instanceof String)) {
            return false;
        }

        String stringData = (String) data;

        // 检查enum
        if (schema.containsKey("enum")) {
            List<String> enumValues = (List<String>) schema.get("enum");
            if (!enumValues.contains(stringData)) {
                log.warn("值{}不在枚举范围内", stringData);
                return false;
            }
        }

        // 检查长度限制
        if (schema.containsKey("minLength")) {
            int minLength = ((Number) schema.get("minLength")).intValue();
            if (stringData.length() < minLength) {
                log.warn("字符串长度不能小于{}", minLength);
                return false;
            }
        }

        if (schema.containsKey("maxLength")) {
            int maxLength = ((Number) schema.get("maxLength")).intValue();
            if (stringData.length() > maxLength) {
                log.warn("字符串长度不能大于{}", maxLength);
                return false;
            }
        }

        return true;
    }

    /**
     * 验证数字数据
     */
    private boolean validateNumberData(Map<String, Object> schema, Object data, boolean isInteger) {
        if (!(data instanceof Number)) {
            return false;
        }

        Number numberData = (Number) data;

        // 如果要求整数，检查是否为整数
        if (isInteger && !(data instanceof Integer) && numberData.doubleValue() != numberData.longValue()) {
            log.warn("值必须是整数");
            return false;
        }

        // 检查数值范围
        if (schema.containsKey("minimum")) {
            double minimum = ((Number) schema.get("minimum")).doubleValue();
            if (numberData.doubleValue() < minimum) {
                log.warn("值不能小于{}", minimum);
                return false;
            }
        }

        if (schema.containsKey("maximum")) {
            double maximum = ((Number) schema.get("maximum")).doubleValue();
            if (numberData.doubleValue() > maximum) {
                log.warn("值不能大于{}", maximum);
                return false;
            }
        }

        return true;
    }

}