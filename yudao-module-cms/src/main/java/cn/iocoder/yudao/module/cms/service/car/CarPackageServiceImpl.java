package cn.iocoder.yudao.module.cms.service.car;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarPackageCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarPackagePageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarPackageUpdateReqVO;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarPackageDO;
import cn.iocoder.yudao.module.cms.dal.mysql.car.CarPackageMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.cms.enums.ErrorCodeConstants.CAR_PACKAGE_NOT_EXISTS;

/**
 * 配置包管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CarPackageServiceImpl implements CarPackageService {

    @Resource
    private CarPackageMapper carPackageMapper;

    @Override
    public Long createCarPackage(CarPackageCreateReqVO createReqVO) {
        // 插入
        CarPackageDO carPackage = BeanUtils.toBean(createReqVO, CarPackageDO.class);
        carPackageMapper.insert(carPackage);
        // 返回
        return carPackage.getId();
    }

    @Override
    public void updateCarPackage(CarPackageUpdateReqVO updateReqVO) {
        // 校验存在
        validateCarPackageExists(updateReqVO.getId());

        // 更新
        CarPackageDO updateObj = BeanUtils.toBean(updateReqVO, CarPackageDO.class);
        carPackageMapper.updateById(updateObj);
    }

    @Override
    public void deleteCarPackage(Long id) {
        // 校验存在
        validateCarPackageExists(id);
        // 删除
        carPackageMapper.deleteById(id);
    }

    private void validateCarPackageExists(Long id) {
        if (carPackageMapper.selectById(id) == null) {
            throw exception(CAR_PACKAGE_NOT_EXISTS);
        }
    }

    @Override
    public CarPackageDO getCarPackage(Long id) {
        return carPackageMapper.selectById(id);
    }

    @Override
    public PageResult<CarPackageDO> getCarPackagePage(CarPackagePageReqVO pageReqVO) {
        return carPackageMapper.selectPage(pageReqVO);
    }

    @Override
    public List<CarPackageDO> getCarPackageList(Integer status) {
        return carPackageMapper.selectListByStatus(status);
    }

    @Override
    public CarPackageDO getCarPackageByCode(String code) {
        return carPackageMapper.selectByCode(code);
    }

    @Override
    public List<CarPackageDO> getCarPackageListByPriceRange(BigDecimal minPrice, BigDecimal maxPrice, Integer status) {
        return carPackageMapper.selectListByPriceRange(minPrice, maxPrice, status);
    }

    @Override
    public List<CarPackageDO> getHotCarPackageList(Integer limit) {
        return carPackageMapper.selectHotCarPackageList(limit);
    }

    @Override
    public List<CarPackageDO> getCarPackageListByModelId(Long modelId) {
        return carPackageMapper.selectListByModelId(modelId);
    }

}