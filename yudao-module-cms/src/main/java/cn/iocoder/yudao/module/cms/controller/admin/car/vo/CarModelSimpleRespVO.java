package cn.iocoder.yudao.module.cms.controller.admin.car.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 车型精简信息 Response VO")
@Data
public class CarModelSimpleRespVO {

    @Schema(description = "车型编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "车型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "瑞虎8 PRO")
    private String name;

}