package cn.iocoder.yudao.module.cms.controller.admin.diy;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.controller.admin.diy.vo.version.CmsDiyPageVersionPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.diy.vo.version.CmsDiyPageVersionRespVO;
import cn.iocoder.yudao.module.cms.controller.admin.diy.vo.version.CmsDiyPageVersionRollbackReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageVersionDO;
import cn.iocoder.yudao.module.cms.service.diy.CmsDiyPageVersionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * CMS DIY页面版本管理 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - CMS DIY页面版本管理")
@RestController
@RequestMapping("/cms/diy-page-version")
@Validated
@Slf4j
public class CmsDiyPageVersionController {

    @Resource
    private CmsDiyPageVersionService versionService;

    @GetMapping("/get")
    @Operation(summary = "获得页面版本")
    @Parameter(name = "id", description = "版本ID", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('cms:diy-page:query')")
    public CommonResult<CmsDiyPageVersionRespVO> getPageVersion(@RequestParam("id") Long id) {
        CmsDiyPageVersionDO version = versionService.getPageVersion(id);
        return success(BeanUtils.toBean(version, CmsDiyPageVersionRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得页面版本分页")
    @PreAuthorize("@ss.hasPermission('cms:diy-page:query')")
    public CommonResult<PageResult<CmsDiyPageVersionRespVO>> getPageVersionPage(@Valid CmsDiyPageVersionPageReqVO pageVO) {
        PageResult<CmsDiyPageVersionDO> pageResult = versionService.getPageVersionPage(pageVO);
        return success(BeanUtils.toBean(pageResult, CmsDiyPageVersionRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得页面的所有版本历史")
    @Parameter(name = "pageId", description = "页面ID", required = true)
    @PreAuthorize("@ss.hasPermission('cms:diy-page:query')")
    public CommonResult<List<CmsDiyPageVersionRespVO>> getPageVersionList(@RequestParam("pageId") Long pageId) {
        List<CmsDiyPageVersionDO> list = versionService.getPageVersionList(pageId);
        return success(BeanUtils.toBean(list, CmsDiyPageVersionRespVO.class));
    }

    @GetMapping("/latest")
    @Operation(summary = "获得页面的最新版本")
    @Parameter(name = "pageId", description = "页面ID", required = true)
    @PreAuthorize("@ss.hasPermission('cms:diy-page:query')")
    public CommonResult<CmsDiyPageVersionRespVO> getLatestPageVersion(@RequestParam("pageId") Long pageId) {
        CmsDiyPageVersionDO version = versionService.getLatestPageVersion(pageId);
        return success(BeanUtils.toBean(version, CmsDiyPageVersionRespVO.class));
    }

    @GetMapping("/published")
    @Operation(summary = "获得页面的已发布版本")
    @Parameter(name = "pageId", description = "页面ID", required = true)
    @Parameter(name = "version", description = "版本号", required = true)
    @PreAuthorize("@ss.hasPermission('cms:diy-page:query')")
    public CommonResult<CmsDiyPageVersionRespVO> getPublishedVersion(@RequestParam("pageId") Long pageId,
                                                                     @RequestParam("version") Integer version) {
        CmsDiyPageVersionDO versionDO = versionService.getPublishedVersion(pageId, version);
        return success(BeanUtils.toBean(versionDO, CmsDiyPageVersionRespVO.class));
    }

    @PostMapping("/rollback")
    @Operation(summary = "回滚页面到指定版本")
    @PreAuthorize("@ss.hasPermission('cms:diy-page:update')")
    public CommonResult<Boolean> rollbackPageVersion(@Valid @RequestBody CmsDiyPageVersionRollbackReqVO rollbackReqVO) {
        versionService.rollbackPageVersion(rollbackReqVO);
        return success(true);
    }

    @PostMapping("/cleanup")
    @Operation(summary = "清理页面的旧版本")
    @Parameter(name = "pageId", description = "页面ID", required = true)
    @Parameter(name = "keepCount", description = "保留的版本数量", required = true)
    @PreAuthorize("@ss.hasPermission('cms:diy-page:delete')")
    public CommonResult<Integer> cleanupOldVersions(@RequestParam("pageId") Long pageId,
                                                    @RequestParam("keepCount") Integer keepCount) {
        Integer cleaned = versionService.cleanupOldVersions(pageId, keepCount);
        return success(cleaned);
    }

    @PostMapping("/cleanup-all")
    @Operation(summary = "批量清理所有页面的旧版本")
    @Parameter(name = "keepCount", description = "每个页面保留的版本数量", required = true)
    @PreAuthorize("@ss.hasPermission('cms:diy-page:delete')")
    public CommonResult<Integer> cleanupAllOldVersions(@RequestParam("keepCount") Integer keepCount) {
        Integer cleaned = versionService.cleanupAllOldVersions(keepCount);
        return success(cleaned);
    }

    @GetMapping("/compare")
    @Operation(summary = "比较两个版本的差异")
    @Parameter(name = "versionId1", description = "版本1 ID", required = true)
    @Parameter(name = "versionId2", description = "版本2 ID", required = true)
    @PreAuthorize("@ss.hasPermission('cms:diy-page:query')")
    public CommonResult<String> compareVersions(@RequestParam("versionId1") Long versionId1,
                                                @RequestParam("versionId2") Long versionId2) {
        String comparison = versionService.compareVersions(versionId1, versionId2);
        return success(comparison);
    }

    @PutMapping("/update-remark")
    @Operation(summary = "更新版本备注")
    @Parameter(name = "id", description = "版本ID", required = true)
    @Parameter(name = "remark", description = "新备注", required = true)
    @PreAuthorize("@ss.hasPermission('cms:diy-page:update')")
    public CommonResult<Boolean> updateVersionRemark(@RequestParam("id") Long id,
                                                     @RequestParam("remark") String remark) {
        versionService.updateVersionRemark(id, remark);
        return success(true);
    }

    @GetMapping("/count")
    @Operation(summary = "获得页面版本数量")
    @Parameter(name = "pageId", description = "页面ID", required = true)
    @PreAuthorize("@ss.hasPermission('cms:diy-page:query')")
    public CommonResult<Long> getVersionCount(@RequestParam("pageId") Long pageId) {
        Long count = versionService.getVersionCount(pageId);
        return success(count);
    }
}