package cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 车型融资方案 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CarModelFinancePlanRespVO extends CarModelFinancePlanBaseVO {

    @Schema(description = "方案ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime updateTime;

    @Schema(description = "融资选项名称", example = "银行贷款")
    private String optionName;

    @Schema(description = "期限名称", example = "36个月")
    private String termName;

    @Schema(description = "期限月数", example = "36")
    private Integer months;

    @Schema(description = "首付名称", example = "20%首付")
    private String downPaymentName;

    @Schema(description = "首付比例值", example = "0.20")
    private java.math.BigDecimal downPaymentValue;

    @Schema(description = "可选的融资选项列表", example = "[]")
    private List<FinanceOptionRespVO> financeOptions;

}