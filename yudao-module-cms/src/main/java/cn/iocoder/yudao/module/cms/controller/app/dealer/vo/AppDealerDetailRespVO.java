package cn.iocoder.yudao.module.cms.controller.app.dealer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "应用 App - 经销商详情 Response VO")
@Data
public class AppDealerDetailRespVO {

    @Schema(description = "经销商ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "经销商名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "Allen Motor Romford")
    private String name;

    @Schema(description = "详细地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "17 London Rd, Romford RM7 9QB Greater London")
    private String address;

    @Schema(description = "邮政编码", example = "RM7 9QB")
    private String postcode;

    @Schema(description = "联系电话", example = "01708 123456")
    private String phone;

    @Schema(description = "电子邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "网站地址", example = "https://www.allenmotorgroup.co.uk")
    private String website;

    @Schema(description = "纬度", requiredMode = Schema.RequiredMode.REQUIRED, example = "51.5758311")
    private Double latitude;

    @Schema(description = "经度", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.1770876")
    private Double longitude;

    @Schema(description = "地区", requiredMode = Schema.RequiredMode.REQUIRED, example = "London")
    private String region;

    @Schema(description = "服务列表", example = "[\"Sales\", \"Service\", \"Parts\", \"Test Drive\", \"Finance\"]")
    private List<String> services;

    @Schema(description = "状态：0-禁用，1-启用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

}