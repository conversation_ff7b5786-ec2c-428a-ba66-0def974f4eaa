package cn.iocoder.yudao.module.cms.service.car;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelUpdateReqVO;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarModelDO;
import cn.iocoder.yudao.module.cms.dal.mysql.car.CarModelMapper;
import cn.iocoder.yudao.module.cms.framework.car.constant.CarCacheConstants;
import cn.iocoder.yudao.module.cms.framework.car.util.CarCacheHelper;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.cms.enums.ErrorCodeConstants.CAR_MODEL_NOT_EXISTS;

/**
 * 车型管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CarModelServiceImpl implements CarModelService {

    @Resource
    private CarModelMapper carModelMapper;
    
    @Resource
    private CarCacheHelper carCacheHelper;

    @Override
    @Caching(evict = {
        @CacheEvict(cacheNames = CarCacheConstants.CAR_MODEL_LIST_BY_SERIES, allEntries = true),
        @CacheEvict(cacheNames = CarCacheConstants.CAR_MODEL_LIST_BY_PRICE, allEntries = true),
        @CacheEvict(cacheNames = CarCacheConstants.CAR_MODEL_HOT_LIST, allEntries = true)
    })
    public Long createCarModel(CarModelCreateReqVO createReqVO) {
        // 插入
        CarModelDO carModel = BeanUtils.toBean(createReqVO, CarModelDO.class);
        carModelMapper.insert(carModel);
        // 返回
        return carModel.getId();
    }

    @Override
    @Caching(evict = {
        @CacheEvict(cacheNames = CarCacheConstants.CAR_MODEL, key = "#updateReqVO.id"),
        @CacheEvict(cacheNames = CarCacheConstants.CAR_MODEL_LIST_BY_SERIES, allEntries = true),
        @CacheEvict(cacheNames = CarCacheConstants.CAR_MODEL_LIST_BY_PRICE, allEntries = true),
        @CacheEvict(cacheNames = CarCacheConstants.CAR_MODEL_HOT_LIST, allEntries = true)
    })
    public void updateCarModel(CarModelUpdateReqVO updateReqVO) {
        // 校验存在
        validateCarModelExists(updateReqVO.getId());

        // 更新
        CarModelDO updateObj = BeanUtils.toBean(updateReqVO, CarModelDO.class);
        carModelMapper.updateById(updateObj);
    }

    @Override
    @Caching(evict = {
        @CacheEvict(cacheNames = CarCacheConstants.CAR_MODEL, key = "#id"),
        @CacheEvict(cacheNames = CarCacheConstants.CAR_MODEL_LIST_BY_SERIES, allEntries = true),
        @CacheEvict(cacheNames = CarCacheConstants.CAR_MODEL_LIST_BY_PRICE, allEntries = true),
        @CacheEvict(cacheNames = CarCacheConstants.CAR_MODEL_HOT_LIST, allEntries = true)
    })
    public void deleteCarModel(Long id) {
        // 校验存在
        validateCarModelExists(id);
        // 删除
        carModelMapper.deleteById(id);
    }

    private void validateCarModelExists(Long id) {
        if (carModelMapper.selectById(id) == null) {
            throw exception(CAR_MODEL_NOT_EXISTS);
        }
    }

    @Override
    @Cacheable(cacheNames = CarCacheConstants.CAR_MODEL, key = "#id", unless = "#result == null")
    public CarModelDO getCarModel(Long id) {
        return carModelMapper.selectById(id);
    }

    @Override
    public PageResult<CarModelDO> getCarModelPage(CarModelPageReqVO pageReqVO) {
        return carModelMapper.selectPage(pageReqVO);
    }

    @Override
    @Cacheable(cacheNames = CarCacheConstants.CAR_MODEL_LIST_BY_SERIES, 
              key = "T(cn.iocoder.yudao.module.cms.framework.car.util.CarCacheHelper).generateKey(#seriesId, #status)",
              unless = "#result == null || #result.isEmpty()")
    public List<CarModelDO> getCarModelList(Long seriesId, Integer status) {
        return carModelMapper.selectListBySeriesIdAndStatus(seriesId, status);
    }

    @Override
    @Cacheable(cacheNames = CarCacheConstants.CAR_MODEL, key = "'code:' + #code", unless = "#result == null")
    public CarModelDO getCarModelByCode(String code) {
        return carModelMapper.selectByCode(code);
    }

    @Override
    @Cacheable(cacheNames = CarCacheConstants.CAR_MODEL_LIST_BY_PRICE,
              key = "T(cn.iocoder.yudao.module.cms.framework.car.util.CarCacheHelper).generateKey(#minPrice, #maxPrice, #status)",
              unless = "#result == null || #result.isEmpty()")
    public List<CarModelDO> getCarModelListByPriceRange(BigDecimal minPrice, BigDecimal maxPrice, Integer status) {
        return carModelMapper.selectListByPriceRange(minPrice, maxPrice, status);
    }

    @Override
    @Cacheable(cacheNames = CarCacheConstants.CAR_MODEL_HOT_LIST, key = "#limit",
              unless = "#result == null || #result.isEmpty()")
    public List<CarModelDO> getHotCarModelList(Integer limit) {
        return carModelMapper.selectHotCarModelList(limit);
    }

    @Override
    @Cacheable(cacheNames = CarCacheConstants.CAR_MODEL_LIST_BY_SERIES, key = "'simple-list'",
              unless = "#result == null || #result.isEmpty()")
    public List<CarModelDO> getCarModelSimpleList() {
        return carModelMapper.selectList();
    }

}