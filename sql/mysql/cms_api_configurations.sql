-- 车型配置器API初始化配置
-- 作者：开发团队
-- 日期：2025-01-09
-- 版本：v1.0

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- API配置数据插入
-- ----------------------------

-- 车型配置器API - 默认版本（完整版）
INSERT INTO `cms_api_sql_configs` (`api_code`, `version`, `sql_content`, `description`, `is_default`, `status`) VALUES 
('car_configurator', 'default', 
'SELECT 
    JSON_OBJECT(
        ''model'', JSON_OBJECT(
            ''id'', m.code,
            ''name'', m.name,
            ''category'', m.category,
            ''description'', m.description,
            ''base_price'', m.base_price,
            ''image'', m.image_url,
            ''ev_icon'', m.ev_icon_url,
            ''badge'', m.badge,
            ''features'', CASE WHEN m.features IS NOT NULL THEN JSON_EXTRACT(m.features, ''$'') ELSE JSON_ARRAY() END
        ),
        ''packages'', COALESCE((
            SELECT JSON_ARRAYAGG(
                JSON_OBJECT(
                    ''id'', cp.package_code,
                    ''name'', cp.name,
                    ''price'', cp.price,
                    ''features_title'', cp.features_title,
                    ''features'', CASE WHEN cp.features IS NOT NULL THEN JSON_EXTRACT(cp.features, ''$'') ELSE JSON_ARRAY() END
                )
            )
            FROM cms_car_packages cp 
            WHERE cp.model_id = m.id AND cp.status = 0 AND cp.deleted = 0
            ORDER BY cp.sort_order
        ), JSON_ARRAY()),
        ''options'', JSON_OBJECT(
            ''colors'', COALESCE((
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                        ''id'', mo.option_code,
                        ''name'', mo.name,
                        ''price'', mo.price,
                        ''image'', mo.image_url,
                        ''config_data'', CASE WHEN mo.config_data IS NOT NULL THEN JSON_EXTRACT(mo.config_data, ''$'') ELSE JSON_OBJECT() END
                    )
                )
                FROM cms_car_model_options mo
                JOIN cms_car_option_types ot ON mo.option_type_id = ot.id
                WHERE mo.model_id = m.id AND ot.type_code = ''colors'' AND mo.status = 0 AND mo.deleted = 0
                ORDER BY mo.sort_order
            ), JSON_ARRAY()),
            ''interiors'', COALESCE((
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                        ''id'', mo.option_code,
                        ''name'', mo.name,
                        ''price'', mo.price,
                        ''image'', mo.image_url,
                        ''config_data'', CASE WHEN mo.config_data IS NOT NULL THEN JSON_EXTRACT(mo.config_data, ''$'') ELSE JSON_OBJECT() END
                    )
                )
                FROM cms_car_model_options mo
                JOIN cms_car_option_types ot ON mo.option_type_id = ot.id
                WHERE mo.model_id = m.id AND ot.type_code = ''interiors'' AND mo.status = 0 AND mo.deleted = 0
                ORDER BY mo.sort_order
            ), JSON_ARRAY()),
            ''wheels'', COALESCE((
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                        ''id'', mo.option_code,
                        ''name'', mo.name,
                        ''price'', mo.price,
                        ''image'', mo.image_url,
                        ''config_data'', CASE WHEN mo.config_data IS NOT NULL THEN JSON_EXTRACT(mo.config_data, ''$'') ELSE JSON_OBJECT() END
                    )
                )
                FROM cms_car_model_options mo
                JOIN cms_car_option_types ot ON mo.option_type_id = ot.id
                WHERE mo.model_id = m.id AND ot.type_code = ''wheels'' AND mo.status = 0 AND mo.deleted = 0
                ORDER BY mo.sort_order
            ), JSON_ARRAY())
        ),
        ''finance_plans'', COALESCE((
            SELECT JSON_ARRAYAGG(
                JSON_OBJECT(
                    ''id'', cmfp.id,
                    ''finance_option'', JSON_OBJECT(
                        ''id'', fo.option_code,
                        ''name'', fo.name,
                        ''description'', fo.description
                    ),
                    ''term'', CASE WHEN ft.id IS NOT NULL THEN JSON_OBJECT(
                        ''name'', ft.name,
                        ''rate'', ft.rate
                    ) ELSE NULL END,
                    ''down_payment'', CASE WHEN dpo.id IS NOT NULL THEN JSON_OBJECT(
                        ''name'', dpo.name,
                        ''value'', dpo.value
                    ) ELSE NULL END,
                    ''weekly_payment'', cmfp.weekly_payment,
                    ''monthly_payment'', cmfp.monthly_payment,
                    ''gfv'', cmfp.gfv,
                    ''km_allowance'', cmfp.km_allowance,
                    ''is_featured'', CASE WHEN cmfp.is_featured = 1 THEN true ELSE false END
                )
            )
            FROM cms_car_model_finance_plans cmfp
            JOIN cms_finance_options fo ON cmfp.finance_option_id = fo.id
            LEFT JOIN cms_finance_terms ft ON cmfp.term_id = ft.id
            LEFT JOIN cms_down_payment_options dpo ON cmfp.down_payment_id = dpo.id
            WHERE cmfp.model_id = m.id AND cmfp.status = 0 AND cmfp.deleted = 0
            ORDER BY cmfp.is_featured DESC, cmfp.sort_order
        ), JSON_ARRAY())
    ) as api_response
FROM cms_car_models m 
WHERE m.code = #{code} AND m.status = 0 AND m.deleted = 0', 
'车型配置器API默认版本 - 完整数据', 1, 0);

-- 车型配置器API - v1版本（完整版，与默认版本相同）
INSERT INTO `cms_api_sql_configs` (`api_code`, `version`, `sql_content`, `description`, `is_default`, `status`) VALUES 
('car_configurator', 'v1', 
'SELECT 
    JSON_OBJECT(
        ''model'', JSON_OBJECT(
            ''id'', m.code,
            ''name'', m.name,
            ''category'', m.category,
            ''description'', m.description,
            ''base_price'', m.base_price,
            ''image'', m.image_url,
            ''ev_icon'', m.ev_icon_url,
            ''badge'', m.badge,
            ''features'', CASE WHEN m.features IS NOT NULL THEN JSON_EXTRACT(m.features, ''$'') ELSE JSON_ARRAY() END
        ),
        ''packages'', COALESCE((
            SELECT JSON_ARRAYAGG(
                JSON_OBJECT(
                    ''id'', cp.package_code,
                    ''name'', cp.name,
                    ''price'', cp.price,
                    ''features_title'', cp.features_title,
                    ''features'', CASE WHEN cp.features IS NOT NULL THEN JSON_EXTRACT(cp.features, ''$'') ELSE JSON_ARRAY() END
                )
            )
            FROM cms_car_packages cp 
            WHERE cp.model_id = m.id AND cp.status = 0 AND cp.deleted = 0
            ORDER BY cp.sort_order
        ), JSON_ARRAY()),
        ''options'', JSON_OBJECT(
            ''colors'', COALESCE((
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                        ''id'', mo.option_code,
                        ''name'', mo.name,
                        ''price'', mo.price,
                        ''image'', mo.image_url,
                        ''config_data'', CASE WHEN mo.config_data IS NOT NULL THEN JSON_EXTRACT(mo.config_data, ''$'') ELSE JSON_OBJECT() END
                    )
                )
                FROM cms_car_model_options mo
                JOIN cms_car_option_types ot ON mo.option_type_id = ot.id
                WHERE mo.model_id = m.id AND ot.type_code = ''colors'' AND mo.status = 0 AND mo.deleted = 0
                ORDER BY mo.sort_order
            ), JSON_ARRAY()),
            ''interiors'', COALESCE((
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                        ''id'', mo.option_code,
                        ''name'', mo.name,
                        ''price'', mo.price,
                        ''image'', mo.image_url,
                        ''config_data'', CASE WHEN mo.config_data IS NOT NULL THEN JSON_EXTRACT(mo.config_data, ''$'') ELSE JSON_OBJECT() END
                    )
                )
                FROM cms_car_model_options mo
                JOIN cms_car_option_types ot ON mo.option_type_id = ot.id
                WHERE mo.model_id = m.id AND ot.type_code = ''interiors'' AND mo.status = 0 AND mo.deleted = 0
                ORDER BY mo.sort_order
            ), JSON_ARRAY()),
            ''wheels'', COALESCE((
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                        ''id'', mo.option_code,
                        ''name'', mo.name,
                        ''price'', mo.price,
                        ''image'', mo.image_url,
                        ''config_data'', CASE WHEN mo.config_data IS NOT NULL THEN JSON_EXTRACT(mo.config_data, ''$'') ELSE JSON_OBJECT() END
                    )
                )
                FROM cms_car_model_options mo
                JOIN cms_car_option_types ot ON mo.option_type_id = ot.id
                WHERE mo.model_id = m.id AND ot.type_code = ''wheels'' AND mo.status = 0 AND mo.deleted = 0
                ORDER BY mo.sort_order
            ), JSON_ARRAY())
        ),
        ''finance_plans'', COALESCE((
            SELECT JSON_ARRAYAGG(
                JSON_OBJECT(
                    ''id'', cmfp.id,
                    ''finance_option'', JSON_OBJECT(
                        ''id'', fo.option_code,
                        ''name'', fo.name,
                        ''description'', fo.description
                    ),
                    ''term'', CASE WHEN ft.id IS NOT NULL THEN JSON_OBJECT(
                        ''name'', ft.name,
                        ''rate'', ft.rate
                    ) ELSE NULL END,
                    ''down_payment'', CASE WHEN dpo.id IS NOT NULL THEN JSON_OBJECT(
                        ''name'', dpo.name,
                        ''value'', dpo.value
                    ) ELSE NULL END,
                    ''weekly_payment'', cmfp.weekly_payment,
                    ''monthly_payment'', cmfp.monthly_payment,
                    ''gfv'', cmfp.gfv,
                    ''km_allowance'', cmfp.km_allowance,
                    ''is_featured'', CASE WHEN cmfp.is_featured = 1 THEN true ELSE false END
                )
            )
            FROM cms_car_model_finance_plans cmfp
            JOIN cms_finance_options fo ON cmfp.finance_option_id = fo.id
            LEFT JOIN cms_finance_terms ft ON cmfp.term_id = ft.id
            LEFT JOIN cms_down_payment_options dpo ON cmfp.down_payment_id = dpo.id
            WHERE cmfp.model_id = m.id AND cmfp.status = 0 AND cmfp.deleted = 0
            ORDER BY cmfp.is_featured DESC, cmfp.sort_order
        ), JSON_ARRAY())
    ) as api_response
FROM cms_car_models m 
WHERE m.code = #{code} AND m.status = 0 AND m.deleted = 0', 
'车型配置器API v1版本 - 完整数据', 0, 0);

-- 车型配置器API - v2版本（简化版）
INSERT INTO `cms_api_sql_configs` (`api_code`, `version`, `sql_content`, `description`, `is_default`, `status`) VALUES 
('car_configurator', 'v2', 
'SELECT 
    JSON_OBJECT(
        ''vehicle'', JSON_OBJECT(
            ''identifier'', m.code,
            ''title'', m.name,
            ''cost'', m.base_price,
            ''thumbnail'', m.image_url
        ),
        ''color_options'', COALESCE((
            SELECT JSON_ARRAYAGG(
                JSON_OBJECT(
                    ''code'', mo.option_code,
                    ''label'', mo.name,
                    ''additional_cost'', mo.price,
                    ''preview_image'', mo.image_url
                )
            )
            FROM cms_car_model_options mo
            JOIN cms_car_option_types ot ON mo.option_type_id = ot.id
            WHERE mo.model_id = m.id AND ot.type_code = ''colors'' AND mo.status = 0 AND mo.deleted = 0
            ORDER BY mo.sort_order
        ), JSON_ARRAY())
    ) as api_response
FROM cms_car_models m 
WHERE m.code = #{code} AND m.status = 0 AND m.deleted = 0', 
'车型配置器API v2版本 - 简化响应格式', 0, 0);

-- 车型配置器API - 移动端版本（精简版）
INSERT INTO `cms_api_sql_configs` (`api_code`, `version`, `sql_content`, `description`, `is_default`, `status`) VALUES 
('car_configurator', 'mobile', 
'SELECT 
    JSON_OBJECT(
        ''model'', JSON_OBJECT(
            ''id'', m.code,
            ''name'', m.name,
            ''price'', m.base_price,
            ''image'', m.image_url
        ),
        ''colors'', COALESCE((
            SELECT JSON_ARRAYAGG(
                JSON_OBJECT(
                    ''id'', mo.option_code,
                    ''name'', mo.name,
                    ''price'', mo.price,
                    ''thumb'', CASE 
                        WHEN mo.thumbnail_urls IS NOT NULL THEN JSON_EXTRACT(mo.thumbnail_urls, ''$[0]'')
                        ELSE mo.image_url 
                    END
                )
            )
            FROM cms_car_model_options mo
            JOIN cms_car_option_types ot ON mo.option_type_id = ot.id
            WHERE mo.model_id = m.id AND ot.type_code = ''colors'' AND mo.status = 0 AND mo.deleted = 0
            ORDER BY mo.sort_order
            LIMIT 5
        ), JSON_ARRAY())
    ) as api_response
FROM cms_car_models m 
WHERE m.code = #{code} AND m.status = 0 AND m.deleted = 0', 
'车型配置器API移动端版本 - 精简数据', 0, 0);

-- 车型详情API - 默认版本
INSERT INTO `cms_api_sql_configs` (`api_code`, `version`, `sql_content`, `description`, `is_default`, `status`) VALUES 
('car_detail', 'default', 
'SELECT 
    JSON_OBJECT(
        ''model'', JSON_OBJECT(
            ''id'', m.code,
            ''name'', m.name,
            ''description'', m.description,
            ''base_price'', m.base_price,
            ''image'', m.image_url
        ),
        ''specs'', COALESCE((
            SELECT JSON_OBJECT(
                ''engine'', cms.engine,
                ''engine_type'', cms.engine_type,
                ''fuel_consumption'', cms.fuel_consumption,
                ''power'', cms.power,
                ''torque'', cms.torque,
                ''seats_num'', cms.seats_num,
                ''dimensions'', CASE WHEN cms.dimensions IS NOT NULL THEN JSON_EXTRACT(cms.dimensions, ''$'') ELSE JSON_OBJECT() END,
                ''performance'', CASE WHEN cms.performance IS NOT NULL THEN JSON_EXTRACT(cms.performance, ''$'') ELSE JSON_OBJECT() END
            )
            FROM cms_car_model_specs cms
            WHERE cms.model_id = m.id AND cms.deleted = 0
        ), JSON_OBJECT()),
        ''default_finance_plan'', COALESCE((
            SELECT JSON_OBJECT(
                ''finance_option'', JSON_OBJECT(
                    ''name'', fo.name,
                    ''description'', fo.description
                ),
                ''weekly_payment'', cmfp.weekly_payment,
                ''comparison_rate'', ft.rate,
                ''deposit_percentage'', dpo.value,
                ''term'', ft.name,
                ''gfv'', cmfp.gfv,
                ''km_allowance'', cmfp.km_allowance
            )
            FROM cms_car_model_finance_plans cmfp
            JOIN cms_finance_options fo ON cmfp.finance_option_id = fo.id
            LEFT JOIN cms_finance_terms ft ON cmfp.term_id = ft.id
            LEFT JOIN cms_down_payment_options dpo ON cmfp.down_payment_id = dpo.id
            WHERE cmfp.model_id = m.id AND cmfp.is_default = 1 AND cmfp.status = 0 AND cmfp.deleted = 0
            LIMIT 1
        ), JSON_OBJECT())
    ) as api_response
FROM cms_car_models m 
WHERE m.code = #{code} AND m.status = 0 AND m.deleted = 0', 
'车型详情API默认版本', 1, 0);

-- 车型列表API - 默认版本
INSERT INTO `cms_api_sql_configs` (`api_code`, `version`, `sql_content`, `description`, `is_default`, `status`) VALUES 
('car_models_list', 'default', 
'SELECT 
    JSON_OBJECT(
        ''total'', COUNT(*) OVER(),
        ''models'', JSON_ARRAYAGG(
            JSON_OBJECT(
                ''id'', m.code,
                ''name'', m.name,
                ''category'', m.category,
                ''base_price'', m.base_price,
                ''image'', m.image_url,
                ''badge'', m.badge,
                ''series_name'', cs.name
            )
        )
    ) as api_response
FROM cms_car_models m
LEFT JOIN cms_car_series cs ON m.series_id = cs.id
WHERE m.status = 0 AND m.deleted = 0
<if test="series != null">AND cs.code = #{series}</if>
<if test="category != null">AND m.category = #{category}</if>
ORDER BY m.sort_order, m.id
LIMIT #{pageSize} OFFSET (#{pageNo} - 1) * #{pageSize}', 
'车型列表API默认版本', 1, 0);

-- 车系列表API - 默认版本
INSERT INTO `cms_api_sql_configs` (`api_code`, `version`, `sql_content`, `description`, `is_default`, `status`) VALUES 
('car_series_list', 'default', 
'SELECT 
    JSON_OBJECT(
        ''series'', JSON_ARRAYAGG(
            JSON_OBJECT(
                ''id'', cs.code,
                ''name'', cs.name,
                ''name_en'', cs.name_en,
                ''description'', cs.description
            )
        )
    ) as api_response
FROM cms_car_series cs
WHERE cs.status = 0 AND cs.deleted = 0
ORDER BY cs.sort_order', 
'车系列表API默认版本', 1, 0);

-- 融资选项API - 默认版本
INSERT INTO `cms_api_sql_configs` (`api_code`, `version`, `sql_content`, `description`, `is_default`, `status`) VALUES 
('finance_options', 'default', 
'SELECT 
    JSON_OBJECT(
        ''finance_options'', JSON_ARRAYAGG(
            JSON_OBJECT(
                ''id'', fo.option_code,
                ''name'', fo.name,
                ''description'', fo.description,
                ''terms'', (
                    SELECT JSON_ARRAYAGG(
                        JSON_OBJECT(
                            ''id'', ft.term_code,
                            ''name'', ft.name,
                            ''rate'', ft.rate,
                            ''months'', ft.months
                        )
                    )
                    FROM cms_finance_terms ft
                    WHERE ft.option_id = fo.id AND ft.status = 0 AND ft.deleted = 0
                ),
                ''down_payments'', (
                    SELECT JSON_ARRAYAGG(
                        JSON_OBJECT(
                            ''id'', dpo.payment_code,
                            ''name'', dpo.name,
                            ''value'', dpo.value
                        )
                    )
                    FROM cms_down_payment_options dpo
                    WHERE dpo.option_id = fo.id AND dpo.status = 0 AND dpo.deleted = 0
                )
            )
        )
    ) as api_response
FROM cms_finance_options fo
WHERE fo.status = 0 AND fo.deleted = 0
ORDER BY fo.sort_order', 
'融资选项API默认版本', 1, 0);

-- 价格计算器API - 默认版本
INSERT INTO `cms_api_sql_configs` (`api_code`, `version`, `sql_content`, `description`, `is_default`, `status`) VALUES 
('price_calculator', 'default', 
'SELECT 
    JSON_OBJECT(
        ''base_price'', m.base_price,
        ''total_price'', m.base_price + COALESCE(package_price, 0) + COALESCE(options_price, 0),
        ''breakdown'', JSON_OBJECT(
            ''model'', JSON_OBJECT(
                ''name'', m.name,
                ''price'', m.base_price
            ),
            ''package'', CASE WHEN package_name IS NOT NULL THEN JSON_OBJECT(
                ''name'', package_name,
                ''price'', package_price
            ) ELSE NULL END,
            ''options'', CASE WHEN options_price > 0 THEN JSON_OBJECT(
                ''total_price'', options_price
            ) ELSE NULL END
        )
    ) as api_response
FROM cms_car_models m
LEFT JOIN (
    SELECT cp.model_id, cp.name as package_name, cp.price as package_price
    FROM cms_car_packages cp
    WHERE cp.package_code = #{packageCode} AND cp.status = 0 AND cp.deleted = 0
) pkg ON m.id = pkg.model_id
LEFT JOIN (
    SELECT mo.model_id, SUM(mo.price) as options_price
    FROM cms_car_model_options mo
    WHERE mo.option_code IN (#{colorCode}, #{interiorCode}, #{wheelCode}) 
    AND mo.status = 0 AND mo.deleted = 0
    GROUP BY mo.model_id
) opts ON m.id = opts.model_id
WHERE m.code = #{code} AND m.status = 0 AND m.deleted = 0', 
'价格计算器API默认版本', 1, 0);

COMMIT;
SET FOREIGN_KEY_CHECKS = 1;