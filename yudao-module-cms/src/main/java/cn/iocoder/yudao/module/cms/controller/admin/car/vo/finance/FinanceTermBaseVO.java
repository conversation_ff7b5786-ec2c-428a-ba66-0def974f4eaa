package cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import java.math.BigDecimal;

/**
 * 融资期限 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class FinanceTermBaseVO {

    @Schema(description = "融资选项ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "融资选项ID不能为空")
    private Long optionId;

    @Schema(description = "期限代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "term_36")
    @NotNull(message = "期限代码不能为空")
    @Length(max = 50, message = "期限代码长度不能超过50个字符")
    private String termCode;

    @Schema(description = "期限名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "36个月")
    @NotNull(message = "期限名称不能为空")
    @Length(max = 100, message = "期限名称长度不能超过100个字符")
    private String name;

    @Schema(description = "利率", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.0299")
    @NotNull(message = "利率不能为空")
    @DecimalMin(value = "0", message = "利率不能小于0")
    private BigDecimal rate;

    @Schema(description = "期限月数", requiredMode = Schema.RequiredMode.REQUIRED, example = "36")
    @NotNull(message = "期限月数不能为空")
    @Min(value = 1, message = "期限月数不能小于1")
    private Integer months;

    @Schema(description = "排序", example = "1")
    private Integer sortOrder;

    @Schema(description = "状态：0-禁用，1-启用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

}