package cn.iocoder.yudao.module.cms.controller.admin.car.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;

@Schema(description = "管理后台 - 车型配置选项更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CarModelOptionUpdateReqVO extends CarModelOptionBaseVO {

    @Schema(description = "配置选项ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "配置选项ID不能为空")
    private Long id;

}