package cn.iocoder.yudao.module.cms.service.car.api;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.api.ApiSqlConfigCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.api.ApiSqlConfigPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.api.ApiSqlConfigUpdateReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.api.ApiSqlConfigDO;
import cn.iocoder.yudao.module.cms.dal.mysql.car.api.ApiSqlConfigMapper;
import cn.iocoder.yudao.module.cms.framework.car.constant.CarCacheConstants;
import cn.iocoder.yudao.module.cms.framework.car.util.CarCacheHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.cms.enums.ErrorCodeConstants.*;

/**
 * API SQL配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ApiSqlConfigServiceImpl implements ApiSqlConfigService {

    @Resource
    private ApiSqlConfigMapper apiSqlConfigMapper;

    @Resource
    private SqlExecutionEngine sqlExecutionEngine;


    @Resource
    private CarCacheHelper carCacheHelper;

    @Override
    @Transactional
    @Caching(evict = {
        @CacheEvict(cacheNames = CarCacheConstants.API_SQL_CONFIG_LIST, allEntries = true),
        @CacheEvict(cacheNames = CarCacheConstants.API_SQL_CONFIG_CODES, allEntries = true),
        @CacheEvict(cacheNames = CarCacheConstants.API_SQL_CONFIG_VERSIONS,
                   key = "T(cn.iocoder.yudao.module.cms.framework.car.util.CarCacheHelper).generateKey(#createReqVO.apiCode)")
    })
    public Long createApiSqlConfig(ApiSqlConfigCreateReqVO createReqVO) {
        // 输入验证
        validateCreateRequest(createReqVO);
        
        // 检查API代码和版本是否已存在
        validateApiCodeVersionNotExists(createReqVO.getApiCode(), createReqVO.getVersion());
        
        // 验证SQL格式
        validateSqlContent(createReqVO.getSqlContent());

        // 插入
        ApiSqlConfigDO apiSqlConfig = BeanUtils.toBean(createReqVO, ApiSqlConfigDO.class);
        apiSqlConfigMapper.insert(apiSqlConfig);
        
        log.info("创建API SQL配置成功: id={}, apiCode={}, version={}", 
            apiSqlConfig.getId(), apiSqlConfig.getApiCode(), apiSqlConfig.getVersion());
        return apiSqlConfig.getId();
    }

    @Override
    @Transactional
    @Caching(evict = {
        @CacheEvict(cacheNames = CarCacheConstants.API_SQL_CONFIG, key = "#updateReqVO.id"),
        @CacheEvict(cacheNames = CarCacheConstants.API_SQL_CONFIG_LIST, allEntries = true),
        @CacheEvict(cacheNames = CarCacheConstants.API_SQL_CONFIG_DEFAULT, allEntries = true),
        @CacheEvict(cacheNames = CarCacheConstants.API_SQL_CONFIG_PARAMS, allEntries = true)
    })
    public void updateApiSqlConfig(ApiSqlConfigUpdateReqVO updateReqVO) {
        // 输入验证
        validateUpdateRequest(updateReqVO);
        
        // 校验配置存在
        ApiSqlConfigDO existingConfig = validateApiSqlConfigExists(updateReqVO.getId());
        
        // 验证SQL格式
        validateSqlContent(updateReqVO.getSqlContent());

        // 更新
        ApiSqlConfigDO updateObj = BeanUtils.toBean(updateReqVO, ApiSqlConfigDO.class);
        apiSqlConfigMapper.updateById(updateObj);
        
        log.info("更新API SQL配置成功: id={}, apiCode={}, version={}", 
            updateReqVO.getId(), existingConfig.getApiCode(), existingConfig.getVersion());
    }

    @Override
    @Transactional
    @Caching(evict = {
        @CacheEvict(cacheNames = CarCacheConstants.API_SQL_CONFIG, key = "#id"),
        @CacheEvict(cacheNames = CarCacheConstants.API_SQL_CONFIG_LIST, allEntries = true),
        @CacheEvict(cacheNames = CarCacheConstants.API_SQL_CONFIG_CODES, allEntries = true),
        @CacheEvict(cacheNames = CarCacheConstants.API_SQL_CONFIG_VERSIONS, allEntries = true),
        @CacheEvict(cacheNames = CarCacheConstants.API_SQL_CONFIG_DEFAULT, allEntries = true),
        @CacheEvict(cacheNames = CarCacheConstants.API_SQL_CONFIG_PARAMS, allEntries = true)
    })
    public void deleteApiSqlConfig(Long id) {
        // 获取配置信息用于缓存清理
        ApiSqlConfigDO config = validateApiSqlConfigExists(id);
        
        // 删除
        apiSqlConfigMapper.deleteById(id);
        
        log.info("删除API SQL配置成功: id={}, apiCode={}, version={}", 
            id, config.getApiCode(), config.getVersion());
    }

    private ApiSqlConfigDO validateApiSqlConfigExists(Long id) {
        ApiSqlConfigDO config = apiSqlConfigMapper.selectById(id);
        if (config == null) {
            throw exception(API_SQL_CONFIG_NOT_EXISTS);
        }
        return config;
    }

    @Override
    @Cacheable(cacheNames = CarCacheConstants.API_SQL_CONFIG, key = "#id", unless = "#result == null")
    public ApiSqlConfigDO getApiSqlConfig(Long id) {
        return apiSqlConfigMapper.selectById(id);
    }

    @Override
    public PageResult<ApiSqlConfigDO> getApiSqlConfigPage(ApiSqlConfigPageReqVO pageReqVO) {
        return apiSqlConfigMapper.selectPage(pageReqVO.getApiCode(), pageReqVO.getVersion(), 
            pageReqVO.getStatus(), pageReqVO.getPageNo(), pageReqVO.getPageSize());
    }

    @Override
    @Transactional
    public Long copyApiConfig(Long id, String newVersion) {
        ApiSqlConfigDO original = getApiSqlConfig(id);
        if (original == null) {
            throw exception(API_SQL_CONFIG_NOT_EXISTS);
        }

        // 检查新版本是否已存在
        ApiSqlConfigDO existing = apiSqlConfigMapper.selectByApiCodeAndVersion(
            original.getApiCode(), newVersion);
        if (existing != null) {
            throw new IllegalArgumentException("版本 " + newVersion + " 已存在");
        }

        // 创建新配置（使用BeanUtils优化字段复制）
        ApiSqlConfigDO newConfig = BeanUtils.toBean(original, ApiSqlConfigDO.class);
        newConfig.setId(null); // 清空ID，让数据库自动生成
        newConfig.setVersion(newVersion);
        newConfig.setDescription("复制自版本 " + original.getVersion());
        newConfig.setIsDefault(false);
        newConfig.setCreateTime(null); // 让框架自动设置创建时间
        newConfig.setUpdateTime(null); // 让框架自动设置更新时间

        apiSqlConfigMapper.insert(newConfig);
        
        log.info("复制API配置: {} -> {}", id, newConfig.getId());
        return newConfig.getId();
    }

    @Override
    @Cacheable(cacheNames = CarCacheConstants.API_SQL_CONFIG_LIST, 
              key = "T(cn.iocoder.yudao.module.cms.framework.car.util.CarCacheHelper).generateKey('status', #status)")
    public List<ApiSqlConfigDO> getApiSqlConfigList(Integer status) {
        return apiSqlConfigMapper.selectListByStatus(status);
    }

    @Override
    @Cacheable(cacheNames = CarCacheConstants.API_SQL_CONFIG,
              key = "T(cn.iocoder.yudao.module.cms.framework.car.util.CarCacheHelper).generateKey('code-version', #apiCode, #version)", 
              unless = "#result == null")
    public ApiSqlConfigDO getApiSqlConfigByCodeAndVersion(String apiCode, String version) {
        return apiSqlConfigMapper.selectByApiCodeAndVersion(apiCode, version);
    }

    @Override
    @Cacheable(cacheNames = CarCacheConstants.API_SQL_CONFIG_DEFAULT,
              key = "T(cn.iocoder.yudao.module.cms.framework.car.util.CarCacheHelper).generateKey(#apiCode)", 
              unless = "#result == null")
    public ApiSqlConfigDO getDefaultApiSqlConfig(String apiCode) {
        return apiSqlConfigMapper.selectByApiCodeAndIsDefault(apiCode, true);
    }

    @Override
    @Cacheable(cacheNames = CarCacheConstants.API_SQL_CONFIG_VERSIONS,
              key = "T(cn.iocoder.yudao.module.cms.framework.car.util.CarCacheHelper).generateKey(#apiCode)")
    public List<ApiSqlConfigDO> getApiSqlConfigListByCode(String apiCode) {
        return apiSqlConfigMapper.selectListByApiCode(apiCode);
    }

    @Override
    @Cacheable(cacheNames = CarCacheConstants.API_SQL_CONFIG_CODES)
    public List<String> getAllApiCodes() {
        return apiSqlConfigMapper.selectDistinctApiCodes();
    }

    @Override
    @Transactional
    @Caching(evict = {
        @CacheEvict(cacheNames = CarCacheConstants.API_SQL_CONFIG, key = "#id"),
        @CacheEvict(cacheNames = CarCacheConstants.API_SQL_CONFIG_DEFAULT, allEntries = true)
    })
    public void setAsDefaultVersion(Long id) {
        ApiSqlConfigDO config = validateApiSqlConfigExists(id);

        // 先将该API代码的所有配置设置为非默认（优化：只更新当前为默认的配置）
        ApiSqlConfigDO currentDefault = apiSqlConfigMapper.selectByApiCodeAndIsDefault(config.getApiCode(), true);
        if (currentDefault != null && !currentDefault.getId().equals(id)) {
            currentDefault.setIsDefault(false);
            apiSqlConfigMapper.updateById(currentDefault);
        }
        
        // 设置当前配置为默认
        config.setIsDefault(true);
        apiSqlConfigMapper.updateById(config);
        
        log.info("设置API配置为默认版本: apiCode={}, version={}, id={}", 
            config.getApiCode(), config.getVersion(), id);
    }

    @Override
    public boolean validateSqlFormat(String sqlContent) {
        return sqlExecutionEngine.validateSqlFormat(sqlContent);
    }

    @Override
    public List<Map<String, Object>> testSqlExecution(String sqlContent, Map<String, Object> params) {
        return sqlExecutionEngine.executeQuery(sqlContent, params);
    }

    @Override
    public List<Map<String, Object>> explainSql(String sqlContent) {
        return sqlExecutionEngine.explainQuery(sqlContent);
    }

    @Override
    public List<Map<String, Object>> explainSql(String sqlContent, Map<String, Object> params) {
        return sqlExecutionEngine.explainQuery(sqlContent, params);
    }

    // ===== 私有验证方法 =====

    private void validateCreateRequest(ApiSqlConfigCreateReqVO createReqVO) {
        if (createReqVO == null) {
            throw new IllegalArgumentException("创建请求不能为空");
        }
        if (createReqVO.getApiCode() == null || createReqVO.getApiCode().trim().isEmpty()) {
            throw new IllegalArgumentException("API代码不能为空");
        }
        if (createReqVO.getVersion() == null || createReqVO.getVersion().trim().isEmpty()) {
            throw new IllegalArgumentException("版本号不能为空");
        }
        if (createReqVO.getSqlContent() == null || createReqVO.getSqlContent().trim().isEmpty()) {
            throw new IllegalArgumentException("SQL内容不能为空");
        }
    }

    private void validateUpdateRequest(ApiSqlConfigUpdateReqVO updateReqVO) {
        if (updateReqVO == null) {
            throw new IllegalArgumentException("更新请求不能为空");
        }
        if (updateReqVO.getId() == null) {
            throw new IllegalArgumentException("配置ID不能为空");
        }
        if (updateReqVO.getSqlContent() == null || updateReqVO.getSqlContent().trim().isEmpty()) {
            throw new IllegalArgumentException("SQL内容不能为空");
        }
    }

    private void validateApiCodeVersionNotExists(String apiCode, String version) {
        ApiSqlConfigDO existing = apiSqlConfigMapper.selectByApiCodeAndVersion(apiCode, version);
        if (existing != null) {
            throw new IllegalArgumentException("API代码 " + apiCode + " 的版本 " + version + " 已存在");
        }
    }

    private void validateSqlContent(String sqlContent) {
        if (!sqlExecutionEngine.validateSqlFormat(sqlContent)) {
            throw new IllegalArgumentException("SQL格式验证失败");
        }
    }

}