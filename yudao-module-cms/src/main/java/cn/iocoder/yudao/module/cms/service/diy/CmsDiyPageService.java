package cn.iocoder.yudao.module.cms.service.diy;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.diy.vo.page.CmsDiyPageCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.diy.vo.page.CmsDiyPagePageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.diy.vo.page.CmsDiyPagePublishReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.diy.vo.page.CmsDiyPageUpdateReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * CMS DIY页面 Service 接口 - 增强版
 * 支持乐观锁、版本管理、发布流程
 *
 * <AUTHOR>
 */
public interface CmsDiyPageService {

    /**
     * 创建DIY页面
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDiyPage(@Valid CmsDiyPageCreateReqVO createReqVO);

    /**
     * 更新DIY页面（带乐观锁）
     * 
     * @param updateReqVO 更新信息（包含version字段）
     * @throws cn.iocoder.yudao.framework.common.exception.ServiceException 版本冲突时抛出
     */
    void updateDiyPage(@Valid CmsDiyPageUpdateReqVO updateReqVO);

    /**
     * 更新DIY页面内容（带乐观锁）
     * 仅更新content字段，用于页面编辑保存
     *
     * @param id 页面ID
     * @param content 页面内容
     * @param version 当前版本号
     * @throws cn.iocoder.yudao.framework.common.exception.ServiceException 版本冲突时抛出
     */
    void updateDiyPageContent(Long id, String content, Integer version);

    /**
     * 删除DIY页面（带乐观锁）
     *
     * @param id 编号
     * @param version 当前版本号
     * @throws cn.iocoder.yudao.framework.common.exception.ServiceException 版本冲突时抛出
     */
    void deleteDiyPage(Long id, Integer version);

    /**
     * 发布DIY页面
     * 1. 更新页面状态为已发布
     * 2. 自动创建版本记录
     * 3. 清除相关缓存
     *
     * @param publishReqVO 发布信息
     */
    void publishDiyPage(@Valid CmsDiyPagePublishReqVO publishReqVO);

    /**
     * 下线DIY页面
     * 将页面状态设置为草稿
     *
     * @param id 页面ID
     * @param version 当前版本号
     */
    void offlineDiyPage(Long id, Integer version);

    /**
     * 获得DIY页面
     *
     * @param id 编号
     * @return DIY页面
     */
    CmsDiyPageDO getDiyPage(Long id);

    /**
     * 根据UUID获得DIY页面
     *
     * @param uuid UUID
     * @return DIY页面
     */
    CmsDiyPageDO getDiyPageByUuid(String uuid);

    /**
     * 根据路径获得DIY页面
     *
     * @param path 页面路径
     * @return DIY页面
     */
    CmsDiyPageDO getDiyPageByPath(String path);

    /**
     * 获得DIY页面分页
     *
     * @param pageReqVO 分页查询
     * @return DIY页面分页
     */
    PageResult<CmsDiyPageDO> getDiyPagePage(CmsDiyPagePageReqVO pageReqVO);

    /**
     * 根据菜单ID获得DIY页面列表
     *
     * @param menuId 菜单ID
     * @return DIY页面列表
     */
    List<CmsDiyPageDO> getDiyPageListByMenuId(Long menuId);

    /**
     * 根据父页面ID获得子页面列表
     *
     * @param parentId 父页面ID
     * @return 子页面列表
     */
    List<CmsDiyPageDO> getDiyPageListByParentId(Long parentId);

    /**
     * 构建页面完整路径
     * 菜单路径 + 父页面路径 + 页面路径
     *
     * @param menuId 菜单ID
     * @param parentId 父页面ID
     * @param pagePath 页面路径
     * @return 完整路径
     */
    String buildPageFullPath(Long menuId, Long parentId, String pagePath);

    /**
     * 处理版本冲突
     * 获取最新版本信息供用户选择
     *
     * @param id 页面ID
     * @return 最新的页面信息
     */
    CmsDiyPageDO handleVersionConflict(Long id);

    /**
     * 清除页面缓存
     *
     * @param id 页面ID
     */
    void clearPageCache(Long id);

    /**
     * 预热页面缓存
     * 加载热门页面到缓存
     */
    void warmupPageCache();

    /**
     * 校验页面是否存在
     *
     * @param id 页面编号
     * @return 页面
     */
    CmsDiyPageDO validatePageExists(Long id);

    /**
     * 校验路径唯一性
     *
     * @param id 页面编号（更新时传入，新增时为null）
     * @param path 页面路径
     */
    void validatePathUnique(Long id, String path);

    /**
     * 校验UUID唯一性
     *
     * @param id 页面编号（更新时传入，新增时为null）
     * @param uuid UUID
     */
    void validateUuidUnique(Long id, String uuid);

    /**
     * 校验是否可以删除页面
     * 检查是否存在子页面等
     *
     * @param id 页面ID
     */
    void validateCanDelete(Long id);

    /**
     * App端：获得已发布的页面
     *
     * @param id 页面ID
     * @return 页面信息（仅已发布状态）
     */
    CmsDiyPageDO getPublishedPage(Long id);

    /**
     * App端：根据UUID获得已发布的页面
     *
     * @param uuid UUID
     * @return 页面信息（仅已发布状态）
     */
    CmsDiyPageDO getPublishedPageByUuid(String uuid);

    /**
     * App端：根据路径获得已发布的页面
     *
     * @param path 页面路径
     * @return 页面信息（仅已发布状态）
     */
    CmsDiyPageDO getPublishedPageByPath(String path);

    /**
     * App端：搜索已发布的页面
     *
     * @param keyword 关键词
     * @return 页面列表
     */
    List<CmsDiyPageDO> searchPublishedPages(String keyword);

    /**
     * 记录页面访问
     * 异步记录，不影响主流程
     *
     * @param pageId 页面ID
     * @param visitorUuid 访客UUID
     */
    void recordPageVisit(Long pageId, String visitorUuid);
}