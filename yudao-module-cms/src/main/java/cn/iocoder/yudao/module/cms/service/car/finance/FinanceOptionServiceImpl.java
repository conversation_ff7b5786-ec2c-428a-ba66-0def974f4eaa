package cn.iocoder.yudao.module.cms.service.car.finance;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.FinanceOptionCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.FinanceOptionPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.FinanceOptionUpdateReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.finance.FinanceOptionDO;
import cn.iocoder.yudao.module.cms.dal.mysql.car.finance.FinanceOptionMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.cms.enums.ErrorCodeConstants.*;

/**
 * 融资选项 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FinanceOptionServiceImpl implements FinanceOptionService {

    @Resource
    private FinanceOptionMapper financeOptionMapper;

    @Override
    public Long createFinanceOption(FinanceOptionCreateReqVO createReqVO) {
        // 校验代码唯一性
        validateOptionCodeUnique(null, createReqVO.getOptionCode());

        // 插入
        FinanceOptionDO financeOption = BeanUtils.toBean(createReqVO, FinanceOptionDO.class);
        financeOptionMapper.insert(financeOption);
        // 返回
        return financeOption.getId();
    }

    @Override
    public void updateFinanceOption(FinanceOptionUpdateReqVO updateReqVO) {
        // 校验存在
        validateFinanceOptionExists(updateReqVO.getId());
        
        // 校验代码唯一性
        validateOptionCodeUnique(updateReqVO.getId(), updateReqVO.getOptionCode());

        // 更新
        FinanceOptionDO updateObj = BeanUtils.toBean(updateReqVO, FinanceOptionDO.class);
        financeOptionMapper.updateById(updateObj);
    }

    @Override
    public void deleteFinanceOption(Long id) {
        // 校验存在
        validateFinanceOptionExists(id);
        // 删除
        financeOptionMapper.deleteById(id);
    }

    @Override
    public FinanceOptionDO getFinanceOption(Long id) {
        return financeOptionMapper.selectById(id);
    }

    @Override
    public PageResult<FinanceOptionDO> getFinanceOptionPage(FinanceOptionPageReqVO pageReqVO) {
        return financeOptionMapper.selectPage(pageReqVO.getName(), pageReqVO.getStatus(),
                pageReqVO.getPageNo(), pageReqVO.getPageSize());
    }

    @Override
    public List<FinanceOptionDO> getFinanceOptionList(Integer status) {
        return financeOptionMapper.selectListByStatus(status);
    }

    @Override
    public FinanceOptionDO getFinanceOptionByCode(String optionCode) {
        return financeOptionMapper.selectByCode(optionCode);
    }

    private void validateFinanceOptionExists(Long id) {
        if (financeOptionMapper.selectById(id) == null) {
            throw exception(FINANCE_OPTION_NOT_EXISTS);
        }
    }

    private void validateOptionCodeUnique(Long id, String optionCode) {
        FinanceOptionDO financeOption = financeOptionMapper.selectByCode(optionCode);
        if (financeOption == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的融资选项
        if (id == null) {
            throw exception(FINANCE_OPTION_CODE_DUPLICATE);
        }
        if (!financeOption.getId().equals(id)) {
            throw exception(FINANCE_OPTION_CODE_DUPLICATE);
        }
    }

}