package cn.iocoder.yudao.module.cms.framework.cache.core;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.caffeine.CaffeineCache;

import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;

/**
 * 多级缓存实现
 * 
 * 实现缓存穿透策略：
 * 1. 先查 L1 缓存（Caffeine）
 * 2. L1 未命中则查 L2 缓存（Redis）
 * 3. L2 命中则异步回填 L1 缓存
 * 4. L2 未命中则查数据库，并同时写入 L1 和 L2
 *
 * <AUTHOR>
 */
@Slf4j
public class MultiLevelCache implements Cache {

    private final String name;
    private final Cache l1Cache; // Caffeine 缓存
    private final Cache l2Cache; // Redis 缓存

    public MultiLevelCache(String name, Cache l1Cache, Cache l2Cache) {
        this.name = name;
        this.l1Cache = l1Cache;
        this.l2Cache = l2Cache;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public Object getNativeCache() {
        return this;
    }

    @Override
    public ValueWrapper get(Object key) {
        // 1. 先查 L1 缓存
        ValueWrapper l1Value = l1Cache.get(key);
        if (l1Value != null) {
            log.debug("Cache L1 hit for key: {} in cache: {}", key, name);
            return l1Value;
        }

        // 2. L1 未命中，查 L2 缓存
        ValueWrapper l2Value = l2Cache.get(key);
        if (l2Value != null) {
            log.debug("Cache L2 hit for key: {} in cache: {}", key, name);
            // 异步回填 L1 缓存
            CompletableFuture.runAsync(() -> {
                try {
                    l1Cache.put(key, l2Value.get());
                    log.debug("Async backfill L1 cache for key: {} in cache: {}", key, name);
                } catch (Exception e) {
                    log.warn("Failed to backfill L1 cache for key: {} in cache: {}", key, name, e);
                }
            });
            return l2Value;
        }

        log.debug("Cache miss for key: {} in cache: {}", key, name);
        return null;
    }

    @Override
    public <T> T get(Object key, Class<T> type) {
        ValueWrapper wrapper = get(key);
        return wrapper != null ? (T) wrapper.get() : null;
    }

    @Override
    public <T> T get(Object key, Callable<T> valueLoader) {
        // 1. 先尝试从缓存获取
        ValueWrapper existingValue = get(key);
        if (existingValue != null) {
            return (T) existingValue.get();
        }

        // 2. 缓存未命中，调用 valueLoader 加载数据
        try {
            T value = valueLoader.call();
            if (value != null) {
                put(key, value);
            }
            return value;
        } catch (Exception e) {
            throw new ValueRetrievalException(key, valueLoader, e);
        }
    }

    @Override
    public void put(Object key, Object value) {
        if (value == null) {
            return;
        }

        try {
            // 同时写入 L1 和 L2 缓存
            l1Cache.put(key, value);
            l2Cache.put(key, value);
            log.debug("Put value to both L1 and L2 cache for key: {} in cache: {}", key, name);
        } catch (Exception e) {
            log.error("Failed to put value to cache for key: {} in cache: {}", key, name, e);
        }
    }

    @Override
    public ValueWrapper putIfAbsent(Object key, Object value) {
        // 先检查是否已存在
        ValueWrapper existingValue = get(key);
        if (existingValue != null) {
            return existingValue;
        }

        // 不存在则写入
        put(key, value);
        return null;
    }

    @Override
    public void evict(Object key) {
        try {
            // 同时从 L1 和 L2 缓存中移除
            l1Cache.evict(key);
            l2Cache.evict(key);
            log.debug("Evicted key: {} from both L1 and L2 cache in cache: {}", key, name);
        } catch (Exception e) {
            log.error("Failed to evict key: {} from cache: {}", key, name, e);
        }
    }

    @Override
    public boolean evictIfPresent(Object key) {
        boolean l1Evicted = l1Cache.evictIfPresent(key);
        boolean l2Evicted = l2Cache.evictIfPresent(key);
        
        boolean evicted = l1Evicted || l2Evicted;
        if (evicted) {
            log.debug("Evicted key: {} from cache: {}, L1: {}, L2: {}", key, name, l1Evicted, l2Evicted);
        }
        return evicted;
    }

    @Override
    public void clear() {
        try {
            l1Cache.clear();
            l2Cache.clear();
            log.debug("Cleared both L1 and L2 cache: {}", name);
        } catch (Exception e) {
            log.error("Failed to clear cache: {}", name, e);
        }
    }

    @Override
    public boolean invalidate() {
        boolean l1Invalidated = l1Cache.invalidate();
        boolean l2Invalidated = l2Cache.invalidate();
        
        boolean invalidated = l1Invalidated && l2Invalidated;
        log.debug("Invalidated cache: {}, L1: {}, L2: {}", name, l1Invalidated, l2Invalidated);
        return invalidated;
    }

    /**
     * 获取缓存统计信息
     */
    public CacheStats getStats() {
        CacheStats stats = new CacheStats();
        stats.setCacheName(name);

        // 获取 L1 缓存统计（Caffeine）
        if (l1Cache instanceof CaffeineCache) {
            CaffeineCache caffeineCache = (CaffeineCache) l1Cache;
            com.github.benmanes.caffeine.cache.stats.CacheStats caffeineStats = 
                    caffeineCache.getNativeCache().stats();
            
            stats.setL1HitCount(caffeineStats.hitCount());
            stats.setL1MissCount(caffeineStats.missCount());
            stats.setL1HitRate(caffeineStats.hitRate());
            stats.setL1EvictionCount(caffeineStats.evictionCount());
        }

        // L2 缓存统计信息较难获取，这里暂时设置为默认值
        stats.setL2HitCount(0L);
        stats.setL2MissCount(0L);
        stats.setL2HitRate(0.0);

        return stats;
    }

    /**
     * 获取 L1 缓存
     */
    public Cache getL1Cache() {
        return l1Cache;
    }

    /**
     * 获取 L2 缓存
     */
    public Cache getL2Cache() {
        return l2Cache;
    }
}