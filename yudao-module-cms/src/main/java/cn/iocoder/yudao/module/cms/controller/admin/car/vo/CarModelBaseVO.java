package cn.iocoder.yudao.module.cms.controller.admin.car.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 车型管理 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class CarModelBaseVO {

    @Schema(description = "车型编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "TIGGO8_PRO_1_5T")
    @NotNull(message = "车型编码不能为空")
    @Length(max = 50, message = "车型编码长度不能超过50个字符")
    private String code;

    @Schema(description = "车型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "瑞虎8 PRO 1.5T CVT精英版")
    @NotNull(message = "车型名称不能为空")
    @Length(max = 100, message = "车型名称长度不能超过100个字符")
    private String name;

    @Schema(description = "英文名称", example = "TIGGO 8 PRO 1.5T CVT Elite")
    @Length(max = 100, message = "英文名称长度不能超过100个字符")
    private String englishName;

    @Schema(description = "车系ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "车系ID不能为空")
    private Long seriesId;

    @Schema(description = "指导价", requiredMode = Schema.RequiredMode.REQUIRED, example = "139800.00")
    @NotNull(message = "指导价不能为空")
    @DecimalMin(value = "0", message = "指导价必须大于等于0")
    private BigDecimal guidePrice;

    @Schema(description = "销售价格", example = "135800.00")
    @DecimalMin(value = "0", message = "销售价格必须大于等于0")
    private BigDecimal salePrice;

    @Schema(description = "车型描述", example = "搭载1.5T涡轮增压发动机，动力强劲")
    @Length(max = 500, message = "车型描述长度不能超过500个字符")
    private String description;

    @Schema(description = "车型图片URL", example = "https://example.com/car-image.jpg")
    @Length(max = 200, message = "车型图片URL长度不能超过200个字符")
    private String imageUrl;

    @Schema(description = "颜色选项", example = "红色,蓝色,白色")
    private String colors;

    @Schema(description = "是否热销", example = "true")
    private Boolean isHot;

    @Schema(description = "是否新车", example = "true")
    private Boolean isNew;

    @Schema(description = "显示顺序", example = "1")
    private Integer sort;

    @Schema(description = "状态：0-禁用，1-启用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

}