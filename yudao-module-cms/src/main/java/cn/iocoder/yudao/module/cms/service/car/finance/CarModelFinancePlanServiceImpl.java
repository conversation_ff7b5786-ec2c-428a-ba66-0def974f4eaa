package cn.iocoder.yudao.module.cms.service.car.finance;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.CarModelFinancePlanCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.CarModelFinancePlanPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.CarModelFinancePlanUpdateReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.finance.CarModelFinancePlanDO;
import cn.iocoder.yudao.module.cms.dal.mysql.car.finance.CarModelFinancePlanMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.cms.enums.ErrorCodeConstants.*;

/**
 * 车型融资方案 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CarModelFinancePlanServiceImpl implements CarModelFinancePlanService {

    @Resource
    private CarModelFinancePlanMapper carModelFinancePlanMapper;

    @Resource
    private FinancePlanCalculator financePlanCalculator;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createCarModelFinancePlan(CarModelFinancePlanCreateReqVO createReqVO) {
        // 如果设置为默认方案，需要将其他方案设置为非默认
        if (Boolean.TRUE.equals(createReqVO.getIsDefault())) {
            resetDefaultPlanByModelId(createReqVO.getModelId());
        }

        // 插入
        CarModelFinancePlanDO financePlan = BeanUtils.toBean(createReqVO, CarModelFinancePlanDO.class);
        carModelFinancePlanMapper.insert(financePlan);
        
        return financePlan.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCarModelFinancePlan(CarModelFinancePlanUpdateReqVO updateReqVO) {
        // 校验存在
        validateFinancePlanExists(updateReqVO.getId());
        
        // 获取原记录
        CarModelFinancePlanDO oldPlan = carModelFinancePlanMapper.selectById(updateReqVO.getId());
        
        // 如果设置为默认方案，需要将其他方案设置为非默认
        if (Boolean.TRUE.equals(updateReqVO.getIsDefault()) && !Boolean.TRUE.equals(oldPlan.getIsDefault())) {
            resetDefaultPlanByModelId(oldPlan.getModelId());
        }
        
        // 更新
        CarModelFinancePlanDO updateObj = BeanUtils.toBean(updateReqVO, CarModelFinancePlanDO.class);
        carModelFinancePlanMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCarModelFinancePlan(Long id) {
        // 校验存在
        validateFinancePlanExists(id);
        
        // 获取记录检查是否为默认方案
        CarModelFinancePlanDO plan = carModelFinancePlanMapper.selectById(id);
        if (Boolean.TRUE.equals(plan.getIsDefault())) {
            throw exception(CAR_MODEL_FINANCE_PLAN_DEFAULT_CANNOT_DELETE);
        }
        
        // 删除
        carModelFinancePlanMapper.deleteById(id);
    }

    @Override
    public CarModelFinancePlanDO getCarModelFinancePlan(Long id) {
        return carModelFinancePlanMapper.selectById(id);
    }

    @Override
    public PageResult<CarModelFinancePlanDO> getCarModelFinancePlanPage(CarModelFinancePlanPageReqVO pageReqVO) {
        return carModelFinancePlanMapper.selectPage(
            pageReqVO.getModelId(),
            pageReqVO.getFinanceOptionId(), 
            pageReqVO.getStatus(),
            pageReqVO.getPageNo(),
            pageReqVO.getPageSize()
        );
    }

    @Override
    public List<CarModelFinancePlanDO> getCarModelFinancePlanListByModelId(Long modelId) {
        return carModelFinancePlanMapper.selectListByModelId(modelId);
    }

    @Override
    public CarModelFinancePlanDO getDefaultFinancePlan(Long modelId) {
        return carModelFinancePlanMapper.selectDefaultPlan(modelId);
    }

    @Override
    public List<CarModelFinancePlanDO> getCarModelFinancePlansWithDetails(Long modelId) {
        return carModelFinancePlanMapper.selectPlansWithDetails(modelId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setAsDefaultPlan(Long id) {
        // 校验存在
        CarModelFinancePlanDO plan = validateFinancePlanExists(id);
        
        // 如果已经是默认方案，直接返回
        if (Boolean.TRUE.equals(plan.getIsDefault())) {
            return;
        }
        
        // 重置其他默认方案
        resetDefaultPlanByModelId(plan.getModelId());
        
        // 设置为默认
        CarModelFinancePlanDO updateObj = new CarModelFinancePlanDO();
        updateObj.setId(id);
        updateObj.setIsDefault(true);
        carModelFinancePlanMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> batchCreateCarModelFinancePlans(List<CarModelFinancePlanCreateReqVO> createReqVOList) {
        if (createReqVOList == null || createReqVOList.isEmpty()) {
            return List.of();
        }
        
        // 批量插入
        List<CarModelFinancePlanDO> financePlans = createReqVOList.stream()
            .map(vo -> BeanUtils.toBean(vo, CarModelFinancePlanDO.class))
            .collect(Collectors.toList());
        
        financePlans.forEach(plan -> carModelFinancePlanMapper.insert(plan));
        
        return financePlans.stream()
            .map(CarModelFinancePlanDO::getId)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCarModelFinancePlansByModelId(Long modelId) {
        carModelFinancePlanMapper.delete(
            new LambdaQueryWrapper<CarModelFinancePlanDO>()
                .eq(CarModelFinancePlanDO::getModelId, modelId)
        );
    }

    @Override
    public FinancePlanDetails calculateFinancePlan(BigDecimal carPrice, BigDecimal downPaymentRatio,
                                                  BigDecimal interestRate, Integer termMonths, BigDecimal gfv) {
        // 计算首付金额
        BigDecimal downPaymentAmount = carPrice.multiply(downPaymentRatio)
            .setScale(2, RoundingMode.HALF_UP);
        
        // 计算融资金额
        BigDecimal financeAmount = carPrice.subtract(downPaymentAmount);
        
        // 如果有GFV，从融资金额中减去
        BigDecimal principalAmount = financeAmount;
        if (gfv != null && gfv.compareTo(BigDecimal.ZERO) > 0) {
            principalAmount = financeAmount.subtract(gfv);
        }
        
        // 计算月利率
        BigDecimal monthlyRate = interestRate.divide(BigDecimal.valueOf(12), 8, RoundingMode.HALF_UP)
            .divide(BigDecimal.valueOf(100), 8, RoundingMode.HALF_UP);
        
        // 计算月供（使用等额本息公式）
        BigDecimal monthlyPayment;
        if (monthlyRate.compareTo(BigDecimal.ZERO) == 0) {
            // 无利息情况
            monthlyPayment = principalAmount.divide(BigDecimal.valueOf(termMonths), 2, RoundingMode.HALF_UP);
        } else {
            // 有利息情况：PMT = P * [r(1+r)^n] / [(1+r)^n - 1]
            BigDecimal onePlusRate = BigDecimal.ONE.add(monthlyRate);
            BigDecimal powerResult = onePlusRate.pow(termMonths);
            BigDecimal numerator = principalAmount.multiply(monthlyRate).multiply(powerResult);
            BigDecimal denominator = powerResult.subtract(BigDecimal.ONE);
            monthlyPayment = numerator.divide(denominator, 2, RoundingMode.HALF_UP);
        }
        
        // 如果有GFV，需要计算GFV的利息并分摊到月供中
        if (gfv != null && gfv.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal gfvInterest = gfv.multiply(monthlyRate).multiply(BigDecimal.valueOf(termMonths));
            BigDecimal gfvMonthlyInterest = gfvInterest.divide(BigDecimal.valueOf(termMonths), 2, RoundingMode.HALF_UP);
            monthlyPayment = monthlyPayment.add(gfvMonthlyInterest);
        }
        
        // 计算周供
        BigDecimal weeklyPayment = monthlyPayment.multiply(BigDecimal.valueOf(12))
            .divide(BigDecimal.valueOf(52), 2, RoundingMode.HALF_UP);
        
        // 计算总金额
        BigDecimal totalAmount = downPaymentAmount.add(monthlyPayment.multiply(BigDecimal.valueOf(termMonths)));
        if (gfv != null && gfv.compareTo(BigDecimal.ZERO) > 0) {
            totalAmount = totalAmount.add(gfv);
        }
        
        return new FinancePlanDetails(downPaymentAmount, financeAmount, monthlyPayment, weeklyPayment, totalAmount);
    }

    /**
     * 校验融资方案存在
     */
    private CarModelFinancePlanDO validateFinancePlanExists(Long id) {
        CarModelFinancePlanDO plan = carModelFinancePlanMapper.selectById(id);
        if (plan == null) {
            throw exception(CAR_MODEL_FINANCE_PLAN_NOT_EXISTS);
        }
        return plan;
    }

    /**
     * 重置车型的默认方案
     */
    private void resetDefaultPlanByModelId(Long modelId) {
        carModelFinancePlanMapper.update(null,
            new LambdaUpdateWrapper<CarModelFinancePlanDO>()
                .eq(CarModelFinancePlanDO::getModelId, modelId)
                .eq(CarModelFinancePlanDO::getIsDefault, true)
                .set(CarModelFinancePlanDO::getIsDefault, false)
        );
    }
}