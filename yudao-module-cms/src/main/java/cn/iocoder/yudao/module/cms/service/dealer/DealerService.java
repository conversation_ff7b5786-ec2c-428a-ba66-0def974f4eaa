package cn.iocoder.yudao.module.cms.service.dealer;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.dealer.vo.DealerCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.dealer.vo.DealerPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.dealer.vo.DealerUpdateReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.dealer.DealerDO;

import cn.iocoder.yudao.module.cms.dal.dataobject.dealer.DealerWithDistanceDO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * CMS经销商 Service 接口
 *
 * <AUTHOR>
 */
public interface DealerService {

    /**
     * 创建经销商
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDealer(@Valid DealerCreateReqVO createReqVO);

    /**
     * 更新经销商
     *
     * @param updateReqVO 更新信息
     */
    void updateDealer(@Valid DealerUpdateReqVO updateReqVO);

    /**
     * 删除经销商
     *
     * @param id 编号
     */
    void deleteDealer(Long id);

    /**
     * 获得经销商
     *
     * @param id 编号
     * @return 经销商
     */
    DealerDO getDealer(Long id);

    /**
     * 获得经销商分页
     *
     * @param pageReqVO 分页查询
     * @return 经销商分页
     */
    PageResult<DealerDO> getDealerPage(DealerPageReqVO pageReqVO);

    /**
     * 获得经销商列表
     *
     * @param region 地区
     * @param status 状态
     * @return 经销商列表
     */
    List<DealerDO> getDealerList(String region, Integer status);

    /**
     * 获得附近经销商列表
     *
     * @param lat 纬度
     * @param lng 经度
     * @param radius 半径（公里）
     * @return 附近经销商列表
     */
    List<DealerWithDistanceDO> getNearbyDealers(Double lat, Double lng, Integer radius);

}