package cn.iocoder.yudao.module.cms.dal.mysql.car;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarOptionTypeDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 配置选项类型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CarOptionTypeMapper extends BaseMapperX<CarOptionTypeDO> {

    default PageResult<CarOptionTypeDO> selectPage(String name, Integer status, 
                                                    Integer pageNo, Integer pageSize) {
        return selectPage(new PageParam().setPageNo(pageNo).setPageSize(pageSize),new LambdaQueryWrapperX<CarOptionTypeDO>()
                .likeIfPresent(CarOptionTypeDO::getName, name)
                .eqIfPresent(CarOptionTypeDO::getStatus, status)
                .orderByAsc(CarOptionTypeDO::getSortOrder)
                .orderByDesc(CarOptionTypeDO::getId));
    }

    default CarOptionTypeDO selectByTypeCode(String typeCode) {
        return selectOne(CarOptionTypeDO::getTypeCode, typeCode);
    }

    default List<CarOptionTypeDO> selectListByStatus(Integer status) {
        return selectList(new LambdaQueryWrapperX<CarOptionTypeDO>()
                .eqIfPresent(CarOptionTypeDO::getStatus, status)
                .orderByAsc(CarOptionTypeDO::getSortOrder));
    }

}