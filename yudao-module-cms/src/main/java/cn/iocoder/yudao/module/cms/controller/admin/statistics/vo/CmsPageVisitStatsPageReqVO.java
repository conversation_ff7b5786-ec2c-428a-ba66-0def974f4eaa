package cn.iocoder.yudao.module.cms.controller.admin.statistics.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * CMS页面访问统计分页 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - CMS页面访问统计分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CmsPageVisitStatsPageReqVO extends PageParam {

    @Schema(description = "页面ID", example = "1024")
    private Long pageId;

    @Schema(description = "统计日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] statDate;
}