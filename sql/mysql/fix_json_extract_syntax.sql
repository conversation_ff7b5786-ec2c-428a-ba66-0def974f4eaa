-- 修复数据库中已存在的API SQL配置的JSON_EXTRACT语法问题
-- 作者：开发团队
-- 日期：2025-01-10
-- 问题：JSON_EXTRACT的路径参数被截断，从'$'变成了'

SET NAMES utf8mb4;

-- 开始事务
START TRANSACTION;

-- 修复car_configurator API的SQL配置
UPDATE cms_api_sql_configs 
SET sql_content = REPLACE(
    REPLACE(
        REPLACE(
            REPLACE(
                REPLACE(
                    sql_content,
                    'JSON_EXTRACT(m.features, '')',
                    'JSON_EXTRACT(m.features, ''$'')'
                ),
                'JSON_EXTRACT(cp.features, '')',
                'JSON_EXTRACT(cp.features, ''$'')'
            ),
            'JSON_EXTRACT(mo.config_data, '')',
            'JSON_EXTRACT(mo.config_data, ''$'')'
        ),
        'JSON_EXTRACT(m.features, '''')',
        'JSON_EXTRACT(m.features, ''$'')'
    ),
    'JSON_EXTRACT(cp.features, '''')',
    'JSON_EXTRACT(cp.features, ''$'')'
)
WHERE api_code = 'car_configurator' 
  AND (sql_content LIKE '%JSON_EXTRACT%features%''%' OR sql_content LIKE '%JSON_EXTRACT%config_data%''%');

-- 修复car_detail API的SQL配置
UPDATE cms_api_sql_configs 
SET sql_content = REPLACE(
    REPLACE(
        REPLACE(
            REPLACE(
                REPLACE(
                    sql_content,
                    'JSON_EXTRACT(m.features, '')',
                    'JSON_EXTRACT(m.features, ''$'')'
                ),
                'JSON_EXTRACT(m.specifications, '')',
                'JSON_EXTRACT(m.specifications, ''$'')'
            ),
            'JSON_EXTRACT(m.tech_features, '')',
            'JSON_EXTRACT(m.tech_features, ''$'')'
        ),
        'JSON_EXTRACT(m.safety_features, '')',
        'JSON_EXTRACT(m.safety_features, ''$'')'
    ),
    'JSON_EXTRACT(cmg.images, '')',
    'JSON_EXTRACT(cmg.images, ''$'')'
)
WHERE api_code = 'car_detail' 
  AND (sql_content LIKE '%JSON_EXTRACT%''%');

-- 修复所有包含截断JSON_EXTRACT路径的配置
-- 这个更通用的修复方案，处理所有可能的JSON_EXTRACT路径截断问题
UPDATE cms_api_sql_configs 
SET sql_content = REGEXP_REPLACE(
    sql_content,
    'JSON_EXTRACT\\(([^,]+),\\s*\'\'\\)',
    'JSON_EXTRACT($1, ''$'')'
)
WHERE sql_content REGEXP 'JSON_EXTRACT\\([^,]+,\\s*\'\'\\)';

-- 另一种修复方式：如果上面的REGEXP_REPLACE不可用（MySQL版本较低）
-- 可以使用多次REPLACE来处理常见的情况
UPDATE cms_api_sql_configs 
SET sql_content = REPLACE(
    REPLACE(
        REPLACE(
            REPLACE(
                REPLACE(
                    REPLACE(
                        REPLACE(
                            REPLACE(
                                sql_content,
                                ', \'\')', 
                                ', ''$'')'
                            ),
                            ',\'\')', 
                            ', ''$'')'
                        ),
                        ', \'\' )', 
                        ', ''$'')'
                    ),
                    ',\'\' )', 
                    ', ''$'')'
                ),
                ', \')', 
                ', ''$'')'
            ),
            ',\')', 
            ', ''$'')'
        ),
        ', \' )', 
        ', ''$'')'
    ),
    ',\' )', 
    ', ''$'')'
)
WHERE sql_content LIKE '%JSON_EXTRACT%' 
  AND (sql_content LIKE '%,\'\')%' OR sql_content LIKE '%,\')%' OR sql_content LIKE '%, \'\')%' OR sql_content LIKE '%, \')%');

-- 验证修复结果
SELECT api_code, version, 
       CASE 
           WHEN sql_content LIKE '%JSON_EXTRACT%\'\')%' OR sql_content LIKE '%JSON_EXTRACT%\')%'
           THEN '仍需修复'
           ELSE '已修复'
       END as fix_status,
       LENGTH(sql_content) as content_length
FROM cms_api_sql_configs
WHERE sql_content LIKE '%JSON_EXTRACT%'
ORDER BY api_code, version;

-- 提交事务
COMMIT;

-- 执行后的验证查询（可选）
-- 查看具体的JSON_EXTRACT使用情况
-- SELECT api_code, version,
--        SUBSTRING(sql_content, 
--                  LOCATE('JSON_EXTRACT', sql_content) - 10, 
--                  100) as json_extract_snippet
-- FROM cms_api_sql_configs
-- WHERE sql_content LIKE '%JSON_EXTRACT%'
-- LIMIT 10;