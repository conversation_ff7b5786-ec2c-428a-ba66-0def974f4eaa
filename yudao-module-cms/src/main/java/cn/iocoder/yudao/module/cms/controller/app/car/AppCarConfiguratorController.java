package cn.iocoder.yudao.module.cms.controller.app.car;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.cms.service.car.api.DatabaseSqlApiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.PermitAll;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * App端车型配置器API控制器
 * 为官网提供可配置的API响应格式
 * 
 * <AUTHOR>
 */
@Tag(name = "App API - 车型配置器")
@RestController
@RequestMapping("/app/cms/app-api")
@Slf4j
public class AppCarConfiguratorController {

    @Resource
    private DatabaseSqlApiService databaseSqlApiService;

    @GetMapping("/v1/car-models/{code}/{apiCode}")
    @Operation(summary = "获取车型配置器数据 - v1版本", description = "返回完整的车型配置器数据，包括车型信息、配置包、选项和融资方案")
    @PermitAll
    public CommonResult<Map<String, Object>> getCarConfigurator(
            @Parameter(description = "车型代码", required = true, example = "tiggo-7")
            @PathVariable String code,
            @Parameter(description = "查询配置", required = true, example = "car_configurator")
            @PathVariable String apiCode,
             @Parameter(description = "API版本", example = "default")
             @RequestParam(required = false,defaultValue = "default" ) String version
    ) {

        log.info("获取车型配置器数据: {}", apiCode);

        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        Map<String, Object> response = databaseSqlApiService.executeApiSql(
                apiCode, version, params);

        return success(response);
    }
//
//    @GetMapping("/v1/car-models/{code}/configurator")
//    @Operation(summary = "获取车型配置器数据 - v1版本", description = "返回完整的车型配置器数据，包括车型信息、配置包、选项和融资方案")
//    @PermitAll
//    public CommonResult<Map<String, Object>> getCarConfiguratorV1(
//            @Parameter(description = "车型代码", required = true, example = "tiggo-7")
//            @PathVariable String code) {
//
//        log.info("获取车型配置器数据: {}", code);
//
//        Map<String, Object> params = new HashMap<>();
//        params.put("code", code);
//
//        Map<String, Object> response = databaseSqlApiService.executeApiSql(
//            "car_configurator", "v1", params);
//
//        return success(response);
//    }
//
//    @GetMapping("/v2/car-models/{code}/configurator")
//    @Operation(summary = "获取车型配置器数据 - v2版本", description = "返回简化的车型配置器数据，适用于移动端")
//    @PermitAll
//    public CommonResult<Map<String, Object>> getCarConfiguratorV2(
//            @Parameter(description = "车型代码", required = true, example = "tiggo-7")
//            @PathVariable String code) {
//
//        log.info("获取车型配置器数据 v2: {}", code);
//
//        Map<String, Object> params = new HashMap<>();
//        params.put("code", code);
//
//        Map<String, Object> response = databaseSqlApiService.executeApiSql(
//            "car_configurator", "v2", params);
//
//        return success(response);
//    }
//
//    @GetMapping("/mobile/car-models/{code}/configurator")
//    @Operation(summary = "获取车型配置器数据 - 移动端版本", description = "返回精简的车型配置器数据，专为移动端优化")
//    @PermitAll
//    public CommonResult<Map<String, Object>> getCarConfiguratorMobile(
//            @Parameter(description = "车型代码", required = true, example = "tiggo-7")
//            @PathVariable String code) {
//
//        log.info("获取车型配置器数据 - 移动端: {}", code);
//
//        Map<String, Object> params = new HashMap<>();
//        params.put("code", code);
//
//        Map<String, Object> response = databaseSqlApiService.executeApiSql(
//            "car_configurator", "mobile", params);
//
//        return success(response);
//    }
//
//    @GetMapping("/car-models/{code}/configurator")
//    @Operation(summary = "获取车型配置器数据 - 默认版本", description = "支持版本参数的通用接口")
//    @PermitAll
//    public CommonResult<Map<String, Object>> getCarConfiguratorDefault(
//            @Parameter(description = "车型代码", required = true, example = "tiggo-7")
//            @PathVariable String code,
//            @Parameter(description = "API版本，不传则使用默认版本", example = "v1")
//            @RequestParam(required = false) String version) {
//
//        log.info("获取车型配置器数据 - 默认: {}, version: {}", code, version);
//
//        Map<String, Object> params = new HashMap<>();
//        params.put("code", code);
//
//        Map<String, Object> response = databaseSqlApiService.executeApiSql(
//            "car_configurator", version, params);
//
//        return success(response);
//    }
//
//    @GetMapping("/car-models/{code}/detail")
//    @Operation(summary = "获取车型详情数据", description = "返回车型的详细信息，包括规格和默认融资方案")
//    @PermitAll
//    public CommonResult<Map<String, Object>> getCarDetail(
//            @Parameter(description = "车型代码", required = true, example = "tiggo-7")
//            @PathVariable String code,
//            @Parameter(description = "API版本", example = "default")
//            @RequestParam(required = false) String version) {
//
//        log.info("获取车型详情数据: {}, version: {}", code, version);
//
//        Map<String, Object> params = new HashMap<>();
//        params.put("code", code);
//
//        Map<String, Object> response = databaseSqlApiService.executeApiSql(
//            "car_detail", version, params);
//
//        return success(response);
//    }

    @GetMapping("/car-models")
    @Operation(summary = "获取车型列表", description = "返回车型列表数据，支持分页和筛选")
    @PermitAll
    public CommonResult<Map<String, Object>> getCarModelsList(
            @Parameter(description = "车系代码", example = "tiggo")
            @RequestParam(required = false) String series,
            @Parameter(description = "车型分类", example = "suv")
            @RequestParam(required = false) String category,
            @Parameter(description = "API版本", example = "default")
            @RequestParam(required = false) String version,
            @Parameter(description = "页码", example = "1")
            @RequestParam(defaultValue = "1") Integer pageNo,
            @Parameter(description = "页大小", example = "10")
            @RequestParam(defaultValue = "10") Integer pageSize) {
        
        log.info("获取车型列表: series={}, category={}, version={}", series, category, version);
        
        Map<String, Object> params = new HashMap<>();
        if (series != null) params.put("series", series);
        if (category != null) params.put("category", category);
        params.put("pageNo", pageNo);
        params.put("pageSize", pageSize);
        
        Map<String, Object> response = databaseSqlApiService.executeApiSql(
            "car_models_list", version, params);
        
        return success(response);
    }

    @GetMapping("/car-series")
    @Operation(summary = "获取车系列表", description = "返回所有车系的基础信息")
    @PermitAll
    public CommonResult<Map<String, Object>> getCarSeriesList(
            @Parameter(description = "API版本", example = "default")
            @RequestParam(required = false) String version) {
        
        log.info("获取车系列表: version={}", version);
        
        Map<String, Object> params = new HashMap<>();
        
        Map<String, Object> response = databaseSqlApiService.executeApiSql(
            "car_series_list", version, params);
        
        return success(response);
    }

    @GetMapping("/finance-options")
    @Operation(summary = "获取融资选项", description = "返回所有可用的融资选项")
    @PermitAll
    public CommonResult<Map<String, Object>> getFinanceOptions(
            @Parameter(description = "车型代码", example = "tiggo-7")
            @RequestParam(required = false) String modelCode,
            @Parameter(description = "API版本", example = "default")
            @RequestParam(required = false) String version) {
        
        log.info("获取融资选项: modelCode={}, version={}", modelCode, version);
        
        Map<String, Object> params = new HashMap<>();
        if (modelCode != null) params.put("modelCode", modelCode);
        
        Map<String, Object> response = databaseSqlApiService.executeApiSql(
            "finance_options", version, params);
        
        return success(response);
    }

    @PostMapping("/car-models/{code}/calculate-price")
    @Operation(summary = "计算车型价格", description = "根据选择的配置计算总价格")
    @PermitAll
    public CommonResult<Map<String, Object>> calculatePrice(
            @Parameter(description = "车型代码", required = true, example = "tiggo-7")
            @PathVariable String code,
            @RequestBody Map<String, Object> configuration,
            @Parameter(description = "API版本", example = "default")
            @RequestParam(required = false) String version) {
        
        log.info("计算车型价格: {}, configuration: {}", code, configuration);
        
        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        params.putAll(configuration);
        
        Map<String, Object> response = databaseSqlApiService.executeApiSql(
            "price_calculator", version, params);
        
        return success(response);
    }

    @GetMapping("/api/health")
    @Operation(summary = "API健康检查", description = "检查API服务状态")
    @PermitAll
    public CommonResult<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", System.currentTimeMillis());
        health.put("service", "car-configurator-api");
        health.putAll(databaseSqlApiService.getApiStats());
        
        return success(health);
    }
}