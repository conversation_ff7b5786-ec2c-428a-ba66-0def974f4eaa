package cn.iocoder.yudao.module.cms.controller.admin.car;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.*;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarModelOptionDO;
import cn.iocoder.yudao.module.cms.service.car.CarModelOptionService;
import cn.iocoder.yudao.module.cms.service.car.validator.DynamicConfigValidator;
import cn.iocoder.yudao.module.cms.service.car.validator.ConfigConsistencyChecker;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 车型配置选项")
@RestController
@RequestMapping("/cms/car/model-option")
@Validated
public class CarModelOptionController {

    @Resource
    private CarModelOptionService carModelOptionService;

    @Resource
    private DynamicConfigValidator dynamicConfigValidator;

    @Resource
    private ConfigConsistencyChecker configConsistencyChecker;

    @PostMapping("/create")
    @Operation(summary = "创建车型配置选项")
    @PreAuthorize("@ss.hasPermission('cms:car-model-option:create')")
    public CommonResult<Long> createCarModelOption(@Valid @RequestBody CarModelOptionCreateReqVO createReqVO) {
        return success(carModelOptionService.createCarModelOption(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新车型配置选项")
    @PreAuthorize("@ss.hasPermission('cms:car-model-option:update')")
    public CommonResult<Boolean> updateCarModelOption(@Valid @RequestBody CarModelOptionUpdateReqVO updateReqVO) {
        carModelOptionService.updateCarModelOption(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除车型配置选项")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('cms:car-model-option:delete')")
    public CommonResult<Boolean> deleteCarModelOption(@RequestParam("id") Long id) {
        carModelOptionService.deleteCarModelOption(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得车型配置选项")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('cms:car-model-option:query')")
    public CommonResult<CarModelOptionRespVO> getCarModelOption(@RequestParam("id") Long id) {
        CarModelOptionDO carModelOption = carModelOptionService.getCarModelOption(id);
        return success(BeanUtils.toBean(carModelOption, CarModelOptionRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得车型配置选项分页")
    @PreAuthorize("@ss.hasPermission('cms:car-model-option:query')")
    public CommonResult<PageResult<CarModelOptionRespVO>> getCarModelOptionPage(@Valid CarModelOptionPageReqVO pageVO) {
        PageResult<CarModelOptionDO> pageResult = carModelOptionService.getCarModelOptionPage(pageVO);
        return success(BeanUtils.toBean(pageResult, CarModelOptionRespVO.class));
    }

    @GetMapping("/list-by-model")
    @Operation(summary = "根据车型ID获得配置选项列表")
    @Parameter(name = "modelId", description = "车型ID", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('cms:car-model-option:query')")
    public CommonResult<List<CarModelOptionRespVO>> getCarModelOptionListByModelId(@RequestParam("modelId") Long modelId) {
        List<CarModelOptionDO> list = carModelOptionService.getCarModelOptionListByModelId(modelId);
        return success(BeanUtils.toBean(list, CarModelOptionRespVO.class));
    }

    @GetMapping("/list-by-model-and-type")
    @Operation(summary = "根据车型ID和选项类型ID获得配置选项列表")
    @PreAuthorize("@ss.hasPermission('cms:car-model-option:query')")
    public CommonResult<List<CarModelOptionRespVO>> getCarModelOptionListByModelIdAndTypeId(
            @RequestParam("modelId") Long modelId,
            @RequestParam("optionTypeId") Long optionTypeId) {
        List<CarModelOptionDO> list = carModelOptionService.getCarModelOptionListByModelIdAndTypeId(modelId, optionTypeId);
        return success(BeanUtils.toBean(list, CarModelOptionRespVO.class));
    }

    @GetMapping("/list-with-type")
    @Operation(summary = "获得车型的所有配置选项（关联选项类型信息）")
    @Parameter(name = "modelId", description = "车型ID", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('cms:car-model-option:query')")
    public CommonResult<List<CarModelOptionRespVO>> getCarModelOptionsWithType(@RequestParam("modelId") Long modelId) {
        List<CarModelOptionDO> list = carModelOptionService.getCarModelOptionsWithType(modelId);
        return success(BeanUtils.toBean(list, CarModelOptionRespVO.class));
    }

    @PostMapping("/batch-create")
    @Operation(summary = "批量创建车型配置选项")
    @PreAuthorize("@ss.hasPermission('cms:car-model-option:create')")
    public CommonResult<List<Long>> batchCreateCarModelOptions(@Valid @RequestBody List<CarModelOptionCreateReqVO> createReqVOList) {
        List<Long> ids = carModelOptionService.batchCreateCarModelOptions(createReqVOList);
        return success(ids);
    }

    @PutMapping("/batch-update")
    @Operation(summary = "批量更新车型配置选项")
    @PreAuthorize("@ss.hasPermission('cms:car-model-option:update')")
    public CommonResult<Boolean> batchUpdateCarModelOptions(@Valid @RequestBody List<CarModelOptionUpdateReqVO> updateReqVOList) {
        carModelOptionService.batchUpdateCarModelOptions(updateReqVOList);
        return success(true);
    }

    @DeleteMapping("/delete-by-model")
    @Operation(summary = "根据车型ID删除所有配置选项")
    @Parameter(name = "modelId", description = "车型ID", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('cms:car-model-option:delete')")
    public CommonResult<Boolean> deleteCarModelOptionsByModelId(@RequestParam("modelId") Long modelId) {
        carModelOptionService.deleteCarModelOptionsByModelId(modelId);
        return success(true);
    }

    @PostMapping("/validate-config")
    @Operation(summary = "验证配置数据")
    @PreAuthorize("@ss.hasPermission('cms:car-model-option:query')")
    public CommonResult<DynamicConfigValidator.ValidationResult> validateConfigData(
            @RequestParam("typeCode") String typeCode,
            @RequestBody Map<String, Object> configData) {
        DynamicConfigValidator.ValidationResult result = dynamicConfigValidator.validateConfigData(typeCode, configData);
        return success(result);
    }

    @PostMapping("/validate-field-completeness")
    @Operation(summary = "验证字段完整性")
    @PreAuthorize("@ss.hasPermission('cms:car-model-option:query')")
    public CommonResult<DynamicConfigValidator.ValidationResult> validateFieldCompleteness(
            @RequestParam("typeCode") String typeCode,
            @RequestBody Map<String, Object> configData) {
        DynamicConfigValidator.ValidationResult result = dynamicConfigValidator.validateFieldCompleteness(typeCode, configData);
        return success(result);
    }

    @PostMapping("/validate-data-type-consistency")
    @Operation(summary = "验证数据类型一致性")
    @PreAuthorize("@ss.hasPermission('cms:car-model-option:query')")
    public CommonResult<DynamicConfigValidator.ValidationResult> validateDataTypeConsistency(
            @RequestParam("typeCode") String typeCode,
            @RequestBody Map<String, Object> configData) {
        DynamicConfigValidator.ValidationResult result = dynamicConfigValidator.validateDataTypeConsistency(typeCode, configData);
        return success(result);
    }

    @GetMapping("/check-consistency")
    @Operation(summary = "检查车型配置的一致性")
    @Parameter(name = "modelId", description = "车型ID", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('cms:car-model-option:query')")
    public CommonResult<ConfigConsistencyChecker.ConsistencyCheckResult> checkModelConfigConsistency(@RequestParam("modelId") Long modelId) {
        ConfigConsistencyChecker.ConsistencyCheckResult result = configConsistencyChecker.checkModelConfigConsistency(modelId);
        return success(result);
    }

    @PostMapping("/batch-check-consistency")
    @Operation(summary = "批量检查多个车型的配置一致性")
    @PreAuthorize("@ss.hasPermission('cms:car-model-option:query')")
    public CommonResult<List<ConfigConsistencyChecker.ConsistencyCheckResult>> batchCheckModelConfigConsistency(@RequestBody List<Long> modelIds) {
        List<ConfigConsistencyChecker.ConsistencyCheckResult> results = configConsistencyChecker.batchCheckModelConfigConsistency(modelIds);
        return success(results);
    }

    @GetMapping("/check-all-active-consistency")
    @Operation(summary = "检查所有活跃车型的配置一致性")
    @PreAuthorize("@ss.hasPermission('cms:car-model-option:query')")
    public CommonResult<List<ConfigConsistencyChecker.ConsistencyCheckResult>> checkAllActiveModelsConsistency() {
        List<ConfigConsistencyChecker.ConsistencyCheckResult> results = configConsistencyChecker.checkAllActiveModelsConsistency();
        return success(results);
    }

}