package cn.iocoder.yudao.module.cms.dal.mysql.menu;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.cms.controller.admin.menu.vo.CmsMenuListReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.menu.CmsMenuDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * CMS菜单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CmsMenuMapper extends BaseMapperX<CmsMenuDO> {

    default PageResult<CmsMenuDO> selectPage(CmsMenuListReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CmsMenuDO>()
                .likeIfPresent(CmsMenuDO::getName, reqVO.getName())
                .eqIfPresent(CmsMenuDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(CmsMenuDO::getCreateTime, reqVO.getCreateTime())
                .orderByAsc(CmsMenuDO::getSort)
                .orderByDesc(CmsMenuDO::getId));
    }

    default List<CmsMenuDO> selectList(CmsMenuListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CmsMenuDO>()
                .likeIfPresent(CmsMenuDO::getName, reqVO.getName())
                .eqIfPresent(CmsMenuDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(CmsMenuDO::getCreateTime, reqVO.getCreateTime())
                .orderByAsc(CmsMenuDO::getSort)
                .orderByDesc(CmsMenuDO::getId));
    }

    /**
     * 查询所有启用状态的菜单，用于构建菜单树
     */
    default List<CmsMenuDO> selectEnabledList() {
        return selectList(new LambdaQueryWrapperX<CmsMenuDO>()
                .eq(CmsMenuDO::getStatus, 0) // 0-启用
                .orderByAsc(CmsMenuDO::getSort)
                .orderByDesc(CmsMenuDO::getId));
    }

    /**
     * 根据父菜单ID查询子菜单列表
     */
    default List<CmsMenuDO> selectListByParentId(Long parentId) {
        return selectList(CmsMenuDO::getParentId, parentId);
    }

    /**
     * 根据父菜单ID查询子菜单数量
     */
    default Long selectCountByParentId(Long parentId) {
        return selectCount(CmsMenuDO::getParentId, parentId);
    }

    /**
     * 根据路径查询菜单（排除指定ID）
     */
    default CmsMenuDO selectByPathAndIdNot(String path, Long id) {
        return selectOne(new LambdaQueryWrapperX<CmsMenuDO>()
                .eq(CmsMenuDO::getPath, path)
                .ne(id != null, CmsMenuDO::getId, id));
    }

    /**
     * 根据路径查询菜单
     */
    default CmsMenuDO selectByPath(String path) {
        return selectOne(CmsMenuDO::getPath, path);
    }

    /**
     * 根据ID获取菜单的物化路径
     */
    @Select("SELECT materialized_path FROM cms_menu WHERE id = #{menuId} AND deleted = 0")
    String selectMaterializedPathById(@Param("menuId") Long menuId);

    /**
     * 递归查询菜单路径（备用方法，当物化路径不可用时使用）
     * 从指定菜单ID开始，向上递归查询到根菜单，构建完整路径
     */
    String selectFullPathById(@Param("menuId") Long menuId);

    /**
     * 查询菜单的所有子菜单ID（递归）
     */
    List<Long> selectChildrenIds(@Param("parentId") Long parentId);

    /**
     * 批量查询菜单的父菜单路径
     */
    List<CmsMenuDO> selectHierarchyByIds(@Param("menuIds") List<Long> menuIds);

    /**
     * 更新菜单的物化路径
     */
    @Update("UPDATE cms_menu SET materialized_path = #{materializedPath} WHERE id = #{menuId}")
    void updateMaterializedPath(@Param("menuId") Long menuId, @Param("materializedPath") String materializedPath);

    /**
     * 批量更新子菜单的物化路径
     */
    void updateChildrenMaterializedPaths(@Param("parentId") Long parentId, @Param("oldParentPath") String oldParentPath, @Param("newParentPath") String newParentPath);
}