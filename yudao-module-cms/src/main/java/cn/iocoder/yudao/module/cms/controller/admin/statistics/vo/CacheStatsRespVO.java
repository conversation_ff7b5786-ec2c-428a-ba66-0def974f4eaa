package cn.iocoder.yudao.module.cms.controller.admin.statistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 缓存统计 Response VO")
@Data
public class CacheStatsRespVO {

    @Schema(description = "缓存名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "menu-tree")
    private String cacheName;

    @Schema(description = "命中次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "5000")
    private Long hitCount;

    @Schema(description = "未命中次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "500")
    private Long missCount;

    @Schema(description = "驱逐次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "50")
    private Long evictionCount;

    @Schema(description = "加载次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "550")
    private Long loadCount;

    @Schema(description = "加载成功次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "545")
    private Long loadSuccessCount;

    @Schema(description = "加载失败次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "5")
    private Long loadFailureCount;

    @Schema(description = "命中率", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.9091")
    private Double hitRate;

    @Schema(description = "平均加载时间（纳秒）", requiredMode = Schema.RequiredMode.REQUIRED, example = "100000")
    private Double averageLoadPenalty;

    @Schema(description = "总加载时间（纳秒）", requiredMode = Schema.RequiredMode.REQUIRED, example = "55000000")
    private Long totalLoadTime;

    @Schema(description = "缓存大小", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private Long size;

    @Schema(description = "自定义命中次数", example = "4800")
    private Long customHitCount;

    @Schema(description = "自定义未命中次数", example = "480")
    private Long customMissCount;

    @Schema(description = "自定义驱逐次数", example = "48")
    private Long customEvictionCount;
}