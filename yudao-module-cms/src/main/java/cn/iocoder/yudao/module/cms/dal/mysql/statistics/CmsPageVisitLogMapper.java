package cn.iocoder.yudao.module.cms.dal.mysql.statistics;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.cms.controller.admin.statistics.vo.CmsPageVisitLogPageReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.statistics.CmsPageVisitLogDO;
import cn.iocoder.yudao.module.cms.dal.dataobject.statistics.CmsPageVisitStatsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * CMS页面访问统计 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CmsPageVisitLogMapper extends BaseMapperX<CmsPageVisitLogDO> {

    default PageResult<CmsPageVisitLogDO> selectPage(CmsPageVisitLogPageReqVO pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<CmsPageVisitLogDO>()
                .eqIfPresent(CmsPageVisitLogDO::getPageId, pageReqVO.getPageId())
                .eqIfPresent(CmsPageVisitLogDO::getPageUuid, pageReqVO.getPageUuid())
                .eqIfPresent(CmsPageVisitLogDO::getUserId, pageReqVO.getUserId())
                .likeIfPresent(CmsPageVisitLogDO::getIp, pageReqVO.getIp())
                .betweenIfPresent(CmsPageVisitLogDO::getVisitTime, pageReqVO.getVisitTime())
                .orderByDesc(CmsPageVisitLogDO::getVisitTime));
    }

    /**
     * 根据页面ID查询访问日志
     */
    default PageResult<CmsPageVisitLogDO> selectPageByPageId(Long pageId, PageParam pageReq) {
        return selectPage(pageReq, new LambdaQueryWrapperX<CmsPageVisitLogDO>()
                .eq(CmsPageVisitLogDO::getPageId, pageId)
                .orderByDesc(CmsPageVisitLogDO::getVisitTime));
    }

    /**
     * 根据时间范围查询访问日志
     */
    default List<CmsPageVisitLogDO> selectListByDateRange(LocalDateTime startTime, LocalDateTime endTime) {
        return selectList(new LambdaQueryWrapperX<CmsPageVisitLogDO>()
                .between(CmsPageVisitLogDO::getVisitTime, startTime, endTime)
                .orderByDesc(CmsPageVisitLogDO::getVisitTime));
    }

    /**
     * 根据页面ID和日期范围查询访问日志
     */
    default List<CmsPageVisitLogDO> selectListByPageIdAndDateRange(Long pageId, LocalDateTime startTime, LocalDateTime endTime) {
        return selectList(new LambdaQueryWrapperX<CmsPageVisitLogDO>()
                .eq(CmsPageVisitLogDO::getPageId, pageId)
                .between(CmsPageVisitLogDO::getVisitTime, startTime, endTime)
                .orderByDesc(CmsPageVisitLogDO::getVisitTime));
    }

    /**
     * 统计指定日期的页面访问数据
     */
    List<CmsPageVisitStatsDO> selectDailyStats(@Param("statDate") LocalDate statDate);

    /**
     * 统计指定页面和日期的访问数据
     */
    CmsPageVisitStatsDO selectPageDailyStats(@Param("pageId") Long pageId, @Param("statDate") LocalDate statDate);

    /**
     * 批量插入访问日志
     */
    int insertBatch(List<CmsPageVisitLogDO> logs);

    /**
     * 删除指定日期之前的访问日志
     */
    default int deleteByDateBefore(LocalDateTime beforeDate) {
        return delete(new LambdaQueryWrapperX<CmsPageVisitLogDO>()
                .lt(CmsPageVisitLogDO::getVisitTime, beforeDate));
    }

    /**
     * 统计页面总访问量（用于热门页面排序）
     */
    List<java.util.Map<String, Object>> selectHotPages(@Param("startTime") LocalDateTime startTime, @Param("limit") Integer limit);

    /**
     * 根据页面ID统计总访问量
     */
    default Long selectTotalVisitsByPageId(Long pageId) {
        return selectCount(CmsPageVisitLogDO::getPageId, pageId);
    }

    /**
     * 根据页面ID和时间范围统计访问量
     */
    default Long selectVisitCountByPageIdAndDateRange(Long pageId, LocalDateTime startTime, LocalDateTime endTime) {
        return selectCount(new LambdaQueryWrapperX<CmsPageVisitLogDO>()
                .eq(CmsPageVisitLogDO::getPageId, pageId)
                .between(CmsPageVisitLogDO::getVisitTime, startTime, endTime));
    }

    /**
     * 聚合指定日期的访问统计数据
     */
    Integer aggregateDailyStats(@Param("statDate") LocalDate statDate);

    /**
     * 删除指定时间之前的访问日志（批量删除，支持limit）
     */
    Integer deleteExpiredLogs(@Param("beforeTime") LocalDateTime beforeTime, @Param("limit") int limit);

    /**
     * 删除指定时间之前的访问日志
     */
    Integer deleteByVisitTimeBefore(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 删除指定页面的所有访问日志
     */
    default int deleteByPageId(Long pageId) {
        return delete(new LambdaQueryWrapperX<CmsPageVisitLogDO>()
                .eq(CmsPageVisitLogDO::getPageId, pageId));
    }

    /**
     * 优化指定表
     */
    void optimizeTable(@Param("tableName") String tableName);

    /**
     * 获取页面今日访问量
     */
    @Select("SELECT COUNT(*) FROM cms_page_visit_log " +
            "WHERE page_id = #{pageId} AND DATE(visit_time) = CURDATE() AND deleted = 0")
    Integer selectTodayVisitCount(@Param("pageId") Long pageId);

    /**
     * 获取今日总访问量
     */
    @Select("SELECT COUNT(*) FROM cms_page_visit_log " +
            "WHERE DATE(visit_time) = CURDATE() AND deleted = 0")
    Integer selectTodayTotalVisits();

    /**
     * 获取今日独立访客数
     */
    @Select("SELECT COUNT(DISTINCT COALESCE(user_id, session_id, ip)) FROM cms_page_visit_log " +
            "WHERE DATE(visit_time) = CURDATE() AND deleted = 0")
    Integer selectTodayUniqueVisitors();

    /**
     * 获取当前小时访问量
     */
    @Select("SELECT COUNT(*) FROM cms_page_visit_log " +
            "WHERE DATE_FORMAT(visit_time, '%Y-%m-%d %H') = DATE_FORMAT(NOW(), '%Y-%m-%d %H') AND deleted = 0")
    Integer selectCurrentHourVisits();

    /**
     * 获取在线用户数（最近5分钟有访问记录的）
     */
    @Select("SELECT COUNT(DISTINCT COALESCE(user_id, session_id, ip)) FROM cms_page_visit_log " +
            "WHERE visit_time >= DATE_SUB(NOW(), INTERVAL 5 MINUTE) AND deleted = 0")
    Integer selectOnlineUsers();

}