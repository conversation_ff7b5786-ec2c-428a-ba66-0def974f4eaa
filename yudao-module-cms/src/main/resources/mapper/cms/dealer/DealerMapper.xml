<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.cms.dal.mysql.dealer.DealerMapper">

    <!-- 通用查询条件片段 -->
    <sql id="commonSearchConditions">
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="region != null and region != ''">
            AND region = #{region}
        </if>
        <if test="postcode != null and postcode != ''">
            AND postcode LIKE CONCAT('%', #{postcode}, '%')
        </if>
        <if test="services != null and services.size() > 0">
            AND (
            <foreach collection="services" item="service" separator=" OR ">
                services LIKE CONCAT('%', #{service}, '%')
            </foreach>
            )
        </if>
    </sql>

    <!-- 基础查询字段 -->
    <sql id="baseColumns">
        id, name, address, postcode, phone, email, website, 
        latitude, longitude, region, services, status,
        creator, create_time, updater, update_time, deleted, tenant_id
    </sql>

    <!-- 带距离信息的查询字段 -->
    <sql id="columnsWithDistance">
        <include refid="baseColumns"/>,
        ST_Distance_Sphere(POINT(longitude, latitude), POINT(#{centerLng}, #{centerLat})) as distance
    </sql>

    <!-- 统一的分页查询方法，支持距离计算 -->
    <select id="searchDealersUnified" resultType="cn.iocoder.yudao.module.cms.dal.dataobject.dealer.DealerWithDistanceDO">
        SELECT 
        <if test="centerLat != null and centerLng != null">
            <include refid="columnsWithDistance"/>
        </if>
        <if test="centerLat == null or centerLng == null">
            <include refid="baseColumns"/>, NULL as distance
        </if>
        FROM cms_dealer
        WHERE status = 1
        <include refid="commonSearchConditions"/>
        <if test="centerLat != null and centerLng != null">
            ORDER BY distance
        </if>
        <if test="centerLat == null or centerLng == null">
            ORDER BY id DESC
        </if>
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 统一的计数方法 -->
    <select id="countDealersUnified" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM cms_dealer
        WHERE status = 1
        <include refid="commonSearchConditions"/>
    </select>


    <!-- 查询附近的经销商 -->
    <select id="selectNearbyDealers" resultType="cn.iocoder.yudao.module.cms.dal.dataobject.dealer.DealerWithDistanceDO">
        SELECT <include refid="baseColumns"/>,
        ST_Distance_Sphere(POINT(longitude, latitude), POINT(#{lng}, #{lat})) as distance
        FROM cms_dealer
        WHERE status = 1
        AND ST_Distance_Sphere(POINT(longitude, latitude), POINT(#{lng}, #{lat})) &lt;= #{radiusMeters}
        ORDER BY ST_Distance_Sphere(POINT(longitude, latitude), POINT(#{lng}, #{lat}))
    </select>

</mapper>