package cn.iocoder.yudao.module.cms.dal.dataobject.car;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.type.JsonStringListTypeHandler;
import cn.iocoder.yudao.framework.mybatis.core.type.JsonMapTypeHandler;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 车型配置选项 DO
 *
 * <AUTHOR>
 */
@TableName(value = "cms_car_model_options", autoResultMap = true)
@KeySequence("cms_car_model_options_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CarModelOptionDO extends BaseDO {

    /**
     * 配置选项ID
     */
    @TableId
    private Long id;

    /**
     * 车型ID
     */
    private Long modelId;

    /**
     * 选项类型ID
     */
    private Long optionTypeId;

    /**
     * 选项代码
     */
    private String optionCode;

    /**
     * 选项名称
     */
    private String name;

    /**
     * 选项价格
     */
    private BigDecimal price;

    /**
     * 选项图片URL
     */
    private String imageUrl;

    /**
     * 缩略图URL数组
     */
    @TableField(typeHandler = JsonStringListTypeHandler.class)
    private List<String> thumbnailUrls;

    /**
     * 需要的配置包
     */
    private String requiredPackage;

    /**
     * 选项配置数据
     *
     * 根据option_type的schema存储
     */
    @TableField(typeHandler = JsonMapTypeHandler.class)
    private Map<String, Object> configData;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 状态
     *
     * 枚举 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer status;

}