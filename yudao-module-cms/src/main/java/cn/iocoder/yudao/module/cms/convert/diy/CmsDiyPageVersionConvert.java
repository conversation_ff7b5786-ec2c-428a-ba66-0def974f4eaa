package cn.iocoder.yudao.module.cms.convert.diy;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.diy.vo.version.CmsDiyPageVersionRespVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageVersionDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * CMS DIY 页面版本 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface CmsDiyPageVersionConvert {

    CmsDiyPageVersionConvert INSTANCE = Mappers.getMapper(CmsDiyPageVersionConvert.class);

    CmsDiyPageVersionRespVO convert(CmsDiyPageVersionDO bean);

    List<CmsDiyPageVersionRespVO> convertList(List<CmsDiyPageVersionDO> list);

    PageResult<CmsDiyPageVersionRespVO> convertPage(PageResult<CmsDiyPageVersionDO> page);
}