package cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 车型融资方案表单选项 Response VO")
@Data
public class CarModelFinancePlanFormOptionsVO {

    @Schema(description = "可选的融资选项列表", example = "[]")
    private List<FinanceOptionRespVO> financeOptions;

    @Schema(description = "可选的融资期限列表", example = "[]")
    private List<FinanceTermRespVO> financeTerms;

    @Schema(description = "可选的首付选项列表", example = "[]")
    private List<DownPaymentOptionRespVO> downPaymentOptions;

}