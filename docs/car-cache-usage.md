# 车型管理系统缓存使用指南

## 概述

车型管理系统使用Spring Cache + Redis实现缓存，与yudao框架的`TimeoutRedisCacheManager`完全兼容。

## 缓存配置特性

### 1. 自定义过期时间

使用yudao框架的`TimeoutRedisCacheManager`，支持在`@Cacheable`注解中直接指定过期时间：

```java
// 格式：cacheName#ttl
// ttl单位：d(天)、h(小时)、m(分钟)、s(秒)，默认s(秒)

@Cacheable(cacheNames = "car:model#1h", key = "#id")  // 1小时过期
@Cacheable(cacheNames = "car:series#30m", key = "#id") // 30分钟过期
@Cacheable(cacheNames = "car:hot#10m", key = "#limit") // 10分钟过期
@Cacheable(cacheNames = "car:finance#4h", key = "#id") // 4小时过期
```

### 2. 缓存常量定义

所有缓存名称定义在`CarCacheConstants`类中：

```java
public interface CarCacheConstants {
    String CAR_SERIES = "car:series#2h";              // 车系缓存 - 2小时
    String CAR_MODEL = "car:model#1h";                // 车型缓存 - 1小时
    String CAR_MODEL_HOT_LIST = "car:model:hot#10m";  // 热门车型 - 10分钟
    String CAR_FINANCE_PLAN = "car:finance:plan#4h";  // 融资方案 - 4小时
    // ... 更多缓存定义
}
```

## 使用示例

### 1. Service层使用缓存注解

```java
@Service
public class CarModelServiceImpl implements CarModelService {
    
    // 查询缓存
    @Cacheable(cacheNames = CarCacheConstants.CAR_MODEL, 
              key = "#id", 
              unless = "#result == null")
    public CarModelDO getCarModel(Long id) {
        return carModelMapper.selectById(id);
    }
    
    // 清除缓存
    @CacheEvict(cacheNames = CarCacheConstants.CAR_MODEL, key = "#id")
    public void deleteCarModel(Long id) {
        carModelMapper.deleteById(id);
    }
    
    // 批量清除缓存
    @Caching(evict = {
        @CacheEvict(cacheNames = CarCacheConstants.CAR_MODEL, key = "#id"),
        @CacheEvict(cacheNames = CarCacheConstants.CAR_MODEL_LIST_BY_SERIES, allEntries = true)
    })
    public void updateCarModel(CarModelUpdateReqVO updateReqVO) {
        // 更新逻辑
    }
}
```

### 2. 使用缓存工具类

```java
@Service
public class CarBusinessService {
    
    @Resource
    private CarCacheHelper carCacheHelper;
    
    public CarModelDO getCarModelWithCache(Long id) {
        // 使用缓存工具类获取数据
        return carCacheHelper.get(
            CarCacheConstants.CAR_MODEL,
            id,
            () -> carModelMapper.selectById(id)
        );
    }
    
    public void clearCarModelCache(Long id) {
        // 清除指定缓存
        carCacheHelper.evict(CarCacheConstants.CAR_MODEL, id);
    }
}
```

### 3. 生成复合缓存键

对于多参数的缓存键，使用`CarCacheHelper.generateKey()`方法：

```java
@Cacheable(cacheNames = CarCacheConstants.CAR_MODEL_LIST_BY_SERIES,
          key = "T(cn.iocoder.yudao.module.cms.framework.car.util.CarCacheHelper).generateKey(#seriesId, #status)")
public List<CarModelDO> getCarModelList(Long seriesId, Integer status) {
    return carModelMapper.selectListBySeriesIdAndStatus(seriesId, status);
}
```

## 缓存监控与管理

### 1. 获取缓存统计信息

```java
@Resource
private CarCacheMonitorService cacheMonitorService;

// 获取所有缓存统计
List<CacheStats> stats = cacheMonitorService.getCacheStats();

// 清理指定缓存
cacheMonitorService.clearCache(CarCacheConstants.CAR_MODEL);

// 清理所有缓存
Map<String, Boolean> results = cacheMonitorService.clearAllCaches();
```

### 2. 缓存预热

在系统启动或维护后，可以调用预热方法加载常用数据：

```java
@PostConstruct
public void init() {
    cacheMonitorService.warmupCache();
}
```

## 最佳实践

### 1. 合理设置过期时间

- 静态数据（车系、品牌）：2-4小时
- 动态数据（车型列表）：30分钟-1小时
- 热点数据（热门车型）：5-10分钟
- 计算结果（融资计算）：5-15分钟

### 2. 缓存键设计

- 使用有意义的前缀：`car:model:`、`car:series:`
- 多参数使用`generateKey()`方法生成
- 避免使用过长的键名

### 3. 缓存更新策略

- 创建/更新/删除操作要及时清理相关缓存
- 使用`@Caching`注解批量处理多个缓存
- 对于列表缓存，更新单个数据时可以使用`allEntries = true`清理全部

### 4. 异常处理

- 缓存操作失败不应影响业务逻辑
- CarCacheHelper已内置异常处理机制
- 监控日志中的缓存警告信息

## 配置说明

### application.yml 配置

```yaml
spring:
  cache:
    type: redis
    redis:
      time-to-live: 3600000  # 默认1小时（毫秒）
      key-prefix: "cms:"      # 缓存键前缀
      cache-null-values: false # 不缓存null值
      use-key-prefix: true    # 使用键前缀

# Redis配置
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 10000
```

## 注意事项

1. **缓存穿透**：使用`unless = "#result == null"`避免缓存null值
2. **缓存雪崩**：通过不同的过期时间避免大量缓存同时失效
3. **缓存一致性**：确保更新数据时清理相关缓存
4. **性能监控**：定期查看缓存命中率和性能指标

## 故障排查

### 常见问题

1. **缓存不生效**
   - 检查是否启用了`@EnableCaching`
   - 确认Redis连接正常
   - 验证缓存名称格式是否正确

2. **缓存数据不一致**
   - 检查是否所有更新操作都清理了缓存
   - 确认事务边界设置正确

3. **缓存键冲突**
   - 使用不同的缓存名称前缀
   - 确保键生成逻辑唯一

### 日志查看

```bash
# 查看缓存相关日志
grep -i cache application.log

# 查看Redis操作日志
redis-cli monitor
```

## 总结

车型管理系统的缓存方案完全兼容yudao框架，通过合理使用Spring Cache注解和自定义过期时间，可以显著提升系统性能。配合缓存监控工具，能够有效管理和维护缓存状态。