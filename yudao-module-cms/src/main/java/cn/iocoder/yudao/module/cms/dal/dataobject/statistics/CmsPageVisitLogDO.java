package cn.iocoder.yudao.module.cms.dal.dataobject.statistics;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * CMS页面访问统计 DO
 *
 * <AUTHOR>
 */
@TableName("cms_page_visit_log")
@KeySequence("cms_page_visit_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsPageVisitLogDO extends BaseDO {

    /**
     * 访问记录ID
     */
    @TableId
    private Long id;

    /**
     * 页面ID
     */
    private Long pageId;

    /**
     * 页面UUID
     */
    private String pageUuid;

    /**
     * 用户ID（登录用户）
     */
    private Long userId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 访问IP
     */
    private String ip;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 来源页面
     */
    private String referer;

    /**
     * 访问时间
     */
    private LocalDateTime visitTime;

    /**
     * 停留时间（秒）
     */
    private Integer stayTime;

}