package cn.iocoder.yudao.module.cms.dal.mysql.car.finance;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.finance.CarModelFinancePlanDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 车型融资方案 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CarModelFinancePlanMapper extends BaseMapperX<CarModelFinancePlanDO> {

    default PageResult<CarModelFinancePlanDO> selectPage(Long modelId, Long financeOptionId,
                                                          Integer status, Integer pageNo, Integer pageSize) {
        return selectPage(new PageParam().setPageNo(pageNo).setPageSize(pageSize),new LambdaQueryWrapperX<CarModelFinancePlanDO>()
                .eqIfPresent(CarModelFinancePlanDO::getModelId, modelId)
                .eqIfPresent(CarModelFinancePlanDO::getFinanceOptionId, financeOptionId)
                .eqIfPresent(CarModelFinancePlanDO::getStatus, status)
                .orderByDesc(CarModelFinancePlanDO::getIsDefault)
                .orderByDesc(CarModelFinancePlanDO::getIsFeatured)
                .orderByAsc(CarModelFinancePlanDO::getSortOrder));
    }

    default List<CarModelFinancePlanDO> selectListByModelId(Long modelId) {
        return selectList(new LambdaQueryWrapperX<CarModelFinancePlanDO>()
                .eq(CarModelFinancePlanDO::getModelId, modelId)
                .orderByDesc(CarModelFinancePlanDO::getIsDefault)
                .orderByDesc(CarModelFinancePlanDO::getIsFeatured)
                .orderByAsc(CarModelFinancePlanDO::getSortOrder));
    }

    default CarModelFinancePlanDO selectDefaultPlan(Long modelId) {
        return selectOne(new LambdaQueryWrapperX<CarModelFinancePlanDO>()
                .eq(CarModelFinancePlanDO::getModelId, modelId)
                .eq(CarModelFinancePlanDO::getIsDefault, true)
                .eq(CarModelFinancePlanDO::getStatus, 0));
    }

    /**
     * 查询车型的融资方案（包含关联信息）
     */
    @Select("SELECT fp.*, fo.name as option_name, ft.name as term_name, ft.months, " +
            "dp.name as down_payment_name, dp.value as down_payment_value " +
            "FROM cms_car_model_finance_plans fp " +
            "INNER JOIN cms_finance_options fo ON fp.finance_option_id = fo.id " +
            "LEFT JOIN cms_finance_terms ft ON fp.term_id = ft.id " +
            "LEFT JOIN cms_down_payment_options dp ON fp.down_payment_id = dp.id " +
            "WHERE fp.model_id = #{modelId} AND fp.deleted = 0 AND fp.status = 0 " +
            "ORDER BY fp.is_default DESC, fp.is_featured DESC, fp.sort_order")
    List<CarModelFinancePlanDO> selectPlansWithDetails(@Param("modelId") Long modelId);

}