-- CMS模块性能优化索引
-- 为CMS相关表添加性能索引和约束

-- ================================
-- cms_menu表索引优化
-- ================================

-- 父子关系查询索引
CREATE INDEX idx_cms_menu_parent_id ON cms_menu(parent_id) USING BTREE;

-- 路径查询索引
CREATE INDEX idx_cms_menu_path ON cms_menu(path) USING BTREE;

-- 状态和排序索引
CREATE INDEX idx_cms_menu_status_sort ON cms_menu(status, sort) USING BTREE;

-- 租户隔离索引
CREATE INDEX idx_cms_menu_tenant_status ON cms_menu(tenant_id, status) USING BTREE;

-- 树形查询复合索引
CREATE INDEX idx_cms_menu_tree_query ON cms_menu(tenant_id, parent_id, status, sort) USING BTREE;

-- ================================
-- cms_diy_page表索引优化  
-- ================================

-- 路径唯一索引（包含租户）
CREATE UNIQUE INDEX uk_cms_diy_page_tenant_path ON cms_diy_page(tenant_id, path) USING BTREE;

-- UUID唯一索引
CREATE UNIQUE INDEX uk_cms_diy_page_uuid ON cms_diy_page(uuid) USING BTREE;

-- 菜单关联索引
CREATE INDEX idx_cms_diy_page_menu_id ON cms_diy_page(menu_id) USING BTREE;

-- 状态查询索引
CREATE INDEX idx_cms_diy_page_status ON cms_diy_page(status) USING BTREE;

-- 发布时间索引
CREATE INDEX idx_cms_diy_page_publish_time ON cms_diy_page(publish_time) USING BTREE;

-- 租户和状态复合索引
CREATE INDEX idx_cms_diy_page_tenant_status ON cms_diy_page(tenant_id, status) USING BTREE;

-- 版本查询索引（乐观锁）
CREATE INDEX idx_cms_diy_page_version ON cms_diy_page(id, version) USING BTREE;

-- 分页查询复合索引
CREATE INDEX idx_cms_diy_page_page_query ON cms_diy_page(tenant_id, status, update_time) USING BTREE;

-- 应用端访问索引
CREATE INDEX idx_cms_diy_page_app_access ON cms_diy_page(tenant_id, status, path, publish_time) USING BTREE;

-- ================================
-- cms_diy_page_version表索引优化
-- ================================

-- 页面版本关联索引
CREATE INDEX idx_cms_diy_page_version_page_id ON cms_diy_page_version(page_id) USING BTREE;

-- 版本号索引
CREATE INDEX idx_cms_diy_page_version_number ON cms_diy_page_version(page_id, version_number) USING BTREE;

-- 创建时间索引（用于版本清理）
CREATE INDEX idx_cms_diy_page_version_create_time ON cms_diy_page_version(create_time) USING BTREE;

-- 最新版本查询索引
CREATE INDEX idx_cms_diy_page_version_latest ON cms_diy_page_version(page_id, create_time DESC) USING BTREE;

-- ================================
-- cms_page_visit_log表索引优化
-- ================================

-- 页面访问统计索引
CREATE INDEX idx_cms_page_visit_log_page_id ON cms_page_visit_log(page_id) USING BTREE;

-- 访问时间索引
CREATE INDEX idx_cms_page_visit_log_visit_time ON cms_page_visit_log(visit_time) USING BTREE;

-- IP地址索引（用于去重统计）
CREATE INDEX idx_cms_page_visit_log_ip ON cms_page_visit_log(ip_address) USING BTREE;

-- 租户访问统计索引
CREATE INDEX idx_cms_page_visit_log_tenant ON cms_page_visit_log(tenant_id, visit_time) USING BTREE;

-- 页面访问统计复合索引
CREATE INDEX idx_cms_page_visit_log_stats ON cms_page_visit_log(page_id, visit_time, ip_address) USING BTREE;

-- 批量插入优化索引
CREATE INDEX idx_cms_page_visit_log_batch ON cms_page_visit_log(tenant_id, page_id, visit_time) USING BTREE;

-- ================================
-- cms_page_visit_stats表索引优化
-- ================================

-- 页面统计查询索引
CREATE INDEX idx_cms_page_visit_stats_page_id ON cms_page_visit_stats(page_id) USING BTREE;

-- 统计日期索引
CREATE INDEX idx_cms_page_visit_stats_date ON cms_page_visit_stats(stat_date) USING BTREE;

-- 页面日期复合唯一索引
CREATE UNIQUE INDEX uk_cms_page_visit_stats_page_date ON cms_page_visit_stats(page_id, stat_date) USING BTREE;

-- 租户统计索引
CREATE INDEX idx_cms_page_visit_stats_tenant ON cms_page_visit_stats(tenant_id, stat_date) USING BTREE;

-- 热门页面查询索引
CREATE INDEX idx_cms_page_visit_stats_hot ON cms_page_visit_stats(tenant_id, stat_date, total_visits DESC) USING BTREE;

-- 访问量排序索引
CREATE INDEX idx_cms_page_visit_stats_visits ON cms_page_visit_stats(total_visits DESC, unique_visitors DESC) USING BTREE;

-- ================================
-- 表分区优化（基于日期）
-- ================================

-- 访问日志表按月分区
-- ALTER TABLE cms_page_visit_log PARTITION BY RANGE (YEAR(visit_time) * 100 + MONTH(visit_time)) (
--     PARTITION p202401 VALUES LESS THAN (202402),
--     PARTITION p202402 VALUES LESS THAN (202403),
--     PARTITION p202403 VALUES LESS THAN (202404),
--     PARTITION p202404 VALUES LESS THAN (202405),
--     PARTITION p202405 VALUES LESS THAN (202406),
--     PARTITION p202406 VALUES LESS THAN (202407),
--     PARTITION p202407 VALUES LESS THAN (202408),
--     PARTITION p202408 VALUES LESS THAN (202409),
--     PARTITION p202409 VALUES LESS THAN (202410),
--     PARTITION p202410 VALUES LESS THAN (202411),
--     PARTITION p202411 VALUES LESS THAN (202412),
--     PARTITION p202412 VALUES LESS THAN (202501),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- ================================
-- 表结构优化建议
-- ================================

-- 为高频查询字段添加前缀索引
-- ALTER TABLE cms_diy_page ADD INDEX idx_name_prefix (name(20));

-- 为JSON字段添加生成列和索引（MySQL 5.7+）
-- ALTER TABLE cms_diy_page ADD COLUMN content_type VARCHAR(50) AS (JSON_EXTRACT(content, '$.type'));
-- CREATE INDEX idx_cms_diy_page_content_type ON cms_diy_page(content_type);

-- ================================
-- 性能监控视图
-- ================================

-- 创建页面性能监控视图
CREATE OR REPLACE VIEW v_cms_page_performance AS
SELECT 
    p.id,
    p.name,
    p.path,
    p.status,
    p.publish_time,
    p.update_time,
    COALESCE(s.total_visits, 0) as total_visits,
    COALESCE(s.unique_visitors, 0) as unique_visitors,
    COALESCE(s.avg_visit_duration, 0) as avg_visit_duration,
    p.version,
    CASE 
        WHEN p.status = 2 THEN '已发布'
        WHEN p.status = 1 THEN '草稿'
        WHEN p.status = 3 THEN '已下线'
        ELSE '未知'
    END as status_name
FROM cms_diy_page p
LEFT JOIN (
    SELECT 
        page_id,
        SUM(total_visits) as total_visits,
        SUM(unique_visitors) as unique_visitors,
        AVG(avg_visit_duration) as avg_visit_duration
    FROM cms_page_visit_stats 
    WHERE stat_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    GROUP BY page_id
) s ON p.id = s.page_id
WHERE p.deleted = 0;

-- 创建热门页面视图
CREATE OR REPLACE VIEW v_cms_hot_pages AS
SELECT 
    p.id,
    p.name,
    p.path,
    s.total_visits,
    s.unique_visitors,
    s.avg_visit_duration,
    RANK() OVER (ORDER BY s.total_visits DESC) as visit_rank,
    RANK() OVER (ORDER BY s.unique_visitors DESC) as visitor_rank
FROM cms_diy_page p
INNER JOIN (
    SELECT 
        page_id,
        SUM(total_visits) as total_visits,
        SUM(unique_visitors) as unique_visitors,
        AVG(avg_visit_duration) as avg_visit_duration
    FROM cms_page_visit_stats 
    WHERE stat_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
    GROUP BY page_id
    HAVING SUM(total_visits) >= 100
) s ON p.id = s.page_id
WHERE p.deleted = 0 AND p.status = 2
ORDER BY s.total_visits DESC
LIMIT 50;

-- ================================
-- 索引使用统计
-- ================================

-- 查看索引使用情况的存储过程
DELIMITER //
CREATE PROCEDURE sp_cms_index_usage_report()
BEGIN
    SELECT 
        t.TABLE_NAME,
        t.INDEX_NAME,
        t.COLUMN_NAME,
        s.rows_examined,
        s.rows_sent,
        s.select_scan
    FROM information_schema.STATISTICS t
    LEFT JOIN (
        SELECT 
            object_name,
            index_name,
            SUM(count_read) as rows_examined,
            SUM(count_write) as rows_sent,
            SUM(count_read) - SUM(count_write) as select_scan
        FROM performance_schema.table_io_waits_summary_by_index_usage 
        WHERE object_schema = DATABASE()
        GROUP BY object_name, index_name
    ) s ON t.TABLE_NAME = s.object_name AND t.INDEX_NAME = s.index_name
    WHERE t.TABLE_SCHEMA = DATABASE() 
      AND t.TABLE_NAME LIKE 'cms_%'
    ORDER BY t.TABLE_NAME, s.rows_examined DESC;
END //
DELIMITER ;

-- ================================
-- 清理和维护脚本
-- ================================

-- 清理过期访问日志的存储过程
DELIMITER //
CREATE PROCEDURE sp_cms_cleanup_visit_logs(IN days_to_keep INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE batch_size INT DEFAULT 10000;
    DECLARE affected_rows INT DEFAULT 0;
    
    -- 批量删除过期数据
    REPEAT
        DELETE FROM cms_page_visit_log 
        WHERE visit_time < DATE_SUB(NOW(), INTERVAL days_to_keep DAY)
        LIMIT batch_size;
        
        SET affected_rows = ROW_COUNT();
        
        -- 防止锁表时间过长
        DO SLEEP(0.1);
    UNTIL affected_rows < batch_size END REPEAT;
    
    -- 优化表
    OPTIMIZE TABLE cms_page_visit_log;
END //
DELIMITER ;

-- 清理过期版本数据的存储过程
DELIMITER //
CREATE PROCEDURE sp_cms_cleanup_page_versions(IN versions_to_keep INT)
BEGIN
    DELETE v1 FROM cms_diy_page_version v1
    INNER JOIN (
        SELECT page_id, version_number
        FROM cms_diy_page_version v2
        WHERE v2.page_id = v1.page_id
        ORDER BY v2.create_time DESC
        LIMIT versions_to_keep, 1000000
    ) v2 ON v1.page_id = v2.page_id AND v1.version_number = v2.version_number;
END //
DELIMITER ;

-- 提交所有更改
COMMIT;