# CMS模块配置文件
yudao:
  cms:
    # 缓存配置
    cache:
      enabled: true
      # Caffeine L1缓存配置
      caffeine:
        maximum-size: 10000
        expire-after-write: 10m
        expire-after-access: 5m
        record-stats: true
        initial-capacity: 100
      # Redis L2缓存配置
      redis:
        enabled: true
        default-ttl: 30m
        key-prefix: "cms:cache"
        sync-topic: "cms:cache:sync"
      # 缓存预热配置
      warmup:
        enabled: true
        on-startup: true
        menu-cache-size: 1000
        page-cache-size: 5000
    
    # DIY页面配置
    diy-page:
      # 页面路径配置
      path:
        max-length: 200
        allowed-pattern: "^[a-zA-Z0-9\\-_/]+$"
        reserved-paths:
          - "/admin"
          - "/api"
          - "/system"
      # 内容配置
      content:
        max-size: 1048576  # 1MB
        allowed-components:
          - "text"
          - "image"
          - "video"
          - "carousel"
          - "form"
          - "table"
      # 版本管理配置
      version:
        max-versions: 20
        cleanup-interval: "0 0 2 * * ?"  # 每天凌晨2点清理
        auto-backup: true
      # 发布配置
      publish:
        validation:
          required-content: true
          min-components: 1
          max-file-size: 10485760  # 10MB
        cdn:
          enabled: false
          base-url: "https://cdn.example.com"
    
    # 菜单配置
    menu:
      # 层级配置
      max-depth: 5
      # 路径配置
      path:
        max-length: 100
        separator: "/"
      # 缓存配置
      cache:
        tree-ttl: 1h
        node-ttl: 30m
    
    # 访问统计配置
    statistics:
      # 数据收集配置
      collection:
        enabled: true
        async: true
        batch-size: 100
        batch-interval: 5s
      # 数据聚合配置
      aggregation:
        enabled: true
        interval: "0 0 1 * * ?"  # 每天凌晨1点聚合
        keep-raw-days: 30
        keep-stats-days: 365
      # 热门页面配置
      hot-pages:
        enabled: true
        calculation-days: 7
        min-visits: 100
        max-count: 50
    
    # 安全配置
    security:
      # XSS防护
      xss:
        enabled: true
        allowed-tags:
          - "p"
          - "div"
          - "span"
          - "img"
          - "a"
          - "br"
          - "strong"
          - "em"
      # 路径验证
      path-validation:
        strict-mode: true
        check-reserved: true
      # 租户隔离
      tenant-isolation:
        strict: true
        cache-isolation: true
    
    # 性能配置
    performance:
      # 数据库配置
      database:
        connection-pool:
          maximum-pool-size: 20
          minimum-idle: 5
          connection-timeout: 30000
          idle-timeout: 600000
        query-timeout: 30s
        batch-size: 1000
      # 并发配置
      async:
        core-pool-size: 5
        max-pool-size: 20
        queue-capacity: 1000
        thread-name-prefix: "cms-async-"
      # 监控配置
      monitoring:
        metrics-enabled: true
        slow-query-threshold: 1000ms
        cache-stats-interval: 60s

# Spring配置扩展
spring:
  # 数据源配置
  datasource:
    hikari:
      # CMS模块专用连接池配置
      maximum-pool-size: ${yudao.cms.performance.database.connection-pool.maximum-pool-size:20}
      minimum-idle: ${yudao.cms.performance.database.connection-pool.minimum-idle:5}
      connection-timeout: ${yudao.cms.performance.database.connection-pool.connection-timeout:30000}
      idle-timeout: ${yudao.cms.performance.database.connection-pool.idle-timeout:600000}
  
  # 缓存配置
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=${yudao.cms.cache.caffeine.maximum-size:10000},expireAfterWrite=${yudao.cms.cache.caffeine.expire-after-write:10m},recordStats
  
  # Redis配置
  redis:
    # 连接池配置
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 2
        max-wait: 30000ms
  
  # 任务调度配置
  task:
    scheduling:
      pool:
        size: 5
      thread-name-prefix: "cms-scheduling-"
    execution:
      pool:
        core-size: ${yudao.cms.performance.async.core-pool-size:5}
        max-size: ${yudao.cms.performance.async.max-pool-size:20}
        queue-capacity: ${yudao.cms.performance.async.queue-capacity:1000}
      thread-name-prefix: ${yudao.cms.performance.async.thread-name-prefix:cms-async-}

# 日志配置
logging:
  level:
    cn.iocoder.yudao.module.cms: INFO
    # 缓存相关日志
    cn.iocoder.yudao.module.cms.framework.cache: DEBUG
    # 性能监控日志
    cn.iocoder.yudao.module.cms.service.statistics: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId},%X{tenantId}] %logger{50} - %msg%n"

# Actuator监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,metrics,prometheus,caches,beans
  endpoint:
    health:
      show-details: always
    metrics:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: ${spring.application.name}
      module: cms