package cn.iocoder.yudao.module.cms.framework.cache.core;

import cn.iocoder.yudao.module.cms.framework.cache.config.CmsCacheProperties;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.data.redis.cache.RedisCacheManager;

import java.util.Collection;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 多级缓存管理器
 * 
 * 实现 L1 (Caffeine) + L2 (Redis) 双级缓存架构
 * - L1: 本地缓存，快速访问，容量有限
 * - L2: 分布式缓存，容量大，支持集群同步
 *
 * <AUTHOR>
 */
@Slf4j
public class MultiLevelCacheManager implements CacheManager {

    private final CacheManager l1CacheManager; // Caffeine 缓存管理器
    private final RedisCacheManager l2CacheManager; // Redis 缓存管理器
    private final CmsCacheProperties cacheProperties;
    private final ConcurrentMap<String, MultiLevelCache> cacheMap = new ConcurrentHashMap<>();

    public MultiLevelCacheManager(CmsCacheProperties cacheProperties, 
                                  RedisCacheManager l2CacheManager) {
        this.cacheProperties = cacheProperties;
        this.l2CacheManager = l2CacheManager;
        this.l1CacheManager = createL1CacheManager();
    }

    /**
     * 创建 L1 缓存管理器（Caffeine）
     */
    private CacheManager createL1CacheManager() {
        return new CacheManager() {
            private final ConcurrentMap<String, Cache> caches = new ConcurrentHashMap<>();

            @Override
            public Cache getCache(String name) {
                return caches.computeIfAbsent(name, this::createCaffeineCache);
            }

            @Override
            public Collection<String> getCacheNames() {
                return caches.keySet();
            }

            private Cache createCaffeineCache(String name) {
                CmsCacheProperties.L1Cache l1Config = cacheProperties.getL1();
                com.github.benmanes.caffeine.cache.Cache<Object, Object> caffeineCache = Caffeine.newBuilder()
                        .initialCapacity(l1Config.getInitialCapacity())
                        .maximumSize(l1Config.getMaximumSize())
                        .expireAfterWrite(l1Config.getExpireAfterWrite())
                        .expireAfterAccess(l1Config.getExpireAfterAccess())
                        .recordStats() // 启用统计信息
                        .build();
                
                return new CaffeineCache(name, caffeineCache);
            }
        };
    }

    @Override
    public Cache getCache(String name) {
        if (!cacheProperties.getEnabled()) {
            // 如果缓存被禁用，直接返回 L2 缓存
            return l2CacheManager.getCache(name);
        }

        return cacheMap.computeIfAbsent(name, this::createMultiLevelCache);
    }

    @Override
    public Collection<String> getCacheNames() {
        return cacheMap.keySet();
    }

    /**
     * 创建多级缓存实例
     */
    private MultiLevelCache createMultiLevelCache(String name) {
        Cache l1Cache = l1CacheManager.getCache(name);
        Cache l2Cache = l2CacheManager.getCache(name);
        
        if (l1Cache == null || l2Cache == null) {
            log.warn("Failed to create multi-level cache for name: {}, l1Cache: {}, l2Cache: {}", 
                    name, l1Cache, l2Cache);
            return null;
        }

        return new MultiLevelCache(name, l1Cache, l2Cache);
    }

    /**
     * 获取 L1 缓存管理器
     */
    public CacheManager getL1CacheManager() {
        return l1CacheManager;
    }

    /**
     * 获取 L2 缓存管理器
     */
    public RedisCacheManager getL2CacheManager() {
        return l2CacheManager;
    }

    /**
     * 清除所有缓存
     */
    public void clearAll() {
        log.info("Clearing all multi-level caches");
        cacheMap.values().forEach(cache -> {
            try {
                cache.clear();
            } catch (Exception e) {
                log.error("Error clearing cache: {}", cache.getName(), e);
            }
        });
    }

    /**
     * 获取缓存统计信息
     */
    public CacheStats getCacheStats(String cacheName) {
        MultiLevelCache cache = cacheMap.get(cacheName);
        if (cache == null) {
            return null;
        }
        return cache.getStats();
    }
}