package cn.iocoder.yudao.module.cms.service.car;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelSpecCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelSpecPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelSpecUpdateReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarModelSpecDO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 车型规格管理 Service 接口
 *
 * <AUTHOR>
 */
public interface CarModelSpecService {

    /**
     * 创建车型规格
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCarModelSpec(@Valid CarModelSpecCreateReqVO createReqVO);

    /**
     * 更新车型规格
     *
     * @param updateReqVO 更新信息
     */
    void updateCarModelSpec(@Valid CarModelSpecUpdateReqVO updateReqVO);

    /**
     * 删除车型规格
     *
     * @param id 编号
     */
    void deleteCarModelSpec(Long id);

    /**
     * 获得车型规格
     *
     * @param id 编号
     * @return 车型规格
     */
    CarModelSpecDO getCarModelSpec(Long id);

    /**
     * 获得车型规格分页
     *
     * @param pageReqVO 分页查询
     * @return 车型规格分页
     */
    PageResult<CarModelSpecDO> getCarModelSpecPage(CarModelSpecPageReqVO pageReqVO);

    /**
     * 获得车型规格列表
     *
     * @param modelId 车型ID
     * @param category 规格分类
     * @param status 状态
     * @return 车型规格列表
     */
    List<CarModelSpecDO> getCarModelSpecList(Long modelId, String category, Integer status);

    /**
     * 根据分组获取车型规格列表
     *
     * @param modelId 车型ID
     * @param specGroup 规格分组
     * @return 车型规格列表
     */
    List<CarModelSpecDO> getCarModelSpecListByGroup(Long modelId, String specGroup);

    /**
     * 批量创建车型规格
     *
     * @param modelId 车型ID
     * @param createReqVOList 创建信息列表
     */
    void batchCreateCarModelSpec(Long modelId, List<CarModelSpecCreateReqVO> createReqVOList);

}