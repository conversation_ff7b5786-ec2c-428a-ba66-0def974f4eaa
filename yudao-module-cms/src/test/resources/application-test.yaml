# CMS 缓存配置
yudao:
  cms:
    cache:
      enabled: true
      l1:
        maximum-size: 1000
        expire-after-write: PT10M
        expire-after-access: PT5M
        initial-capacity: 50
      l2:
        menu-ttl: PT30M
        page-ttl: PT15M
        path-ttl: PT30M
        stats-ttl: PT10M
        tenant-key-prefix: "cms:tenant:"
      warmup:
        enabled: true
        menu-tree: true
        hot-pages-count: 50
        delay: PT10S

# Spring 配置
spring:
  cache:
    type: redis
  redis:
    host: localhost
    port: 6379
    database: 0