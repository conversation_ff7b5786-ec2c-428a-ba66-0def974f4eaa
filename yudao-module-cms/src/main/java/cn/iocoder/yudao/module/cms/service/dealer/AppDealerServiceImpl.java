package cn.iocoder.yudao.module.cms.service.dealer;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.app.dealer.vo.AppDealerDetailRespVO;
import cn.iocoder.yudao.module.cms.controller.app.dealer.vo.AppDealerRespVO;
import cn.iocoder.yudao.module.cms.controller.app.dealer.vo.AppDealerSearchReqVO;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.dal.dataobject.dealer.DealerDO;
import cn.iocoder.yudao.module.cms.dal.dataobject.dealer.DealerWithDistanceDO;
import cn.iocoder.yudao.module.cms.dal.mysql.dealer.DealerMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.cms.enums.ErrorCodeConstants.DEALER_NOT_EXISTS;

/**
 * App端经销商 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AppDealerServiceImpl implements AppDealerService {

    @Resource
    private DealerMapper dealerMapper;

    @Override
    public PageResult<AppDealerRespVO> searchDealers(AppDealerSearchReqVO reqVO) {
        PageResult<DealerWithDistanceDO> pageResult = dealerMapper.selectPage(reqVO);
        return BeanUtils.toBean(pageResult, AppDealerRespVO.class);
    }

    @Override
    public AppDealerDetailRespVO getDealerDetail(Long id) {
        DealerDO dealer = dealerMapper.selectById(id);
        if (dealer == null) {
            throw exception(DEALER_NOT_EXISTS);
        }
       return BeanUtils.toBean(dealer, AppDealerDetailRespVO.class);
    }

}