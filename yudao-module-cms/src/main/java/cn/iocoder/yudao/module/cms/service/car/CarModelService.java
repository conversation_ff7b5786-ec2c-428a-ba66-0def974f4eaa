package cn.iocoder.yudao.module.cms.service.car;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelUpdateReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarModelDO;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 车型管理 Service 接口
 *
 * <AUTHOR>
 */
public interface CarModelService {

    /**
     * 创建车型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCarModel(@Valid CarModelCreateReqVO createReqVO);

    /**
     * 更新车型
     *
     * @param updateReqVO 更新信息
     */
    void updateCarModel(@Valid CarModelUpdateReqVO updateReqVO);

    /**
     * 删除车型
     *
     * @param id 编号
     */
    void deleteCarModel(Long id);

    /**
     * 获得车型
     *
     * @param id 编号
     * @return 车型
     */
    CarModelDO getCarModel(Long id);

    /**
     * 获得车型分页
     *
     * @param pageReqVO 分页查询
     * @return 车型分页
     */
    PageResult<CarModelDO> getCarModelPage(CarModelPageReqVO pageReqVO);

    /**
     * 获得车型列表
     *
     * @param seriesId 车系ID
     * @param status 状态
     * @return 车型列表
     */
    List<CarModelDO> getCarModelList(Long seriesId, Integer status);

    /**
     * 根据编码获取车型
     *
     * @param code 车型编码
     * @return 车型信息
     */
    CarModelDO getCarModelByCode(String code);

    /**
     * 获得价格区间内的车型列表
     *
     * @param minPrice 最小价格
     * @param maxPrice 最大价格
     * @param status 状态
     * @return 车型列表
     */
    List<CarModelDO> getCarModelListByPriceRange(BigDecimal minPrice, BigDecimal maxPrice, Integer status);

    /**
     * 获得热销车型列表
     *
     * @param limit 限制数量
     * @return 热销车型列表
     */
    List<CarModelDO> getHotCarModelList(Integer limit);

    /**
     * 获得车型精简列表
     *
     * @return 车型精简列表
     */
    List<CarModelDO> getCarModelSimpleList();

}