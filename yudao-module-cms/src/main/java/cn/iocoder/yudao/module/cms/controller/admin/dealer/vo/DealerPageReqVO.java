package cn.iocoder.yudao.module.cms.controller.admin.dealer.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - CMS经销商分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DealerPageReqVO extends PageParam {

    @Schema(description = "经销商名称", example = "Allen Motor")
    private String name;

    @Schema(description = "地区", example = "London")
    private String region;

    @Schema(description = "状态：0-禁用，1-启用", example = "1")
    private Integer status;

    @Schema(description = "邮政编码", example = "1")
    private String postcode;

}