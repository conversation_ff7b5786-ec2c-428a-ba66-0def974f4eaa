package cn.iocoder.yudao.module.cms.service.car;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarOptionTypeCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarOptionTypePageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarOptionTypeUpdateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarOptionTypeUpdateSchemaReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarOptionTypeDO;
import cn.iocoder.yudao.module.cms.dal.mysql.car.CarOptionTypeMapper;
import cn.iocoder.yudao.module.cms.service.car.validator.JsonSchemaValidator;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.cms.enums.ErrorCodeConstants.*;

/**
 * 配置选项类型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CarOptionTypeServiceImpl implements CarOptionTypeService {

    @Resource
    private CarOptionTypeMapper carOptionTypeMapper;

    @Resource
    private JsonSchemaValidator jsonSchemaValidator;

    @Override
    public Long createCarOptionType(CarOptionTypeCreateReqVO createReqVO) {
        // 校验类型编码唯一性
        validateTypeCodeUnique(null, createReqVO.getTypeCode());
        
        // 校验JSON Schema格式
        if (createReqVO.getConfigSchema() != null) {
            validateJsonSchema(createReqVO.getConfigSchema());
        }

        // 插入
        CarOptionTypeDO carOptionType = BeanUtils.toBean(createReqVO, CarOptionTypeDO.class);
        carOptionTypeMapper.insert(carOptionType);
        // 返回
        return carOptionType.getId();
    }

    @Override
    public void updateCarOptionType(CarOptionTypeUpdateReqVO updateReqVO) {
        // 校验存在
        validateCarOptionTypeExists(updateReqVO.getId());
        
        // 校验类型编码唯一性
        validateTypeCodeUnique(updateReqVO.getId(), updateReqVO.getTypeCode());
        
        // 校验JSON Schema格式
        if (updateReqVO.getConfigSchema() != null) {
            validateJsonSchema(updateReqVO.getConfigSchema());
        }

        // 更新
        CarOptionTypeDO updateObj = BeanUtils.toBean(updateReqVO, CarOptionTypeDO.class);
        carOptionTypeMapper.updateById(updateObj);
    }

    @Override
    public void updateCarOptionTypeSchema(CarOptionTypeUpdateSchemaReqVO updateReqVO) {
        // 校验存在
        validateCarOptionTypeExists(updateReqVO.getId());
        // 使用实体对象更新，让MyBatis Plus自动处理JSON序列化
        CarOptionTypeDO updateObj = getCarOptionType(updateReqVO.getId());
        updateObj.setConfigSchema(updateReqVO.getConfigSchema());
        carOptionTypeMapper.updateById(updateObj);
    }

    @Override
    public void deleteCarOptionType(Long id) {
        // 校验存在
        validateCarOptionTypeExists(id);
        // 删除
        carOptionTypeMapper.deleteById(id);
    }

    @Override
    public CarOptionTypeDO getCarOptionType(Long id) {
        return carOptionTypeMapper.selectById(id);
    }

    @Override
    public PageResult<CarOptionTypeDO> getCarOptionTypePage(CarOptionTypePageReqVO pageReqVO) {
        return carOptionTypeMapper.selectPage(pageReqVO.getName(), pageReqVO.getStatus(),
                pageReqVO.getPageNo(), pageReqVO.getPageSize());
    }

    @Override
    public List<CarOptionTypeDO> getCarOptionTypeList(Integer status) {
        return carOptionTypeMapper.selectListByStatus(status);
    }

    @Override
    public CarOptionTypeDO getCarOptionTypeByCode(String typeCode) {
        return carOptionTypeMapper.selectByTypeCode(typeCode);
    }

    @Override
    public boolean validateJsonSchema(Map<String, Object> schema) {
        if (schema == null || schema.isEmpty()) {
            return true;
        }
        
        try {
            return jsonSchemaValidator.validateSchema(schema);
        } catch (Exception e) {
            throw exception(CAR_OPTION_TYPE_JSON_SCHEMA_INVALID);
        }
    }

    @Override
    public boolean validateConfigData(String typeCode, Map<String, Object> configData) {
        CarOptionTypeDO optionType = getCarOptionTypeByCode(typeCode);
        if (optionType == null) {
            throw exception(CAR_OPTION_TYPE_NOT_EXISTS);
        }
        
        if (optionType.getConfigSchema() == null || optionType.getConfigSchema().isEmpty()) {
            return true;
        }
        
        try {
            return jsonSchemaValidator.validateData(optionType.getConfigSchema(), configData);
        } catch (Exception e) {
            throw exception(CAR_MODEL_OPTION_CONFIG_INVALID);
        }
    }

    private void validateCarOptionTypeExists(Long id) {
        if (carOptionTypeMapper.selectById(id) == null) {
            throw exception(CAR_OPTION_TYPE_NOT_EXISTS);
        }
    }

    private void validateTypeCodeUnique(Long id, String typeCode) {
        CarOptionTypeDO carOptionType = carOptionTypeMapper.selectByTypeCode(typeCode);
        if (carOptionType == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的字典类型
        if (id == null) {
            throw exception(CAR_OPTION_TYPE_CODE_DUPLICATE);
        }
        if (!carOptionType.getId().equals(id)) {
            throw exception(CAR_OPTION_TYPE_CODE_DUPLICATE);
        }
    }

}