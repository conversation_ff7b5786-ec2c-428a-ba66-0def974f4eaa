package cn.iocoder.yudao.module.cms.dal.dataobject.menu;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * CMS菜单 DO
 *
 * <AUTHOR>
 */
@TableName("cms_menu")
@KeySequence("cms_menu_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsMenuDO extends BaseDO {

    /**
     * 菜单ID
     */
    @TableId
    private Long id;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 菜单路径
     */
    private String path;

    /**
     * 上级菜单ID，0表示根菜单
     */
    private Long parentId;

    /**
     * 显示顺序
     */
    private Integer sort;

    /**
     * 状态：0-启用，1-禁用
     *
     * 枚举 {@link cn.iocoder.yudao.module.cms.enums.menu.CmsMenuStatusEnum}
     */
    private Integer status;

    /**
     * 物化路径，完整路径用/分隔，如: /home/<USER>/cars
     * 用于优化菜单路径查询性能，避免递归查询
     */
    private String materializedPath;

}