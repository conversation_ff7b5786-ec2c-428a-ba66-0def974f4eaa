package cn.iocoder.yudao.module.cms.dal.mysql.car.type;

import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarPackageFeatureDO;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * List<CarPackageFeatureDO> 的 JSON 类型处理器
 * 用于处理数据库中 JSON 格式的配置包特性数组
 *
 * <AUTHOR>
 */
@MappedJdbcTypes(JdbcType.OTHER)
public class CarPackageFeatureListTypeHandler extends BaseTypeHandler<List<CarPackageFeatureDO>> {

    private static final TypeReference<List<CarPackageFeatureDO>> TYPE_REFERENCE = new TypeReference<List<CarPackageFeatureDO>>() {};

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<CarPackageFeatureDO> parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, JsonUtils.toJsonString(parameter));
    }

    @Override
    public List<CarPackageFeatureDO> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return parseJson(json);
    }

    @Override
    public List<CarPackageFeatureDO> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return parseJson(json);
    }

    @Override
    public List<CarPackageFeatureDO> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return parseJson(json);
    }

    private List<CarPackageFeatureDO> parseJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return JsonUtils.parseObject(json, TYPE_REFERENCE);
        } catch (Exception e) {
            throw new RuntimeException("JSON 配置包特性列表解析失败: " + json, e);
        }
    }
}