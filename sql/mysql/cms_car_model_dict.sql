-- 车型配置管理系统字典数据 SQL
-- 作者：开发团队
-- 日期：2025-01-09

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 字典类型
-- ----------------------------

-- 车型状态
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES ('车型状态', 'cms_car_model_status', 0, '车型的上架下架状态', '1', NOW(), '1', NOW(), b'0');

-- 融资方案类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES ('融资方案类型', 'cms_finance_plan_type', 0, '融资方案的类型', '1', NOW(), '1', NOW(), b'0');

-- 首付比例选项
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES ('首付比例', 'cms_down_payment_ratio', 0, '首付比例选项', '1', NOW(), '1', NOW(), b'0');

-- 融资期限选项
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES ('融资期限', 'cms_finance_term', 0, '融资期限选项（月）', '1', NOW(), '1', NOW(), b'0');

-- 车型级别
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES ('车型级别', 'cms_car_model_level', 0, '车型级别分类', '1', NOW(), '1', NOW(), b'0');

-- 燃料类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES ('燃料类型', 'cms_fuel_type', 0, '车辆燃料类型', '1', NOW(), '1', NOW(), b'0');

-- 驱动方式
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES ('驱动方式', 'cms_drive_type', 0, '车辆驱动方式', '1', NOW(), '1', NOW(), b'0');

-- 变速箱类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES ('变速箱类型', 'cms_transmission_type', 0, '变速箱类型', '1', NOW(), '1', NOW(), b'0');

-- 配置选项类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES ('配置选项类型', 'cms_car_option_category', 0, '车型配置选项分类', '1', NOW(), '1', NOW(), b'0');

-- API版本
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES ('API版本', 'cms_api_version', 0, 'API版本管理', '1', NOW(), '1', NOW(), b'0');

-- ----------------------------
-- 字典数据
-- ----------------------------

-- 车型状态数据
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (1, '在售', '1', 'cms_car_model_status', 0, 'success', '', '车型正在销售', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (2, '预售', '2', 'cms_car_model_status', 0, 'warning', '', '车型预售中', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (3, '停售', '3', 'cms_car_model_status', 0, 'danger', '', '车型已停售', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (4, '未上市', '4', 'cms_car_model_status', 0, 'info', '', '车型未上市', '1', NOW(), '1', NOW(), b'0');

-- 融资方案类型数据
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (1, '贷款', 'loan', 'cms_finance_plan_type', 0, 'primary', '', '贷款购车', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (2, '租赁', 'lease', 'cms_finance_plan_type', 0, 'success', '', '融资租赁', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (3, '全款', 'full', 'cms_finance_plan_type', 0, 'warning', '', '全款购车', '1', NOW(), '1', NOW(), b'0');

-- 首付比例数据
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (1, '0%', '0', 'cms_down_payment_ratio', 0, 'success', '', '零首付', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (2, '10%', '10', 'cms_down_payment_ratio', 0, 'default', '', '一成首付', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (3, '20%', '20', 'cms_down_payment_ratio', 0, 'default', '', '两成首付', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (4, '30%', '30', 'cms_down_payment_ratio', 0, 'default', '', '三成首付', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (5, '40%', '40', 'cms_down_payment_ratio', 0, 'default', '', '四成首付', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (6, '50%', '50', 'cms_down_payment_ratio', 0, 'default', '', '五成首付', '1', NOW(), '1', NOW(), b'0');

-- 融资期限数据
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (1, '12期', '12', 'cms_finance_term', 0, 'default', '', '12个月', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (2, '24期', '24', 'cms_finance_term', 0, 'default', '', '24个月', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (3, '36期', '36', 'cms_finance_term', 0, 'primary', '', '36个月', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (4, '48期', '48', 'cms_finance_term', 0, 'default', '', '48个月', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (5, '60期', '60', 'cms_finance_term', 0, 'warning', '', '60个月', '1', NOW(), '1', NOW(), b'0');

-- 车型级别数据
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (1, '微型车', 'A00', 'cms_car_model_level', 0, 'default', '', '微型车', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (2, '小型车', 'A0', 'cms_car_model_level', 0, 'default', '', '小型车', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (3, '紧凑型车', 'A', 'cms_car_model_level', 0, 'primary', '', '紧凑型车', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (4, '中型车', 'B', 'cms_car_model_level', 0, 'success', '', '中型车', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (5, '中大型车', 'C', 'cms_car_model_level', 0, 'warning', '', '中大型车', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (6, '豪华型车', 'D', 'cms_car_model_level', 0, 'danger', '', '豪华型车', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (7, 'SUV', 'SUV', 'cms_car_model_level', 0, 'info', '', 'SUV车型', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (8, 'MPV', 'MPV', 'cms_car_model_level', 0, 'info', '', 'MPV车型', '1', NOW(), '1', NOW(), b'0');

-- 燃料类型数据
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (1, '汽油', 'gasoline', 'cms_fuel_type', 0, 'default', '', '汽油车', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (2, '柴油', 'diesel', 'cms_fuel_type', 0, 'warning', '', '柴油车', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (3, '纯电动', 'electric', 'cms_fuel_type', 0, 'success', '', '纯电动车', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (4, '插电混动', 'phev', 'cms_fuel_type', 0, 'primary', '', '插电式混合动力', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (5, '油电混动', 'hev', 'cms_fuel_type', 0, 'info', '', '油电混合动力', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (6, '增程式', 'reev', 'cms_fuel_type', 0, 'default', '', '增程式电动', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (7, '氢燃料', 'hydrogen', 'cms_fuel_type', 0, 'danger', '', '氢燃料电池', '1', NOW(), '1', NOW(), b'0');

-- 驱动方式数据
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (1, '前驱', 'FWD', 'cms_drive_type', 0, 'primary', '', '前轮驱动', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (2, '后驱', 'RWD', 'cms_drive_type', 0, 'success', '', '后轮驱动', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (3, '四驱', 'AWD', 'cms_drive_type', 0, 'warning', '', '全轮驱动', '1', NOW(), '1', NOW(), b'0');

-- 变速箱类型数据
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (1, '手动', 'MT', 'cms_transmission_type', 0, 'default', '', '手动变速箱', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (2, '自动', 'AT', 'cms_transmission_type', 0, 'primary', '', '自动变速箱', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (3, '双离合', 'DCT', 'cms_transmission_type', 0, 'success', '', '双离合变速箱', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (4, '无级变速', 'CVT', 'cms_transmission_type', 0, 'warning', '', '无级变速箱', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (5, '电动车单速', 'EV', 'cms_transmission_type', 0, 'info', '', '电动车单速变速箱', '1', NOW(), '1', NOW(), b'0');

-- 配置选项类型数据
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (1, '外观颜色', 'exterior_color', 'cms_car_option_category', 0, 'primary', '', '车身外观颜色', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (2, '内饰颜色', 'interior_color', 'cms_car_option_category', 0, 'success', '', '内饰颜色材质', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (3, '轮毂', 'wheels', 'cms_car_option_category', 0, 'warning', '', '轮毂样式尺寸', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (4, '音响系统', 'audio', 'cms_car_option_category', 0, 'info', '', '音响系统配置', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (5, '座椅', 'seat', 'cms_car_option_category', 0, 'default', '', '座椅材质功能', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (6, '驾驶辅助', 'adas', 'cms_car_option_category', 0, 'danger', '', '驾驶辅助系统', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (7, '舒适配置', 'comfort', 'cms_car_option_category', 0, 'primary', '', '舒适性配置', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (8, '选装包', 'package', 'cms_car_option_category', 0, 'success', '', '选装配置包', '1', NOW(), '1', NOW(), b'0');

-- API版本数据
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (1, '默认版本', 'default', 'cms_api_version', 0, 'primary', '', '默认API版本', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (2, 'V1版本', 'v1', 'cms_api_version', 0, 'default', '', 'API V1版本', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (3, 'V2版本', 'v2', 'cms_api_version', 0, 'success', '', 'API V2版本', '1', NOW(), '1', NOW(), b'0');
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES (4, 'V3版本', 'v3', 'cms_api_version', 0, 'warning', '', 'API V3版本', '1', NOW(), '1', NOW(), b'0');

COMMIT;
SET FOREIGN_KEY_CHECKS = 1;