package cn.iocoder.yudao.module.cms.controller.admin.menu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - CMS菜单 Response VO")
@Data
public class CmsMenuRespVO {

    @Schema(description = "菜单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "菜单名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "首页")
    private String name;

    @Schema(description = "菜单图标", example = "home")
    private String icon;

    @Schema(description = "菜单路径", requiredMode = Schema.RequiredMode.REQUIRED, example = "home")
    private String path;

    @Schema(description = "完整路径", example = "/home/<USER>")
    private String fullPath;

    @Schema(description = "上级菜单ID", example = "0")
    private Long parentId;

    @Schema(description = "显示顺序", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer sort;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "子菜单列表")
    private List<CmsMenuRespVO> children;

}