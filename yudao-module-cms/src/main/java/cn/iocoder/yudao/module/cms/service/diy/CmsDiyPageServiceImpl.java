package cn.iocoder.yudao.module.cms.service.diy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.controller.admin.diy.vo.page.CmsDiyPageCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.diy.vo.page.CmsDiyPagePageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.diy.vo.page.CmsDiyPagePublishReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.diy.vo.page.CmsDiyPageUpdateReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageDO;
import cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageVersionDO;
import cn.iocoder.yudao.module.cms.dal.mysql.diy.CmsDiyPageMapper;
import cn.iocoder.yudao.module.cms.dal.mysql.diy.CmsDiyPageVersionMapper;
import cn.iocoder.yudao.module.cms.dal.mysql.statistics.CmsPageVisitLogMapper;
import cn.iocoder.yudao.module.cms.enums.diy.CmsDiyPageStatusEnum;
import cn.iocoder.yudao.module.cms.framework.cache.core.CmsCacheService;
import cn.iocoder.yudao.module.cms.service.menu.CmsMenuService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.OptimisticLockingFailureException;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.cms.enums.ErrorCodeConstants.*;

/**
 * CMS DIY页面 Service 实现类
 * 支持乐观锁、版本管理、发布流程
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class CmsDiyPageServiceImpl implements CmsDiyPageService {

    @Resource
    private CmsDiyPageMapper diyPageMapper;

    @Resource
    private CmsDiyPageVersionMapper diyPageVersionMapper;
    
    @Resource
    private CmsPageVisitLogMapper pageVisitLogMapper;

    @Resource
    private CmsMenuService menuService;

    @Resource
    private CmsCacheService cacheService;

    private static final String PATH_SEPARATOR = "/";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createDiyPage(@Valid CmsDiyPageCreateReqVO createReqVO) {
        validatePathUnique(null, createReqVO.getPath());
        
        String uuid = IdUtil.fastSimpleUUID();
        validateUuidUnique(null, uuid);
        
        if (createReqVO.getMenuId() != null) {
            menuService.validateMenuExists(createReqVO.getMenuId());
        }
        
        if (createReqVO.getParentId() != null) {
            validatePageExists(createReqVO.getParentId());
        }

        CmsDiyPageDO page = BeanUtils.toBean(createReqVO, CmsDiyPageDO.class);
        page.setUuid(uuid);
        page.setStatus(CmsDiyPageStatusEnum.DRAFT.getStatus());
        page.setVersion(1);
        page.setPublishedVersion(null);
        
        String fullPath = buildPageFullPath(createReqVO.getMenuId(), createReqVO.getParentId(), createReqVO.getPath());
        page.setPath(fullPath);
        
        diyPageMapper.insert(page);
        
        log.info("[createDiyPage][创建页面成功，页面ID：{}，UUID：{}]", page.getId(), page.getUuid());
        return page.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDiyPage(@Valid CmsDiyPageUpdateReqVO updateReqVO) {
        CmsDiyPageDO existingPage = validatePageExists(updateReqVO.getId());
        
        validatePathUnique(updateReqVO.getId(), updateReqVO.getPath());
        
        if (updateReqVO.getMenuId() != null && !updateReqVO.getMenuId().equals(existingPage.getMenuId())) {
            menuService.validateMenuExists(updateReqVO.getMenuId());
        }
        
        if (updateReqVO.getParentId() != null && !updateReqVO.getParentId().equals(existingPage.getParentId())) {
            validatePageExists(updateReqVO.getParentId());
            
            if (updateReqVO.getParentId().equals(updateReqVO.getId())) {
                throw exception(CMS_DIY_PAGE_PARENT_CANNOT_BE_SELF);
            }
            
            List<Long> childrenIds = getChildrenPageIds(updateReqVO.getId());
            if (childrenIds.contains(updateReqVO.getParentId())) {
                throw exception(CMS_DIY_PAGE_PARENT_CANNOT_BE_CHILD);
            }
        }

        CmsDiyPageDO updateObj = BeanUtils.toBean(updateReqVO, CmsDiyPageDO.class);
        
        String fullPath = buildPageFullPath(updateReqVO.getMenuId(), updateReqVO.getParentId(), updateReqVO.getPath());
        updateObj.setPath(fullPath);
        
        try {
            int result = diyPageMapper.updateByIdAndVersion(updateObj, updateReqVO.getVersion());
            if (result == 0) {
                throw exception(CMS_DIY_PAGE_VERSION_CONFLICT);
            }
        } catch (OptimisticLockingFailureException e) {
            throw exception(CMS_DIY_PAGE_VERSION_CONFLICT);
        }
        
        clearPageCache(updateReqVO.getId());
        
        log.info("[updateDiyPage][更新页面成功，页面ID：{}]", updateReqVO.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDiyPageContent(Long id, String content, Integer version) {
        validatePageExists(id);
        
        try {
            int result = diyPageMapper.updateContentByIdAndVersion(id, content, version);
            if (result == 0) {
                throw exception(CMS_DIY_PAGE_VERSION_CONFLICT);
            }
        } catch (OptimisticLockingFailureException e) {
            throw exception(CMS_DIY_PAGE_VERSION_CONFLICT);
        }
        
        clearPageCache(id);
        
        log.info("[updateDiyPageContent][更新页面内容成功，页面ID：{}]", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDiyPage(Long id, Integer version) {
        validatePageExists(id);
        validateCanDelete(id);
        
        try {
            int result = diyPageMapper.deleteByIdAndVersion(id, version);
            if (result == 0) {
                throw exception(CMS_DIY_PAGE_VERSION_CONFLICT);
            }
        } catch (OptimisticLockingFailureException e) {
            throw exception(CMS_DIY_PAGE_VERSION_CONFLICT);
        }
        
        diyPageVersionMapper.delete(CmsDiyPageVersionDO::getPageId, id);
        
        clearPageCache(id);
        
        log.info("[deleteDiyPage][删除页面成功，页面ID：{}]", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void publishDiyPage(@Valid CmsDiyPagePublishReqVO publishReqVO) {
        CmsDiyPageDO page = validatePageExists(publishReqVO.getId());
        
        if (CmsDiyPageStatusEnum.PUBLISHED.getStatus().equals(page.getStatus()) && 
            page.getPublishedVersion() != null && 
            page.getPublishedVersion().equals(page.getVersion())) {
            log.info("[publishDiyPage][页面已经是发布状态，无需重复发布，页面ID：{}]", publishReqVO.getId());
            return;
        }
        
        int nextVersion = page.getVersion() + 1;
        
        CmsDiyPageVersionDO versionDO = new CmsDiyPageVersionDO();
        versionDO.setPageId(page.getId());
        versionDO.setVersion(nextVersion);
        versionDO.setName(publishReqVO.getVersionName() != null ? publishReqVO.getVersionName() : 
                      "V" + nextVersion + " - " + LocalDateTime.now().toString());
        versionDO.setContent(page.getContent());
        versionDO.setPublishTime(LocalDateTime.now());
        versionDO.setIsPublished(true);
        versionDO.setRemark(publishReqVO.getRemark());
        diyPageVersionMapper.insert(versionDO);
        
        page.setStatus(CmsDiyPageStatusEnum.PUBLISHED.getStatus());
        page.setPublishedVersion(nextVersion);
        page.setVersion(nextVersion);
        
        try {
            int result = diyPageMapper.updateByIdAndVersion(page, publishReqVO.getVersion());
            if (result == 0) {
                throw exception(CMS_DIY_PAGE_VERSION_CONFLICT);
            }
        } catch (OptimisticLockingFailureException e) {
            throw exception(CMS_DIY_PAGE_VERSION_CONFLICT);
        }
        
        clearPageCache(page.getId());
        warmupPageForSinglePage(page.getId());
        
        log.info("[publishDiyPage][发布页面成功，页面ID：{}，版本号：{}]", page.getId(), nextVersion);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void offlineDiyPage(Long id, Integer version) {
        CmsDiyPageDO page = validatePageExists(id);
        
        if (!CmsDiyPageStatusEnum.PUBLISHED.getStatus().equals(page.getStatus())) {
            log.info("[offlineDiyPage][页面不是发布状态，无需下线，页面ID：{}]", id);
            return;
        }
        
        page.setStatus(CmsDiyPageStatusEnum.OFFLINE.getStatus());
        
        try {
            int result = diyPageMapper.updateByIdAndVersion(page, version);
            if (result == 0) {
                throw exception(CMS_DIY_PAGE_VERSION_CONFLICT);
            }
        } catch (OptimisticLockingFailureException e) {
            throw exception(CMS_DIY_PAGE_VERSION_CONFLICT);
        }
        
        clearPageCache(id);
        
        log.info("[offlineDiyPage][下线页面成功，页面ID：{}]", id);
    }

    @Override
    public CmsDiyPageDO getDiyPage(Long id) {
        return diyPageMapper.selectById(id);
    }

    @Override
    public CmsDiyPageDO getDiyPageByUuid(String uuid) {
        if (StrUtil.isBlank(uuid)) {
            return null;
        }
        return diyPageMapper.selectByUuid(uuid);
    }

    @Override
    public CmsDiyPageDO getDiyPageByPath(String path) {
        if (StrUtil.isBlank(path)) {
            return null;
        }
        
        return cacheService.getPageByPath("page:" + path, CmsDiyPageDO.class, () -> {
            return diyPageMapper.selectByPath(path);
        });
    }

    @Override
    public PageResult<CmsDiyPageDO> getDiyPagePage(CmsDiyPagePageReqVO pageReqVO) {
        return diyPageMapper.selectPage(pageReqVO);
    }

    @Override
    public List<CmsDiyPageDO> getDiyPageListByMenuId(Long menuId) {
        if (menuId == null) {
            return Collections.emptyList();
        }
        return diyPageMapper.selectListByMenuId(menuId);
    }

    @Override
    public List<CmsDiyPageDO> getDiyPageListByParentId(Long parentId) {
        if (parentId == null) {
            return Collections.emptyList();
        }
        return diyPageMapper.selectListByParentId(parentId);
    }

    @Override
    public String buildPageFullPath(Long menuId, Long parentId, String pagePath) {
        if (StrUtil.isBlank(pagePath)) {
            return "";
        }
        
        StringBuilder pathBuilder = new StringBuilder();
        
        if (menuId != null) {
            String menuPath = menuService.buildMenuPath(menuId);
            if (StrUtil.isNotBlank(menuPath)) {
                pathBuilder.append(menuPath);
            }
        }
        
        if (parentId != null) {
            CmsDiyPageDO parentPage = diyPageMapper.selectById(parentId);
            if (parentPage != null && StrUtil.isNotBlank(parentPage.getPath())) {
                if (pathBuilder.length() == 0 || !pathBuilder.toString().endsWith(PATH_SEPARATOR)) {
                    pathBuilder.append(PATH_SEPARATOR);
                }
                pathBuilder.append(parentPage.getPath());
            }
        }
        
        if (pathBuilder.length() == 0 || !pathBuilder.toString().endsWith(PATH_SEPARATOR)) {
            pathBuilder.append(PATH_SEPARATOR);
        }
        pathBuilder.append(pagePath);
        
        return pathBuilder.toString();
    }

    @Override
    public CmsDiyPageDO handleVersionConflict(Long id) {
        CmsDiyPageDO latestPage = diyPageMapper.selectById(id);
        if (latestPage == null) {
            throw exception(CMS_DIY_PAGE_NOT_EXISTS);
        }
        
        log.info("[handleVersionConflict][获取最新页面版本，页面ID：{}，最新版本：{}]", id, latestPage.getVersion());
        return latestPage;
    }

    @Override
    public void clearPageCache(Long id) {
        CmsDiyPageDO page = diyPageMapper.selectById(id);
        if (page != null) {
            // 清除页面内容缓存
            cacheService.evictPageCache(id);
            // 清除路径映射缓存
            cacheService.evict(CmsCacheService.CACHE_PAGE_PATH, page.getPath());
            // 清除UUID映射缓存
            cacheService.evict(CmsCacheService.CACHE_PAGE_PATH, "uuid:" + page.getUuid());
        }
        log.debug("[clearPageCache][清除页面缓存，页面ID：{}]", id);
    }

    @Override
    public void warmupPageCache() {
        try {
            List<CmsDiyPageDO> hotPages = diyPageMapper.selectHotPages(10);
            
            for (CmsDiyPageDO page : hotPages) {
                cacheService.getPageByPath("page:" + page.getPath(), CmsDiyPageDO.class, () -> page);
                cacheService.getPageByPath("page:uuid:" + page.getUuid(), CmsDiyPageDO.class, () -> page);
            }
            
            log.info("[warmupPageCache][页面缓存预热成功，预热页面数：{}]", hotPages.size());
        } catch (Exception e) {
            log.error("[warmupPageCache][页面缓存预热失败]", e);
        }
    }

    @Override
    public CmsDiyPageDO validatePageExists(Long id) {
        if (id == null) {
            throw exception(CMS_DIY_PAGE_NOT_EXISTS);
        }
        
        CmsDiyPageDO page = diyPageMapper.selectById(id);
        if (page == null) {
            throw exception(CMS_DIY_PAGE_NOT_EXISTS);
        }
        
        return page;
    }

    @Override
    public void validatePathUnique(Long id, String path) {
        if (StrUtil.isBlank(path)) {
            return;
        }
        
        CmsDiyPageDO existingPage = diyPageMapper.selectByPathAndIdNot(path, id);
        if (existingPage != null) {
            throw exception(CMS_DIY_PAGE_PATH_DUPLICATE);
        }
    }

    @Override
    public void validateUuidUnique(Long id, String uuid) {
        if (StrUtil.isBlank(uuid)) {
            return;
        }
        
        CmsDiyPageDO existingPage = diyPageMapper.selectByUuidAndIdNot(uuid, id);
        if (existingPage != null) {
            throw exception(CMS_DIY_PAGE_UUID_DUPLICATE);
        }
    }

    @Override
    public void validateCanDelete(Long id) {
        List<CmsDiyPageDO> children = diyPageMapper.selectListByParentId(id);
        if (CollUtil.isNotEmpty(children)) {
            throw exception(CMS_DIY_PAGE_EXISTS_CHILDREN);
        }
    }

    @Override
    public CmsDiyPageDO getPublishedPage(Long id) {
        CmsDiyPageDO page = getDiyPage(id);
        if (page == null || !CmsDiyPageStatusEnum.PUBLISHED.getStatus().equals(page.getStatus())) {
            return null;
        }
        return page;
    }

    @Override
    public CmsDiyPageDO getPublishedPageByUuid(String uuid) {
        if (StrUtil.isBlank(uuid)) {
            return null;
        }
        
        return cacheService.getPageByPath("page:uuid:" + uuid, CmsDiyPageDO.class, () -> {
            CmsDiyPageDO page = diyPageMapper.selectByUuid(uuid);
            if (page == null || !CmsDiyPageStatusEnum.PUBLISHED.getStatus().equals(page.getStatus())) {
                return null;
            }
            return page;
        });
    }

    @Override
    public CmsDiyPageDO getPublishedPageByPath(String path) {
        if (StrUtil.isBlank(path)) {
            return null;
        }
        
        return cacheService.getPageByPath("page:" + path, CmsDiyPageDO.class, () -> {
            CmsDiyPageDO page = diyPageMapper.selectByPath(path);
            if (page == null || !CmsDiyPageStatusEnum.PUBLISHED.getStatus().equals(page.getStatus())) {
                return null;
            }
            return page;
        });
    }

    @Override
    public List<CmsDiyPageDO> searchPublishedPages(String keyword) {
        if (StrUtil.isBlank(keyword)) {
            return Collections.emptyList();
        }
        
        return diyPageMapper.searchPublishedPages(keyword);
    }

    @Override
    @Async
    public void recordPageVisit(Long pageId, String visitorUuid) {
        try {
            log.debug("[recordPageVisit][记录页面访问，页面ID：{}，访客UUID：{}]", pageId, visitorUuid);
        } catch (Exception e) {
            log.error("[recordPageVisit][记录页面访问失败，页面ID：{}]", pageId, e);
        }
    }

    private List<Long> getChildrenPageIds(Long parentId) {
        if (parentId == null || parentId <= 0) {
            return Collections.emptyList();
        }
        
        return diyPageMapper.selectChildrenIds(parentId);
    }

    private void warmupPageForSinglePage(Long pageId) {
        try {
            CmsDiyPageDO page = diyPageMapper.selectById(pageId);
            if (page != null) {
                cacheService.getPageByPath("page:" + page.getPath(), CmsDiyPageDO.class, () -> page);
                cacheService.getPageByPath("page:uuid:" + page.getUuid(), CmsDiyPageDO.class, () -> page);
            }
        } catch (Exception e) {
            log.error("[warmupPageForSinglePage][单页面缓存预热失败，页面ID：{}]", pageId, e);
        }
    }
}