-- 车型配置管理系统索引优化脚本
-- 作者：开发团队
-- 日期：2025-01-09
-- 版本：v4.0
-- 说明：性能优化索引，提升查询效率

SET NAMES utf8mb4;

-- ----------------------------
-- 车系表索引优化
-- ----------------------------
-- 复合索引：支持按状态和排序查询
CREATE INDEX idx_car_series_status_sort ON cms_car_series(status, sort_order);
-- 覆盖索引：支持列表查询
CREATE INDEX idx_car_series_list ON cms_car_series(status, deleted, tenant_id, sort_order);

-- ----------------------------
-- 车型表索引优化
-- ----------------------------
-- 复合索引：支持按车系、分类、状态查询
CREATE INDEX idx_car_models_series_category_status ON cms_car_models(series_id, category, status);
-- 覆盖索引：支持价格范围查询
CREATE INDEX idx_car_models_price_range ON cms_car_models(status, base_price, deleted);
-- 复合索引：支持按日期查询优惠车型
CREATE INDEX idx_car_models_end_date ON cms_car_models(end_date, status) WHERE end_date IS NOT NULL;

-- ----------------------------
-- 车型规格表索引优化
-- ----------------------------
-- 复合索引：支持按发动机类型查询
CREATE INDEX idx_car_specs_engine_type ON cms_car_model_specs(engine_type, deleted);

-- ----------------------------
-- 配置包表索引优化
-- ----------------------------
-- 复合索引：支持价格查询
CREATE INDEX idx_car_packages_price ON cms_car_packages(model_id, price, status);

-- ----------------------------
-- 配置选项表索引优化
-- ----------------------------
-- 复合索引：支持按类型和价格查询
CREATE INDEX idx_car_options_type_price ON cms_car_model_options(option_type_id, price, status);
-- 复合索引：支持按配置包查询
CREATE INDEX idx_car_options_package ON cms_car_model_options(required_package, status) WHERE required_package IS NOT NULL;
-- 覆盖索引：支持选项列表查询
CREATE INDEX idx_car_options_list ON cms_car_model_options(model_id, option_type_id, status, deleted, sort_order);

-- ----------------------------
-- 融资方案表索引优化
-- ----------------------------
-- 复合索引：支持默认和推荐方案查询
CREATE INDEX idx_finance_plans_default ON cms_car_model_finance_plans(model_id, is_default, status);
CREATE INDEX idx_finance_plans_featured ON cms_car_model_finance_plans(model_id, is_featured, status);
-- 复合索引：支持价格范围查询
CREATE INDEX idx_finance_plans_payment ON cms_car_model_finance_plans(model_id, weekly_payment, status);

-- ----------------------------
-- API配置表索引优化
-- ----------------------------
-- 复合索引：支持版本查询
CREATE INDEX idx_api_configs_version_status ON cms_api_sql_configs(api_code, version, status);
-- 部分索引：只索引启用的配置
CREATE INDEX idx_api_configs_active ON cms_api_sql_configs(api_code, is_default) WHERE status = 0 AND deleted = 0;

-- ----------------------------
-- 展示配置表索引优化
-- ----------------------------
-- 复合索引：支持卡片查询
CREATE INDEX idx_car_cards_price_type ON cms_car_model_cards(price_type, status, sort_order);
-- 复合索引：支持按标签分组查询
CREATE INDEX idx_car_cards_tab_sort ON cms_car_model_cards(tab_id, status, sort_order);

-- ----------------------------
-- 多语言表索引优化
-- ----------------------------
-- 复合索引：支持语言查询
CREATE INDEX idx_translations_lang ON cms_translations(language_code, table_name, record_id);
-- 覆盖索引：支持批量翻译查询
CREATE INDEX idx_translations_batch ON cms_translations(table_name, record_id, language_code, field_name);

-- ----------------------------
-- 统计信息更新
-- ----------------------------
ANALYZE TABLE cms_car_series;
ANALYZE TABLE cms_car_models;
ANALYZE TABLE cms_car_model_specs;
ANALYZE TABLE cms_car_packages;
ANALYZE TABLE cms_car_option_types;
ANALYZE TABLE cms_car_model_options;
ANALYZE TABLE cms_finance_options;
ANALYZE TABLE cms_finance_terms;
ANALYZE TABLE cms_down_payment_options;
ANALYZE TABLE cms_car_model_finance_plans;
ANALYZE TABLE cms_api_sql_configs;
ANALYZE TABLE cms_api_param_configs;
ANALYZE TABLE cms_car_model_tabs;
ANALYZE TABLE cms_car_model_cards;
ANALYZE TABLE cms_translations;

-- ----------------------------
-- 查询优化建议
-- ----------------------------
-- 1. 车型列表查询优化
-- SELECT m.*, s.name as series_name 
-- FROM cms_car_models m
-- INNER JOIN cms_car_series s ON m.series_id = s.id
-- WHERE m.status = 0 AND m.deleted = 0
-- ORDER BY m.sort_order;

-- 2. 配置选项查询优化
-- SELECT o.*, t.name as type_name, t.config_schema
-- FROM cms_car_model_options o
-- INNER JOIN cms_car_option_types t ON o.option_type_id = t.id
-- WHERE o.model_id = ? AND o.status = 0
-- ORDER BY t.sort_order, o.sort_order;

-- 3. 融资方案查询优化
-- SELECT fp.*, fo.name as option_name, ft.name as term_name
-- FROM cms_car_model_finance_plans fp
-- INNER JOIN cms_finance_options fo ON fp.finance_option_id = fo.id
-- LEFT JOIN cms_finance_terms ft ON fp.term_id = ft.id
-- WHERE fp.model_id = ? AND fp.status = 0
-- ORDER BY fp.is_default DESC, fp.is_featured DESC, fp.sort_order;

-- ----------------------------
-- 性能监控视图
-- ----------------------------
CREATE OR REPLACE VIEW v_car_model_stats AS
SELECT 
    s.id as series_id,
    s.name as series_name,
    COUNT(DISTINCT m.id) as model_count,
    COUNT(DISTINCT p.id) as package_count,
    COUNT(DISTINCT o.id) as option_count,
    AVG(m.base_price) as avg_price,
    MIN(m.base_price) as min_price,
    MAX(m.base_price) as max_price
FROM cms_car_series s
LEFT JOIN cms_car_models m ON s.id = m.series_id AND m.deleted = 0
LEFT JOIN cms_car_packages p ON m.id = p.model_id AND p.deleted = 0
LEFT JOIN cms_car_model_options o ON m.id = o.model_id AND o.deleted = 0
WHERE s.deleted = 0
GROUP BY s.id, s.name;

-- ----------------------------
-- 索引维护建议
-- ----------------------------
-- 定期执行以下命令优化索引：
-- OPTIMIZE TABLE cms_car_models;
-- OPTIMIZE TABLE cms_car_model_options;
-- OPTIMIZE TABLE cms_car_model_finance_plans;

-- 监控索引使用情况：
-- SELECT 
--     table_name,
--     index_name,
--     cardinality,
--     seq_in_index,
--     column_name
-- FROM information_schema.statistics
-- WHERE table_schema = DATABASE()
--     AND table_name LIKE 'cms_car%'
-- ORDER BY table_name, index_name, seq_in_index;