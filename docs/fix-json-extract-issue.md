# API SQL配置JSON_EXTRACT问题修复指南

## 问题描述

在测试API SQL配置时，出现以下错误：
```
java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax
```

具体问题：
1. JSON_EXTRACT函数的路径参数被截断
   - 错误形式：`JSON_EXTRACT(m.features, ')`
   - 正确形式：`JSON_EXTRACT(m.features, '$')`

2. 参数占位符格式问题
   - MyBatis格式：`#{code}`
   - Spring NamedParameterJdbcTemplate格式：`:code`

## 问题原因

1. **字符串转义问题**：在SQL存储到数据库或从前端传输时，单引号内的内容被错误截断
2. **编码问题**：可能存在字符编码导致的解析错误
3. **前端处理问题**：前端在发送SQL内容时可能错误处理了字符串

## 修复方案

### 方案1：执行数据库修复脚本（推荐）

运行以下SQL脚本修复数据库中的配置：

```bash
mysql -u root -p your_database < sql/mysql/fix_api_sql_complete.sql
```

该脚本会：
- 修复所有JSON_EXTRACT的路径参数
- 确保参数占位符格式正确
- 验证修复结果

### 方案2：通过管理后台手动修复

1. 登录管理后台
2. 进入"API SQL配置"管理页面
3. 编辑受影响的配置（car_configurator等）
4. 确保所有JSON_EXTRACT函数的格式为：
   ```sql
   JSON_EXTRACT(field_name, '$')
   ```
5. 保存配置

### 方案3：重新导入正确的配置

```bash
# 清理错误配置
mysql -u root -p your_database < sql/mysql/fix_api_sql_syntax.sql

# 重新导入正确配置
mysql -u root -p your_database < sql/mysql/cms_api_configurations.sql
```

## 验证修复

### 1. 数据库验证
```sql
-- 检查JSON_EXTRACT语法是否正确
SELECT api_code, version,
       CASE 
           WHEN sql_content LIKE '%JSON_EXTRACT%''$''%' THEN '✅ 正确'
           WHEN sql_content LIKE '%JSON_EXTRACT%'')%' THEN '❌ 错误'
           ELSE '⚠️ 未知'
       END as status
FROM cms_api_sql_configs
WHERE sql_content LIKE '%JSON_EXTRACT%';
```

### 2. API测试
通过管理后台的"测试SQL"功能测试修复后的配置：
- 测试参数：`{"code": "test_model_code"}`
- 期望结果：返回JSON格式的数据，无SQL语法错误

## 预防措施

### 1. SQL配置最佳实践
- 在SQL中使用双单引号（`''`）来表示单引号
- JSON_EXTRACT路径始终使用`'$'`格式
- 参数占位符使用`#{paramName}`格式

### 2. 代码层面优化
- 在`SqlExecutionEngine.java`中添加JSON_EXTRACT路径验证
- 在保存SQL配置前进行语法检查
- 添加单元测试覆盖JSON函数处理

### 3. 前端优化建议
- 使用专门的SQL编辑器组件
- 避免对SQL内容进行不必要的转义
- 在发送前验证JSON_EXTRACT语法

## 相关文件

- `/sql/mysql/fix_api_sql_complete.sql` - 完整修复脚本
- `/sql/mysql/fix_json_extract_syntax.sql` - JSON_EXTRACT专项修复
- `/sql/mysql/cms_api_configurations.sql` - 正确的配置模板
- `/yudao-module-cms/src/main/java/cn/iocoder/yudao/module/cms/service/car/api/SqlExecutionEngine.java` - SQL执行引擎

## 联系支持

如果问题持续存在，请：
1. 检查MySQL版本（需要5.7+以支持JSON函数）
2. 确认字符集设置（应使用utf8mb4）
3. 查看完整错误日志
4. 联系开发团队获取支持

## 更新记录

- 2025-01-10：创建修复指南
- 2025-01-10：添加fix_api_sql_complete.sql脚本
- 2025-01-10：更新验证步骤