package cn.iocoder.yudao.module.cms.service.car.finance;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.FinanceTermCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.FinanceTermPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.FinanceTermUpdateReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.finance.FinanceTermDO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 融资期限 Service 接口
 *
 * <AUTHOR>
 */
public interface FinanceTermService {

    /**
     * 创建融资期限
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createFinanceTerm(@Valid FinanceTermCreateReqVO createReqVO);

    /**
     * 更新融资期限
     *
     * @param updateReqVO 更新信息
     */
    void updateFinanceTerm(@Valid FinanceTermUpdateReqVO updateReqVO);

    /**
     * 删除融资期限
     *
     * @param id 编号
     */
    void deleteFinanceTerm(Long id);

    /**
     * 获得融资期限
     *
     * @param id 编号
     * @return 融资期限
     */
    FinanceTermDO getFinanceTerm(Long id);

    /**
     * 获得融资期限分页
     *
     * @param pageReqVO 分页查询
     * @return 融资期限分页
     */
    PageResult<FinanceTermDO> getFinanceTermPage(FinanceTermPageReqVO pageReqVO);

    /**
     * 获得融资期限列表
     *
     * @param status 状态
     * @return 融资期限列表
     */
    List<FinanceTermDO> getFinanceTermList(Integer status);

    /**
     * 根据融资选项ID获得期限列表
     *
     * @param optionId 融资选项ID
     * @return 期限列表
     */
    List<FinanceTermDO> getFinanceTermListByOptionId(Long optionId);

    /**
     * 根据代码获取融资期限
     *
     * @param termCode 期限代码
     * @return 融资期限信息
     */
    FinanceTermDO getFinanceTermByCode(String termCode);

}