package cn.iocoder.yudao.module.cms.service.statistics;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.statistics.vo.CmsPageVisitLogPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.statistics.vo.CmsPageVisitStatsPageReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.statistics.CmsPageVisitLogDO;
import cn.iocoder.yudao.module.cms.dal.dataobject.statistics.CmsPageVisitStatsDO;

import java.time.LocalDate;
import java.util.List;

/**
 * CMS页面访问统计 Service 接口
 *
 * <AUTHOR>
 */
public interface CmsPageVisitService {

    /**
     * 记录页面访问日志
     * 异步处理，不阻塞主流程
     *
     * @param pageId 页面ID
     * @param pageUuid 页面UUID
     * @param userId 用户ID（可为空）
     * @param sessionId 会话ID
     * @param ip 访问IP
     * @param userAgent 用户代理
     * @param referer 来源页面
     */
    void recordPageVisit(Long pageId, String pageUuid, Long userId, String sessionId, 
                        String ip, String userAgent, String referer);

    /**
     * 批量插入访问日志
     * 用于批量处理提高性能
     *
     * @param visitLogs 访问日志列表
     */
    void batchInsertVisitLogs(List<CmsPageVisitLogDO> visitLogs);

    /**
     * 获得访问日志分页
     *
     * @param pageReqVO 分页查询
     * @return 访问日志分页
     */
    PageResult<CmsPageVisitLogDO> getVisitLogPage(CmsPageVisitLogPageReqVO pageReqVO);

    /**
     * 获得页面访问统计分页
     *
     * @param pageReqVO 分页查询
     * @return 访问统计分页
     */
    PageResult<CmsPageVisitStatsDO> getVisitStatsPage(CmsPageVisitStatsPageReqVO pageReqVO);

    /**
     * 获得页面的访问统计
     *
     * @param pageId 页面ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 访问统计列表
     */
    List<CmsPageVisitStatsDO> getPageVisitStats(Long pageId, LocalDate startDate, LocalDate endDate);

    /**
     * 获得热门页面
     * 根据最近访问量排序
     *
     * @param days 统计天数
     * @param limit 返回数量限制
     * @return 热门页面统计
     */
    List<CmsPageVisitStatsDO> getHotPages(Integer days, Integer limit);

    /**
     * 聚合每日访问统计
     * 定时任务调用，将访问日志聚合为统计数据
     *
     * @param statDate 统计日期
     * @return 聚合的统计记录数
     */
    Integer aggregateDailyStats(LocalDate statDate);

    /**
     * 批量聚合访问统计
     * 处理多天的统计聚合
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 总聚合记录数
     */
    Integer batchAggregateStats(LocalDate startDate, LocalDate endDate);

    /**
     * 清理旧的访问日志
     * 删除超过指定天数的原始访问日志
     *
     * @param keepDays 保留天数
     * @return 清理的记录数
     */
    Integer cleanupOldVisitLogs(Integer keepDays);

    /**
     * 获得页面今日访问量
     *
     * @param pageId 页面ID
     * @return 今日访问量
     */
    Integer getTodayVisitCount(Long pageId);

    /**
     * 获得页面总访问量
     *
     * @param pageId 页面ID
     * @return 总访问量
     */
    Long getTotalVisitCount(Long pageId);

    /**
     * 获得页面访问趋势
     * 返回最近N天的访问量数据
     *
     * @param pageId 页面ID
     * @param days 天数
     * @return 访问趋势数据 [日期, 访问量] 的键值对
     */
    List<Object[]> getPageVisitTrend(Long pageId, Integer days);

    /**
     * 获得实时访问统计
     * 返回当前在线访客数等实时数据
     *
     * @return 实时统计数据
     */
    Object getRealTimeStats();
}