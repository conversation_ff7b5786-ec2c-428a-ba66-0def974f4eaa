package cn.iocoder.yudao.module.cms.controller.admin.diy.vo.page;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;


/**
 * CMS DIY页面发布 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - CMS DIY页面发布 Request VO")
@Data
public class CmsDiyPagePublishReqVO {

    @Schema(description = "页面ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "页面ID不能为空")
    private Long id;

    @Schema(description = "当前版本号（用于乐观锁）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "版本号不能为空")
    private Integer version;

    @Schema(description = "版本名称", example = "双十一活动版本")
    @Size(max = 100, message = "版本名称长度不能超过100个字符")
    private String versionName;

    @Schema(description = "发布备注", example = "修复了首页轮播图问题")
    @Size(max = 500, message = "发布备注长度不能超过500个字符")
    private String remark;
}