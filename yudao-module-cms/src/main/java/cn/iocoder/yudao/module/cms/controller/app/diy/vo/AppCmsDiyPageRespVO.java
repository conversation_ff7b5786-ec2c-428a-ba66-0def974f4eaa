package cn.iocoder.yudao.module.cms.controller.app.diy.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * App端 - CMS DIY页面 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "App端 - CMS DIY页面 Response VO")
@Data
public class AppCmsDiyPageRespVO {

    @Schema(description = "页面ID", example = "1024")
    private Long id;

    @Schema(description = "页面UUID", example = "550e8400-e29b-41d4-a716-************")
    private String uuid;

    @Schema(description = "页面名称", example = "首页")
    private String name;

    @Schema(description = "页面路径", example = "/home/<USER>")
    private String path;

    @Schema(description = "关键词", example = "首页,主页")
    private String keywords;

    @Schema(description = "页面描述", example = "这是网站首页")
    private String description;

    @Schema(description = "页面内容JSON")
    private String content;

    @Schema(description = "发布时间")
    private LocalDateTime publishTime;
}