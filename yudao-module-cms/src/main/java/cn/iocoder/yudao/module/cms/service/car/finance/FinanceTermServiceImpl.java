package cn.iocoder.yudao.module.cms.service.car.finance;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.FinanceTermCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.FinanceTermPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.FinanceTermUpdateReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.finance.FinanceTermDO;
import cn.iocoder.yudao.module.cms.dal.mysql.car.finance.FinanceTermMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.cms.enums.ErrorCodeConstants.*;

/**
 * 融资期限 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FinanceTermServiceImpl implements FinanceTermService {

    @Resource
    private FinanceTermMapper financeTermMapper;

    @Resource
    private FinanceOptionService financeOptionService;

    @Override
    public Long createFinanceTerm(FinanceTermCreateReqVO createReqVO) {
        // 验证融资选项存在
        validateFinanceOptionExists(createReqVO.getOptionId());
        
        // 校验代码唯一性
        validateTermCodeUnique(null, createReqVO.getTermCode());

        // 插入
        FinanceTermDO financeTerm = BeanUtils.toBean(createReqVO, FinanceTermDO.class);
        financeTermMapper.insert(financeTerm);
        // 返回
        return financeTerm.getId();
    }

    @Override
    public void updateFinanceTerm(FinanceTermUpdateReqVO updateReqVO) {
        // 校验存在
        validateFinanceTermExists(updateReqVO.getId());
        
        // 验证融资选项存在
        validateFinanceOptionExists(updateReqVO.getOptionId());
        
        // 校验代码唯一性
        validateTermCodeUnique(updateReqVO.getId(), updateReqVO.getTermCode());

        // 更新
        FinanceTermDO updateObj = BeanUtils.toBean(updateReqVO, FinanceTermDO.class);
        financeTermMapper.updateById(updateObj);
    }

    @Override
    public void deleteFinanceTerm(Long id) {
        // 校验存在
        validateFinanceTermExists(id);
        // 删除
        financeTermMapper.deleteById(id);
    }

    @Override
    public FinanceTermDO getFinanceTerm(Long id) {
        return financeTermMapper.selectById(id);
    }

    @Override
    public PageResult<FinanceTermDO> getFinanceTermPage(FinanceTermPageReqVO pageReqVO) {
        return financeTermMapper.selectPage(pageReqVO.getOptionId(), pageReqVO.getName(), pageReqVO.getStatus(),
                pageReqVO.getPageNo(), pageReqVO.getPageSize());
    }

    @Override
    public List<FinanceTermDO> getFinanceTermList(Integer status) {
        return financeTermMapper.selectListByStatus(status);
    }

    @Override
    public List<FinanceTermDO> getFinanceTermListByOptionId(Long optionId) {
        return financeTermMapper.selectListByOptionId(optionId);
    }

    @Override
    public FinanceTermDO getFinanceTermByCode(String termCode) {
        return financeTermMapper.selectByCode(termCode);
    }

    private void validateFinanceTermExists(Long id) {
        if (financeTermMapper.selectById(id) == null) {
            throw exception(FINANCE_TERM_NOT_EXISTS);
        }
    }

    private void validateFinanceOptionExists(Long optionId) {
        if (financeOptionService.getFinanceOption(optionId) == null) {
            throw exception(FINANCE_OPTION_NOT_EXISTS);
        }
    }

    private void validateTermCodeUnique(Long id, String termCode) {
        FinanceTermDO financeTerm = financeTermMapper.selectByCode(termCode);
        if (financeTerm == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的融资期限
        if (id == null) {
            throw exception(FINANCE_TERM_CODE_DUPLICATE);
        }
        if (!financeTerm.getId().equals(id)) {
            throw exception(FINANCE_TERM_CODE_DUPLICATE);
        }
    }

}