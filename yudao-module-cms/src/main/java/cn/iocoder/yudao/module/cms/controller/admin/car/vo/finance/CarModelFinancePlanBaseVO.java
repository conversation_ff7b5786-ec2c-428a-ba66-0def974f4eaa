package cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import java.math.BigDecimal;

/**
 * 车型融资方案 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class CarModelFinancePlanBaseVO {

    @Schema(description = "车型ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "车型ID不能为空")
    private Long modelId;

    @Schema(description = "融资选项ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "融资选项ID不能为空")
    private Long financeOptionId;

    @Schema(description = "期限ID", example = "1024")
    private Long termId;

    @Schema(description = "首付选项ID", example = "1024")
    private Long downPaymentId;

    @Schema(description = "每周付款金额", example = "158.00")
    @DecimalMin(value = "0", message = "每周付款金额不能小于0")
    private BigDecimal weeklyPayment;

    @Schema(description = "每月付款金额", example = "688.00")
    @DecimalMin(value = "0", message = "每月付款金额不能小于0")
    private BigDecimal monthlyPayment;

    @Schema(description = "总金额", example = "28888.00")
    @DecimalMin(value = "0", message = "总金额不能小于0")
    private BigDecimal totalAmount;

    @Schema(description = "保证未来价值", example = "12000.00")
    @DecimalMin(value = "0", message = "保证未来价值不能小于0")
    private BigDecimal gfv;

    @Schema(description = "公里数限制", example = "15000")
    @Min(value = 0, message = "公里数限制不能小于0")
    private Integer kmAllowance;

    @Schema(description = "是否默认方案", example = "false")
    private Boolean isDefault;

    @Schema(description = "是否推荐方案", example = "true")
    private Boolean isFeatured;

    @Schema(description = "排序", example = "1")
    private Integer sortOrder;

    @Schema(description = "状态：0-禁用，1-启用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

}