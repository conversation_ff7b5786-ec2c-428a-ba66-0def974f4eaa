package cn.iocoder.yudao.module.cms.convert.car;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.api.ApiSqlConfigCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.api.ApiSqlConfigRespVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.api.ApiSqlConfigUpdateReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.api.ApiSqlConfigDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * API SQL配置 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ApiSqlConfigConvert {

    ApiSqlConfigConvert INSTANCE = Mappers.getMapper(ApiSqlConfigConvert.class);

    ApiSqlConfigDO convert(ApiSqlConfigCreateReqVO bean);

    ApiSqlConfigDO convert(ApiSqlConfigUpdateReqVO bean);

    ApiSqlConfigRespVO convert(ApiSqlConfigDO bean);

    List<ApiSqlConfigRespVO> convertList(List<ApiSqlConfigDO> list);

    PageResult<ApiSqlConfigRespVO> convertPage(PageResult<ApiSqlConfigDO> page);
}