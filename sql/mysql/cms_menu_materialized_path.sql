-- 给菜单表添加物化路径列
ALTER TABLE cms_menu ADD COLUMN materialized_path VARCHAR(2000) DEFAULT '' COMMENT '物化路径，完整路径用/分隔，如: /home/<USER>/cars';

-- 添加索引以优化路径查询
CREATE INDEX idx_cms_menu_materialized_path ON cms_menu(materialized_path);

-- 初始化已有数据的物化路径（使用递归CTE构建）
WITH RECURSIVE menu_hierarchy AS (
    -- 根菜单
    SELECT id, parent_id, path, path as materialized_path, 0 as level
    FROM cms_menu 
    WHERE (parent_id IS NULL OR parent_id = 0) AND deleted = 0
    
    UNION ALL
    
    -- 子菜单
    SELECT m.id, m.parent_id, m.path, 
           CONCAT(mh.materialized_path, '/', m.path) as materialized_path,
           mh.level + 1
    FROM cms_menu m
    INNER JOIN menu_hierarchy mh ON m.parent_id = mh.id
    WHERE m.deleted = 0 AND mh.level < 10
)
UPDATE cms_menu 
SET materialized_path = (
    SELECT mh.materialized_path 
    FROM menu_hierarchy mh 
    WHERE mh.id = cms_menu.id
)
WHERE deleted = 0;

-- 对于没有匹配到的记录（可能是孤立数据），设置为其自身的path
UPDATE cms_menu 
SET materialized_path = path 
WHERE materialized_path IS NULL OR materialized_path = '';

-- 创建存储过程来重新计算单个菜单的物化路径
DELIMITER $$
CREATE PROCEDURE RecalculateMenuPath(IN menu_id BIGINT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE current_id BIGINT DEFAULT menu_id;
    DECLARE parent_path VARCHAR(2000) DEFAULT '';
    DECLARE current_path VARCHAR(500);
    DECLARE full_path VARCHAR(2000) DEFAULT '';
    DECLARE path_parts TEXT DEFAULT '';
    
    -- 从当前菜单向上遍历到根菜单
    path_loop: LOOP
        SELECT path, parent_id INTO current_path, current_id
        FROM cms_menu 
        WHERE id = current_id AND deleted = 0;
        
        IF current_path IS NULL THEN
            LEAVE path_loop;
        END IF;
        
        -- 将当前路径添加到路径部分的前面
        IF path_parts = '' THEN
            SET path_parts = current_path;
        ELSE
            SET path_parts = CONCAT(current_path, '/', path_parts);
        END IF;
        
        -- 如果是根菜单（parent_id为空或0），退出循环
        IF current_id IS NULL OR current_id = 0 THEN
            LEAVE path_loop;
        END IF;
    END LOOP;
    
    -- 更新物化路径
    SET full_path = CONCAT('/', path_parts);
    UPDATE cms_menu SET materialized_path = full_path WHERE id = menu_id;
END$$

-- 创建存储过程来重新计算某个菜单下所有子菜单的物化路径
DELIMITER $$
CREATE PROCEDURE RecalculateChildrenPaths(IN parent_menu_id BIGINT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE child_id BIGINT;
    DECLARE child_cursor CURSOR FOR 
        WITH RECURSIVE children AS (
            SELECT id FROM cms_menu WHERE parent_id = parent_menu_id AND deleted = 0
            UNION ALL
            SELECT m.id FROM cms_menu m 
            INNER JOIN children c ON m.parent_id = c.id 
            WHERE m.deleted = 0
        )
        SELECT id FROM children;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN child_cursor;
    
    read_loop: LOOP
        FETCH child_cursor INTO child_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        CALL RecalculateMenuPath(child_id);
    END LOOP;
    
    CLOSE child_cursor;
END$$

DELIMITER ;