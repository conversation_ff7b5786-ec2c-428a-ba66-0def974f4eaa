package cn.iocoder.yudao.module.cms.framework.validation.annotation;

import cn.iocoder.yudao.module.cms.framework.validation.validator.JsonContentValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * JSON 内容验证注解
 *
 * <AUTHOR>
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = JsonContentValidator.class)
public @interface ValidJsonContent {

    String message() default "JSON 内容格式不正确或超过大小限制";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    /**
     * 最大内容长度（字节）
     */
    int maxLength() default 5 * 1024 * 1024; // 5MB

    /**
     * 是否进行 XSS 清理
     */
    boolean sanitize() default true;
}