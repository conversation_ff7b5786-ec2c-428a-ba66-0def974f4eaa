package cn.iocoder.yudao.module.cms.dal.mysql.car.finance;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.finance.DownPaymentOptionDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 首付选项 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DownPaymentOptionMapper extends BaseMapperX<DownPaymentOptionDO> {

    default PageResult<DownPaymentOptionDO> selectPage(Long optionId, String name, Integer status,
                                                        Integer pageNo, Integer pageSize) {
        return selectPage(new PageParam().setPageNo(pageNo).setPageSize(pageSize),new LambdaQueryWrapperX<DownPaymentOptionDO>()
                .eqIfPresent(DownPaymentOptionDO::getOptionId, optionId)
                .likeIfPresent(DownPaymentOptionDO::getName, name)
                .eqIfPresent(DownPaymentOptionDO::getStatus, status)
                .orderByAsc(DownPaymentOptionDO::getSortOrder)
                .orderByDesc(DownPaymentOptionDO::getId));
    }

    default DownPaymentOptionDO selectByCode(String paymentCode) {
        return selectOne(DownPaymentOptionDO::getPaymentCode, paymentCode);
    }

    default List<DownPaymentOptionDO> selectListByOptionId(Long optionId) {
        return selectList(new LambdaQueryWrapperX<DownPaymentOptionDO>()
                .eq(DownPaymentOptionDO::getOptionId, optionId)
                .eq(DownPaymentOptionDO::getStatus, 0)
                .orderByAsc(DownPaymentOptionDO::getSortOrder));
    }

    default List<DownPaymentOptionDO> selectListByStatus(Integer status) {
        return selectList(new LambdaQueryWrapperX<DownPaymentOptionDO>()
                .eqIfPresent(DownPaymentOptionDO::getStatus, status)
                .orderByAsc(DownPaymentOptionDO::getSortOrder));
    }

}