package cn.iocoder.yudao.module.cms.service.car;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarSeriesCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarSeriesPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarSeriesUpdateReqVO;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarSeriesDO;
import cn.iocoder.yudao.module.cms.dal.mysql.car.CarSeriesMapper;
import cn.iocoder.yudao.module.cms.framework.car.constant.CarCacheConstants;
import cn.iocoder.yudao.module.cms.framework.car.util.CarCacheHelper;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.cms.enums.ErrorCodeConstants.CAR_SERIES_NOT_EXISTS;

/**
 * 车系管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CarSeriesServiceImpl implements CarSeriesService {

    @Resource
    private CarSeriesMapper carSeriesMapper;

    @Override
    @CacheEvict(cacheNames = CarCacheConstants.CAR_SERIES_LIST, allEntries = true)
    public Long createCarSeries(CarSeriesCreateReqVO createReqVO) {
        // 插入
        CarSeriesDO carSeries = BeanUtils.toBean(createReqVO, CarSeriesDO.class);
        carSeriesMapper.insert(carSeries);
        // 返回
        return carSeries.getId();
    }

    @Override
    @Caching(evict = {
        @CacheEvict(cacheNames = CarCacheConstants.CAR_SERIES, key = "#updateReqVO.id"),
        @CacheEvict(cacheNames = CarCacheConstants.CAR_SERIES_LIST, allEntries = true)
    })
    public void updateCarSeries(CarSeriesUpdateReqVO updateReqVO) {
        // 校验存在
        validateCarSeriesExists(updateReqVO.getId());

        // 更新
        CarSeriesDO updateObj = BeanUtils.toBean(updateReqVO, CarSeriesDO.class);
        carSeriesMapper.updateById(updateObj);
    }

    @Override
    @Caching(evict = {
        @CacheEvict(cacheNames = CarCacheConstants.CAR_SERIES, key = "#id"),
        @CacheEvict(cacheNames = CarCacheConstants.CAR_SERIES_LIST, allEntries = true)
    })
    public void deleteCarSeries(Long id) {
        // 校验存在
        validateCarSeriesExists(id);
        // 删除
        carSeriesMapper.deleteById(id);
    }

    private void validateCarSeriesExists(Long id) {
        if (carSeriesMapper.selectById(id) == null) {
            throw exception(CAR_SERIES_NOT_EXISTS);
        }
    }

    @Override
    @Cacheable(cacheNames = CarCacheConstants.CAR_SERIES, key = "#id", unless = "#result == null")
    public CarSeriesDO getCarSeries(Long id) {
        return carSeriesMapper.selectById(id);
    }

    @Override
    public PageResult<CarSeriesDO> getCarSeriesPage(CarSeriesPageReqVO pageReqVO) {
        return carSeriesMapper.selectPage(pageReqVO);
    }

    @Override
    @Cacheable(cacheNames = CarCacheConstants.CAR_SERIES_LIST,
              key = "T(cn.iocoder.yudao.module.cms.framework.car.util.CarCacheHelper).generateKey(#brand, #status)",
              unless = "#result == null || #result.isEmpty()")
    public List<CarSeriesDO> getCarSeriesList(String brand, Integer status) {
        return carSeriesMapper.selectListByBrandAndStatus(brand, status);
    }

    @Override
    @Cacheable(cacheNames = CarCacheConstants.CAR_SERIES, key = "'code:' + #code", unless = "#result == null")
    public CarSeriesDO getCarSeriesByCode(String code) {
        return carSeriesMapper.selectByCode(code);
    }

}