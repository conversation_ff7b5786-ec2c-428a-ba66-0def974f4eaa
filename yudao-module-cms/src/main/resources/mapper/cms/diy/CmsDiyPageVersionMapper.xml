<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.cms.dal.mysql.diy.CmsDiyPageVersionMapper">

    <!-- 查询页面最大版本号 -->
    <select id="selectMaxVersionByPageId" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(version), 0)
        FROM cms_diy_page_version
        WHERE page_id = #{pageId} AND deleted = 0
    </select>

    <!-- 删除旧版本（保留最新的N个版本） -->
    <update id="deleteOldVersions">
        UPDATE cms_diy_page_version SET deleted = 1, update_time = NOW()
        WHERE page_id = #{pageId}
        AND id NOT IN (
            SELECT id FROM (
                SELECT id FROM cms_diy_page_version
                WHERE page_id = #{pageId} AND deleted = 0
                ORDER BY version DESC
                LIMIT #{keepCount}
            ) temp
        )
        AND deleted = 0
    </update>

    <!-- 批量更新版本发布状态 -->
    <update id="updatePublishStatusByPageId">
        UPDATE cms_diy_page_version
        SET is_published = #{isPublished}, update_time = NOW()
        WHERE page_id = #{pageId} AND deleted = 0
    </update>

</mapper>