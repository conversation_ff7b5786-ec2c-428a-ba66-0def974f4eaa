package cn.iocoder.yudao.module.cms.framework.security.annotation;

import java.lang.annotation.*;

/**
 * CMS 审计日志注解
 * 用于标记需要记录审计日志的方法
 *
 * <AUTHOR>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CmsAuditLog {

    /**
     * 操作描述
     */
    String value() default "";

    /**
     * 操作类型
     */
    OperationType type() default OperationType.OTHER;

    /**
     * 操作类型枚举
     */
    enum OperationType {
        CREATE("创建"),
        UPDATE("更新"),
        DELETE("删除"),
        PUBLISH("发布"),
        OFFLINE("下线"),
        ROLLBACK("回滚"),
        OTHER("其他");

        private final String description;

        OperationType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}