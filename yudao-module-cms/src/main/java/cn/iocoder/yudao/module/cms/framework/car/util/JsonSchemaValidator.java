//package cn.iocoder.yudao.module.cms.framework.car.util;
//
//import com.fasterxml.jackson.databind.JsonNode;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import jakarta.annotation.PostConstruct;
//import lombok.Data;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.cache.annotation.Cacheable;
//import org.springframework.stereotype.Component;
//
//import java.io.IOException;
//import java.util.*;
//import java.util.Collections;
//
///**
// * JSON Schema验证工具
// * 支持动态配置选项数据验证
// *
// * <AUTHOR>
// */
//@Slf4j
//@Component
//@ConfigurationProperties(prefix = "cms.car.json-schema")
//public class JsonSchemaValidator {
//
//    @Autowired
//    private ObjectMapper objectMapper;
//
//    /**
//     * 配置属性
//     */
//    private boolean strictMode = true;
//    private boolean allowAdditionalProperties = false;
//    private boolean verboseErrors = true;
//
//    private CacheConfig cache = new CacheConfig();
//
//    @PostConstruct
//    public void init() {
//        log.info("JSON Schema验证器初始化完成 - 严格模式: {}, 允许额外属性: {}, 详细错误: {}",
//                strictMode, allowAdditionalProperties, verboseErrors);
//    }
//
//    /**
//     * 验证JSON数据
//     * 使用简化的验证逻辑，基于JsonNode结构验证
//     *
//     * @param schemaJson Schema定义
//     * @param dataJson 待验证的数据
//     * @return 验证结果
//     */
//    public ValidationResult validate(String schemaJson, String dataJson) {
//        try {
//            JsonNode schemaNode = objectMapper.readTree(schemaJson);
//            JsonNode dataNode = objectMapper.readTree(dataJson);
//
//            return validateNode(schemaNode, dataNode, "");
//
//        } catch (Exception e) {
//            log.error("JSON Schema验证失败", e);
//            return ValidationResult.error("验证过程发生错误: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 验证JSON数据（对象形式）
//     */
//    public ValidationResult validate(String schemaJson, Object data) {
//        try {
//            String dataJson = objectMapper.writeValueAsString(data);
//            return validate(schemaJson, dataJson);
//        } catch (Exception e) {
//            log.error("对象序列化失败", e);
//            return ValidationResult.error("数据序列化失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取解析后的Schema（带缓存）
//     */
//    @Cacheable(value = "json_schema_cache", key = "#schemaJson.hashCode()")
//    public JsonNode getSchema(String schemaJson) throws IOException {
//        JsonNode schemaNode = objectMapper.readTree(schemaJson);
//
//        // 如果不允许额外属性，修改Schema
//        if (!allowAdditionalProperties) {
//            schemaNode = addAdditionalPropertiesFalse(schemaNode);
//        }
//
//        return schemaNode;
//    }
//
//    /**
//     * 验证节点
//     */
//    private ValidationResult validateNode(JsonNode schema, JsonNode data, String path) {
//        ValidationResult result = new ValidationResult();
//        List<String> errors = new ArrayList<>();
//
//        try {
//            // 验证类型
//            if (schema.has("type")) {
//                String expectedType = schema.get("type").asText();
//                if (!validateType(data, expectedType)) {
//                    errors.add(formatError(path, "类型不匹配，期望: " + expectedType + ", 实际: " + getActualType(data)));
//                }
//            }
//
//            // 验证必填字段
//            if (schema.has("required") && schema.get("required").isArray()) {
//                for (JsonNode requiredField : schema.get("required")) {
//                    String fieldName = requiredField.asText();
//                    if (!data.has(fieldName) || data.get(fieldName).isNull()) {
//                        errors.add(formatError(path, "缺少必填字段: " + fieldName));
//                    }
//                }
//            }
//
//            // 验证对象属性
//            if (schema.has("properties") && data.isObject()) {
//                JsonNode properties = schema.get("properties");
//                for (Iterator<Map.Entry<String, JsonNode>> fields = properties.fields(); fields.hasNext();) {
//                    Map.Entry<String, JsonNode> entry = fields.next();
//                    String fieldName = entry.getKey();
//                    JsonNode fieldSchema = entry.getValue();
//
//                    if (data.has(fieldName)) {
//                        String fieldPath = path.isEmpty() ? fieldName : path + "." + fieldName;
//                        ValidationResult fieldResult = validateNode(fieldSchema, data.get(fieldName), fieldPath);
//                        if (!fieldResult.isValid()) {
//                            errors.addAll(fieldResult.getErrors());
//                        }
//                    }
//                }
//
//                // 检查额外属性
//                if (!allowAdditionalProperties && schema.has("additionalProperties")
//                    && !schema.get("additionalProperties").asBoolean()) {
//                    for (Iterator<String> fieldNames = data.fieldNames(); fieldNames.hasNext();) {
//                        String fieldName = fieldNames.next();
//                        if (!properties.has(fieldName)) {
//                            errors.add(formatError(path, "不允许的额外属性: " + fieldName));
//                        }
//                    }
//                }
//            }
//
//            // 验证字符串模式
//            if (schema.has("pattern") && data.isTextual()) {
//                String pattern = schema.get("pattern").asText();
//                String value = data.asText();
//                if (!value.matches(pattern)) {
//                    errors.add(formatError(path, "不匹配模式: " + pattern));
//                }
//            }
//
//            // 验证数值范围
//            if (data.isNumber()) {
//                if (schema.has("minimum")) {
//                    double min = schema.get("minimum").asDouble();
//                    if (data.asDouble() < min) {
//                        errors.add(formatError(path, "值小于最小值: " + min));
//                    }
//                }
//                if (schema.has("maximum")) {
//                    double max = schema.get("maximum").asDouble();
//                    if (data.asDouble() > max) {
//                        errors.add(formatError(path, "值大于最大值: " + max));
//                    }
//                }
//            }
//
//            // 验证枚举
//            if (schema.has("enum") && schema.get("enum").isArray()) {
//                boolean found = false;
//                for (JsonNode enumValue : schema.get("enum")) {
//                    if (data.equals(enumValue)) {
//                        found = true;
//                        break;
//                    }
//                }
//                if (!found) {
//                    errors.add(formatError(path, "值不在枚举范围内"));
//                }
//            }
//
//        } catch (Exception e) {
//            errors.add(formatError(path, "验证过程发生错误: " + e.getMessage()));
//        }
//
//        result.setValid(errors.isEmpty());
//        result.setErrors(errors);
//        return result;
//    }
//
//    /**
//     * 验证类型
//     */
//    private boolean validateType(JsonNode data, String expectedType) {
//        return switch (expectedType.toLowerCase()) {
//            case "string" -> data.isTextual();
//            case "number", "integer" -> data.isNumber();
//            case "boolean" -> data.isBoolean();
//            case "array" -> data.isArray();
//            case "object" -> data.isObject();
//            case "null" -> data.isNull();
//            default -> true;
//        };
//    }
//
//    /**
//     * 获取实际类型
//     */
//    private String getActualType(JsonNode data) {
//        if (data.isTextual()) return "string";
//        if (data.isNumber()) return "number";
//        if (data.isBoolean()) return "boolean";
//        if (data.isArray()) return "array";
//        if (data.isObject()) return "object";
//        if (data.isNull()) return "null";
//        return "unknown";
//    }
//
//    /**
//     * 格式化错误信息
//     */
//    private String formatError(String path, String message) {
//        if (verboseErrors && !path.isEmpty()) {
//            return "路径 '" + path + "': " + message;
//        }
//        return message;
//    }
//
//    /**
//     * 为Schema添加additionalProperties: false
//     */
//    private JsonNode addAdditionalPropertiesFalse(JsonNode schemaNode) {
//        if (schemaNode.isObject() && schemaNode.has("properties")) {
//            @SuppressWarnings("unchecked")
//            Map<String, Object> schemaMap = objectMapper.convertValue(schemaNode, Map.class);
//            schemaMap.put("additionalProperties", false);
//            return objectMapper.valueToTree(schemaMap);
//        }
//        return schemaNode;
//    }
//
//    /**
//     * 创建标准Schema模板
//     */
//    public String createSchemaTemplate(String optionType) {
//        Map<String, Object> schema = new HashMap<>();
//        schema.put("$schema", "http://json-schema.org/draft-04/schema#");
//        schema.put("type", "object");
//        schema.put("title", optionType + "配置Schema");
//        schema.put("description", "用于验证" + optionType + "配置选项的数据结构");
//
//        Map<String, Object> properties = new HashMap<>();
//
//        // 根据选项类型添加通用属性
//        switch (optionType.toLowerCase()) {
//            case "exterior_color":
//                properties.put("name", createStringProperty("颜色名称", true));
//                properties.put("code", createStringProperty("颜色代码", true));
//                properties.put("hex", createStringProperty("十六进制值", true, "^#[0-9A-Fa-f]{6}$"));
//                properties.put("image", createStringProperty("颜色图片URL", false));
//                break;
//
//            case "interior_color":
//                properties.put("name", createStringProperty("内饰名称", true));
//                properties.put("material", createStringProperty("材质", true));
//                properties.put("color", createStringProperty("颜色", true));
//                properties.put("image", createStringProperty("内饰图片URL", false));
//                break;
//
//            case "wheels":
//                properties.put("name", createStringProperty("轮毂名称", true));
//                properties.put("size", createIntegerProperty("尺寸(英寸)", true, 16, 22));
//                properties.put("style", createStringProperty("样式", true));
//                properties.put("material", createStringProperty("材质", true));
//                properties.put("image", createStringProperty("轮毂图片URL", false));
//                break;
//
//            case "audio":
//                properties.put("brand", createStringProperty("品牌", true));
//                properties.put("speakers_count", createIntegerProperty("扬声器数量", true, 4, 20));
//                properties.put("features", createArrayProperty("功能特性", false));
//                properties.put("power", createIntegerProperty("功率(W)", false, 100, 1000));
//                break;
//
//            default:
//                // 通用属性
//                properties.put("name", createStringProperty("名称", true));
//                properties.put("value", createStringProperty("值", true));
//                properties.put("description", createStringProperty("描述", false));
//                break;
//        }
//
//        schema.put("properties", properties);
//        schema.put("additionalProperties", allowAdditionalProperties);
//
//        // 设置必填字段
//        List<String> required = new ArrayList<>();
//        for (Map.Entry<String, Object> entry : properties.entrySet()) {
//            Map<String, Object> prop = (Map<String, Object>) entry.getValue();
//            if (Boolean.TRUE.equals(prop.get("required"))) {
//                required.add(entry.getKey());
//                prop.remove("required"); // 移除自定义的required字段
//            }
//        }
//        if (!required.isEmpty()) {
//            schema.put("required", required);
//        }
//
//        try {
//            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(schema);
//        } catch (Exception e) {
//            log.error("创建Schema模板失败", e);
//            return "{}";
//        }
//    }
//
//    /**
//     * 创建字符串属性
//     */
//    private Map<String, Object> createStringProperty(String description, boolean required) {
//        return createStringProperty(description, required, null);
//    }
//
//    private Map<String, Object> createStringProperty(String description, boolean required, String pattern) {
//        Map<String, Object> prop = new HashMap<>();
//        prop.put("type", "string");
//        prop.put("description", description);
//        prop.put("required", required);
//        if (pattern != null) {
//            prop.put("pattern", pattern);
//        }
//        return prop;
//    }
//
//    /**
//     * 创建整数属性
//     */
//    private Map<String, Object> createIntegerProperty(String description, boolean required, Integer min, Integer max) {
//        Map<String, Object> prop = new HashMap<>();
//        prop.put("type", "integer");
//        prop.put("description", description);
//        prop.put("required", required);
//        if (min != null) {
//            prop.put("minimum", min);
//        }
//        if (max != null) {
//            prop.put("maximum", max);
//        }
//        return prop;
//    }
//
//    /**
//     * 创建数组属性
//     */
//    private Map<String, Object> createArrayProperty(String description, boolean required) {
//        Map<String, Object> prop = new HashMap<>();
//        prop.put("type", "array");
//        prop.put("description", description);
//        prop.put("required", required);
//
//        Map<String, Object> items = new HashMap<>();
//        items.put("type", "string");
//        prop.put("items", items);
//
//        return prop;
//    }
//
//    /**
//     * 验证结果类
//     */
//    @Data
//    public static class ValidationResult {
//        private boolean valid = true;
//        private List<String> errors = new ArrayList<>();
//        private String errorMessage;
//
//        public static ValidationResult success() {
//            return new ValidationResult();
//        }
//
//        public static ValidationResult error(String message) {
//            ValidationResult result = new ValidationResult();
//            result.valid = false;
//            result.errorMessage = message;
//            result.errors = new ArrayList<>(Collections.singletonList(message));
//            return result;
//        }
//
//        public String getFormattedErrors() {
//            return String.join("; ", errors);
//        }
//    }
//
//    /**
//     * 缓存配置内部类
//     */
//    @Data
//    public static class CacheConfig {
//        private int maxSize = 100;
//        private String expireAfterWrite = "1h";
//    }
//
//    // Getters and Setters for configuration properties
//    public boolean isStrictMode() { return strictMode; }
//    public void setStrictMode(boolean strictMode) { this.strictMode = strictMode; }
//
//    public boolean isAllowAdditionalProperties() { return allowAdditionalProperties; }
//    public void setAllowAdditionalProperties(boolean allowAdditionalProperties) { this.allowAdditionalProperties = allowAdditionalProperties; }
//
//    public boolean isVerboseErrors() { return verboseErrors; }
//    public void setVerboseErrors(boolean verboseErrors) { this.verboseErrors = verboseErrors; }
//
//    public CacheConfig getCache() { return cache; }
//    public void setCache(CacheConfig cache) { this.cache = cache; }
//}