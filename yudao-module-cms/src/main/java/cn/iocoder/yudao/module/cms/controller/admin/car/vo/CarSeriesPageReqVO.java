package cn.iocoder.yudao.module.cms.controller.admin.car.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 车系分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CarSeriesPageReqVO extends PageParam {

    @Schema(description = "车系编码", example = "TIGGO8_PRO")
    private String code;

    @Schema(description = "车系名称", example = "瑞虎8 PRO")
    private String name;

    @Schema(description = "状态：0-禁用，1-启用", example = "1")
    private Integer status;

}