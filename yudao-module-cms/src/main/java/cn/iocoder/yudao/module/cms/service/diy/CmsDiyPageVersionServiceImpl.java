package cn.iocoder.yudao.module.cms.service.diy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.diy.vo.version.CmsDiyPageVersionPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.diy.vo.version.CmsDiyPageVersionRollbackReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageDO;
import cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageVersionDO;
import cn.iocoder.yudao.module.cms.dal.mysql.diy.CmsDiyPageMapper;
import cn.iocoder.yudao.module.cms.dal.mysql.diy.CmsDiyPageVersionMapper;
import cn.iocoder.yudao.module.cms.enums.diy.CmsDiyPageStatusEnum;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.*;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.cms.enums.ErrorCodeConstants.*;

/**
 * CMS DIY页面版本 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class CmsDiyPageVersionServiceImpl implements CmsDiyPageVersionService {

    @Resource
    private CmsDiyPageVersionMapper pageVersionMapper;

    @Resource
    private CmsDiyPageMapper diyPageMapper;

    @Resource
    private CmsDiyPageService diyPageService;

    private static final Integer DEFAULT_KEEP_VERSION_COUNT = 10;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createPageVersion(Long pageId, String versionName, String content, String remark) {
        diyPageService.validatePageExists(pageId);
        
        Integer maxVersion = pageVersionMapper.selectMaxVersionByPageId(pageId);
        Integer nextVersion = (maxVersion == null ? 0 : maxVersion) + 1;
        
        CmsDiyPageVersionDO version = new CmsDiyPageVersionDO();
        version.setPageId(pageId);
        version.setVersion(nextVersion);
        version.setName(StrUtil.isNotBlank(versionName) ? versionName : 
                      "V" + nextVersion + " - " + LocalDateTime.now());
        version.setContent(content);
        version.setPublishTime(LocalDateTime.now());
        version.setIsPublished(false);
        version.setRemark(remark);
        
        pageVersionMapper.insert(version);
        
        log.info("[createPageVersion][创建页面版本成功，页面ID：{}，版本号：{}]", pageId, nextVersion);
        return version.getId();
    }

    @Override
    public CmsDiyPageVersionDO getPageVersion(Long id) {
        return pageVersionMapper.selectById(id);
    }

    @Override
    public PageResult<CmsDiyPageVersionDO> getPageVersionPage(CmsDiyPageVersionPageReqVO pageReqVO) {
        return pageVersionMapper.selectPage(pageReqVO);
    }

    @Override
    public List<CmsDiyPageVersionDO> getPageVersionList(Long pageId) {
        if (pageId == null) {
            return Collections.emptyList();
        }
        return pageVersionMapper.selectListByPageId(pageId);
    }

    @Override
    public CmsDiyPageVersionDO getLatestPageVersion(Long pageId) {
        if (pageId == null) {
            return null;
        }
        return pageVersionMapper.selectLatestByPageId(pageId);
    }

    @Override
    public CmsDiyPageVersionDO getPublishedVersion(Long pageId, Integer version) {
        if (pageId == null || version == null) {
            return null;
        }
        return pageVersionMapper.selectByPageIdAndVersion(pageId, version);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rollbackPageVersion(@Valid CmsDiyPageVersionRollbackReqVO rollbackReqVO) {
        CmsDiyPageVersionDO targetVersion = validateVersionExists(rollbackReqVO.getVersionId());
        
        CmsDiyPageDO page = diyPageService.validatePageExists(targetVersion.getPageId());
        
        validateCanRollback(targetVersion.getPageId(), rollbackReqVO.getVersionId());
        
        Long newVersionId = createPageVersion(
            targetVersion.getPageId(),
            "回滚到 " + targetVersion.getName(),
            targetVersion.getContent(),
            "从版本 " + targetVersion.getVersion() + " 回滚"
        );
        
        CmsDiyPageVersionDO newVersion = pageVersionMapper.selectById(newVersionId);
        
        try {
            diyPageService.updateDiyPageContent(
                targetVersion.getPageId(), 
                targetVersion.getContent(), 
                rollbackReqVO.getCurrentVersion()
            );
            
            page.setVersion(newVersion.getVersion());
            page.setStatus(CmsDiyPageStatusEnum.DRAFT.getStatus());
            diyPageMapper.updateById(page);
            
        } catch (Exception e) {
            log.error("[rollbackPageVersion][回滚页面版本失败，页面ID：{}，目标版本ID：{}]", 
                     targetVersion.getPageId(), rollbackReqVO.getVersionId(), e);
            throw e;
        }
        
        diyPageService.clearPageCache(targetVersion.getPageId());
        
        log.info("[rollbackPageVersion][回滚页面版本成功，页面ID：{}，回滚到版本：{}]", 
                targetVersion.getPageId(), targetVersion.getVersion());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer cleanupOldVersions(Long pageId, Integer keepCount) {
        if (pageId == null || keepCount == null || keepCount <= 0) {
            return 0;
        }
        
        List<CmsDiyPageVersionDO> allVersions = pageVersionMapper.selectListByPageId(pageId);
        if (allVersions.size() <= keepCount) {
            return 0;
        }
        
        allVersions.sort((v1, v2) -> v2.getVersion().compareTo(v1.getVersion()));
        
        CmsDiyPageDO page = diyPageMapper.selectById(pageId);
        Integer publishedVersion = page != null ? page.getPublishedVersion() : null;
        
        List<Long> versionsToDelete = new ArrayList<>();
        int keptCount = 0;
        
        for (CmsDiyPageVersionDO version : allVersions) {
            if (publishedVersion != null && version.getVersion().equals(publishedVersion)) {
                continue;
            }
            
            if (keptCount >= keepCount) {
                versionsToDelete.add(version.getId());
            } else {
                keptCount++;
            }
        }
        
        if (CollUtil.isNotEmpty(versionsToDelete)) {
            pageVersionMapper.deleteBatchIds(versionsToDelete);
            log.info("[cleanupOldVersions][清理页面旧版本，页面ID：{}，删除版本数：{}]", 
                    pageId, versionsToDelete.size());
            return versionsToDelete.size();
        }
        
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer cleanupAllOldVersions(Integer keepCount) {
        if (keepCount == null || keepCount <= 0) {
            keepCount = DEFAULT_KEEP_VERSION_COUNT;
        }
        
        List<Long> allPageIds = pageVersionMapper.selectDistinctPageIds();
        
        int totalCleaned = 0;
        for (Long pageId : allPageIds) {
            try {
                Integer cleaned = cleanupOldVersions(pageId, keepCount);
                totalCleaned += cleaned;
            } catch (Exception e) {
                log.error("[cleanupAllOldVersions][清理页面版本失败，页面ID：{}]", pageId, e);
            }
        }
        
        log.info("[cleanupAllOldVersions][批量清理页面旧版本完成，清理页面数：{}，总删除版本数：{}]", 
                allPageIds.size(), totalCleaned);
        return totalCleaned;
    }

    @Override
    public String compareVersions(Long versionId1, Long versionId2) {
        CmsDiyPageVersionDO version1 = validateVersionExists(versionId1);
        CmsDiyPageVersionDO version2 = validateVersionExists(versionId2);
        
        if (!version1.getPageId().equals(version2.getPageId())) {
            throw exception(CMS_DIY_PAGE_VERSION_NOT_SAME_PAGE);
        }
        
        Map<String, Object> comparison = new HashMap<>();
        comparison.put("version1", buildVersionInfo(version1));
        comparison.put("version2", buildVersionInfo(version2));
        
        boolean contentChanged = !Objects.equals(version1.getContent(), version2.getContent());
        comparison.put("contentChanged", contentChanged);
        
        if (contentChanged && StrUtil.isNotBlank(version1.getContent()) && StrUtil.isNotBlank(version2.getContent())) {
            try {
                Object content1 = JSONUtil.parse(version1.getContent());
                Object content2 = JSONUtil.parse(version2.getContent());
                comparison.put("content1", content1);
                comparison.put("content2", content2);
            } catch (Exception e) {
                comparison.put("content1", version1.getContent());
                comparison.put("content2", version2.getContent());
            }
        }
        
        return JSONUtil.toJsonStr(comparison);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePageVersions(Long pageId) {
        if (pageId == null) {
            return;
        }
        
        int deleted = pageVersionMapper.deleteByPageId(pageId);
        log.info("[deletePageVersions][删除页面所有版本，页面ID：{}，删除版本数：{}]", pageId, deleted);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateVersionRemark(Long id, String remark) {
        CmsDiyPageVersionDO version = validateVersionExists(id);
        
        version.setRemark(remark);
        pageVersionMapper.updateById(version);
        
        log.info("[updateVersionRemark][更新版本备注，版本ID：{}]", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void markVersionAsPublished(Long pageId, Integer version) {
        if (pageId == null || version == null) {
            return;
        }
        
        pageVersionMapper.updateAllUnpublishedByPageId(pageId);
        
        CmsDiyPageVersionDO versionDO = pageVersionMapper.selectByPageIdAndVersion(pageId, version);
        if (versionDO != null) {
            versionDO.setIsPublished(true);
            versionDO.setPublishTime(LocalDateTime.now());
            pageVersionMapper.updateById(versionDO);
        }
        
        log.info("[markVersionAsPublished][标记版本为已发布，页面ID：{}，版本号：{}]", pageId, version);
    }

    @Override
    public Long getVersionCount(Long pageId) {
        if (pageId == null) {
            return 0L;
        }
        return pageVersionMapper.selectCountByPageId(pageId);
    }

    @Override
    public CmsDiyPageVersionDO validateVersionExists(Long id) {
        if (id == null) {
            throw exception(CMS_DIY_PAGE_VERSION_NOT_EXISTS);
        }
        
        CmsDiyPageVersionDO version = pageVersionMapper.selectById(id);
        if (version == null) {
            throw exception(CMS_DIY_PAGE_VERSION_NOT_EXISTS);
        }
        
        return version;
    }

    @Override
    public void validateCanRollback(Long pageId, Long versionId) {
        CmsDiyPageVersionDO version = validateVersionExists(versionId);
        
        if (!version.getPageId().equals(pageId)) {
            throw exception(CMS_DIY_PAGE_VERSION_NOT_BELONG_TO_PAGE);
        }
        
        CmsDiyPageDO page = diyPageMapper.selectById(pageId);
        if (page == null) {
            throw exception(CMS_DIY_PAGE_NOT_EXISTS);
        }
        
        if (CmsDiyPageStatusEnum.PUBLISHED.getStatus().equals(page.getStatus())) {
            log.warn("[validateCanRollback][页面处于发布状态，回滚后将变为草稿状态，页面ID：{}]", pageId);
        }
    }

    private Map<String, Object> buildVersionInfo(CmsDiyPageVersionDO version) {
        Map<String, Object> info = new HashMap<>();
        info.put("id", version.getId());
        info.put("version", version.getVersion());
        info.put("name", version.getName());
        info.put("publishTime", version.getPublishTime());
        info.put("isPublished", version.getIsPublished());
        info.put("remark", version.getRemark());
        return info;
    }
}