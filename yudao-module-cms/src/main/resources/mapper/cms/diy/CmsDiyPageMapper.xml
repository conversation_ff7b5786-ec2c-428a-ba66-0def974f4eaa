<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.cms.dal.mysql.diy.CmsDiyPageMapper">

    <!-- 乐观锁更新页面 -->
    <update id="updateByIdAndVersion">
        UPDATE cms_diy_page SET
            name = #{updateObj.name},
            parent_id = #{updateObj.parentId},
            menu_id = #{updateObj.menuId},
            path = #{updateObj.path},
            keywords = #{updateObj.keywords},
            description = #{updateObj.description},
            status = #{updateObj.status},
            published_version = #{updateObj.publishedVersion},
            content = #{updateObj.content},
            version = #{updateObj.version},
            updater = #{updateObj.updater},
            update_time = NOW()
        WHERE id = #{updateObj.id} AND version = #{version} AND deleted = 0
    </update>

    <!-- 乐观锁删除页面 -->
    <update id="deleteByIdAndVersion">
        UPDATE cms_diy_page SET
            deleted = 1,
            update_time = NOW()
        WHERE id = #{id} AND version = #{version} AND deleted = 0
    </update>

    <!-- 查询热门页面（基于更新时间，后续可结合访问统计优化） -->
    <select id="selectHotPages" resultType="cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageDO">
        SELECT * FROM cms_diy_page
        WHERE status = 1 AND deleted = 0
        ORDER BY update_time DESC
        LIMIT #{limit}
    </select>

    <!-- 搜索已发布页面 -->
    <select id="searchPublishedPages" resultType="cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageDO">
        SELECT * FROM cms_diy_page
        WHERE status = 1 AND deleted = 0
        AND (
            name LIKE CONCAT('%', #{keyword}, '%')
            OR keywords LIKE CONCAT('%', #{keyword}, '%')
            OR description LIKE CONCAT('%', #{keyword}, '%')
        )
        ORDER BY id DESC
    </select>

    <!-- 批量更新页面路径（当菜单路径变更时使用） -->
    <update id="batchUpdatePagePaths">
        <foreach collection="pageUpdates" item="update" separator=";">
            UPDATE cms_diy_page 
            SET path = #{update.newFullPath}, update_time = NOW()
            WHERE id = #{update.pageId} AND deleted = 0
        </foreach>
    </update>

</mapper>