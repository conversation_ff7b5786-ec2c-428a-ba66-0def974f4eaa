package cn.iocoder.yudao.module.cms.service.diy;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.cms.controller.admin.diy.vo.page.*;
import cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageDO;
import cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageVersionDO;
import cn.iocoder.yudao.module.cms.dal.dataobject.menu.CmsMenuDO;
import cn.iocoder.yudao.module.cms.dal.mysql.diy.CmsDiyPageMapper;
import cn.iocoder.yudao.module.cms.dal.mysql.diy.CmsDiyPageVersionMapper;
import cn.iocoder.yudao.module.cms.dal.mysql.statistics.CmsPageVisitLogMapper;
import cn.iocoder.yudao.module.cms.enums.diy.CmsDiyPageStatusEnum;
import cn.iocoder.yudao.module.cms.framework.cache.core.CmsCacheService;
import cn.iocoder.yudao.module.cms.service.menu.CmsMenuService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.dao.OptimisticLockingFailureException;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.cms.enums.ErrorCodeConstants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CmsDiyPageServiceTest {

    @Mock
    private CmsDiyPageMapper diyPageMapper;

    @Mock
    private CmsDiyPageVersionMapper versionMapper;

    @Mock
    private CmsPageVisitLogMapper visitLogMapper;

    @Mock
    private CmsCacheService cacheService;

    @Mock
    private CmsMenuService menuService;

    @InjectMocks
    private CmsDiyPageServiceImpl diyPageService;

    @BeforeEach
    void setUp() {
        TenantContextHolder.setTenantId(1L);
    }

    @AfterEach
    void tearDown() {
        TenantContextHolder.clear();
    }

    @Test
    void testCreateDiyPage() {
        // Given
        CmsDiyPageCreateReqVO createReqVO = new CmsDiyPageCreateReqVO();
        createReqVO.setName("测试页面");
        createReqVO.setPath("/test-page");
        createReqVO.setMenuId(1L);
        createReqVO.setContent("{\"components\": []}");
        createReqVO.setRemark("测试备注");

        CmsMenuDO mockMenu = new CmsMenuDO();
        mockMenu.setId(1L);
        mockMenu.setPath("/test");

        when(menuService.getMenu(1L)).thenReturn(mockMenu);
        when(diyPageMapper.selectByPath("/test/test-page")).thenReturn(null);
        when(diyPageMapper.insert(any(CmsDiyPageDO.class))).thenAnswer(invocation -> {
            CmsDiyPageDO page = invocation.getArgument(0);
            page.setId(100L);
            return 1;
        });

        // When
        Long pageId = diyPageService.createDiyPage(createReqVO);

        // Then
        assertNotNull(pageId);
        assertEquals(100L, pageId);
        verify(diyPageMapper).insert(any(CmsDiyPageDO.class));
        verify(cacheService).evictPageCache(pageId);
    }

    @Test
    void testCreateDiyPage_DuplicatePath() {
        // Given
        CmsDiyPageCreateReqVO createReqVO = new CmsDiyPageCreateReqVO();
        createReqVO.setName("测试页面");
        createReqVO.setPath("/test-page");
        createReqVO.setMenuId(1L);

        CmsMenuDO mockMenu = new CmsMenuDO();
        mockMenu.setId(1L);
        mockMenu.setPath("/test");

        CmsDiyPageDO existingPage = new CmsDiyPageDO();
        existingPage.setId(200L);

        when(menuService.getMenu(1L)).thenReturn(mockMenu);
        when(diyPageMapper.selectByPath("/test/test-page")).thenReturn(existingPage);

        // When & Then
        assertThrows(RuntimeException.class, () -> diyPageService.createDiyPage(createReqVO));
    }

    @Test
    void testUpdateDiyPage_Success() {
        // Given
        CmsDiyPageUpdateReqVO updateReqVO = new CmsDiyPageUpdateReqVO();
        updateReqVO.setId(100L);
        updateReqVO.setName("更新页面");
        updateReqVO.setContent("{\"components\": [\"updated\"]}");
        updateReqVO.setVersion(1);

        CmsDiyPageDO existingPage = new CmsDiyPageDO();
        existingPage.setId(100L);
        existingPage.setVersion(1);
        existingPage.setPath("/test/page");
        existingPage.setMenuId(1L);

        when(diyPageMapper.selectById(100L)).thenReturn(existingPage);
        when(diyPageMapper.updateByIdAndVersion(any(CmsDiyPageDO.class))).thenReturn(1);

        // When
        diyPageService.updateDiyPage(updateReqVO);

        // Then
        verify(diyPageMapper).updateByIdAndVersion(any(CmsDiyPageDO.class));
        verify(cacheService).evictPageCache(100L);
    }

    @Test
    void testUpdateDiyPage_OptimisticLockFailure() {
        // Given
        CmsDiyPageUpdateReqVO updateReqVO = new CmsDiyPageUpdateReqVO();
        updateReqVO.setId(100L);
        updateReqVO.setVersion(1);

        CmsDiyPageDO existingPage = new CmsDiyPageDO();
        existingPage.setId(100L);
        existingPage.setVersion(2); // 版本不匹配

        when(diyPageMapper.selectById(100L)).thenReturn(existingPage);

        // When & Then
        assertThrows(OptimisticLockingFailureException.class, () -> diyPageService.updateDiyPage(updateReqVO));
    }

    @Test
    void testUpdateDiyPage_ConcurrentUpdate() {
        // Given
        CmsDiyPageUpdateReqVO updateReqVO = new CmsDiyPageUpdateReqVO();
        updateReqVO.setId(100L);
        updateReqVO.setVersion(1);

        CmsDiyPageDO existingPage = new CmsDiyPageDO();
        existingPage.setId(100L);
        existingPage.setVersion(1);

        when(diyPageMapper.selectById(100L)).thenReturn(existingPage);
        when(diyPageMapper.updateByIdAndVersion(any(CmsDiyPageDO.class))).thenReturn(0); // 更新失败

        // When & Then
        assertThrows(OptimisticLockingFailureException.class, () -> diyPageService.updateDiyPage(updateReqVO));
    }

    @Test
    void testPublishDiyPage_Success() {
        // Given
        CmsDiyPagePublishReqVO publishReqVO = new CmsDiyPagePublishReqVO();
        publishReqVO.setId(100L);
        publishReqVO.setVersion(1);

        CmsDiyPageDO existingPage = new CmsDiyPageDO();
        existingPage.setId(100L);
        existingPage.setVersion(1);
        existingPage.setStatus(CmsDiyPageStatusEnum.DRAFT.getStatus());
        existingPage.setContent("{\"components\": []}");
        existingPage.setName("测试页面");

        when(diyPageMapper.selectById(100L)).thenReturn(existingPage);
        when(diyPageMapper.updateByIdAndVersion(any(CmsDiyPageDO.class))).thenReturn(1);
        when(versionMapper.insert(any(CmsDiyPageVersionDO.class))).thenReturn(1);

        // When
        diyPageService.publishDiyPage(publishReqVO);

        // Then
        verify(diyPageMapper).updateByIdAndVersion(argThat(page -> 
            page.getStatus().equals(CmsDiyPageStatusEnum.PUBLISHED.getStatus()) &&
            page.getPublishTime() != null
        ));
        verify(versionMapper).insert(any(CmsDiyPageVersionDO.class));
        verify(cacheService).evictPageCache(100L);
    }

    @Test
    void testPublishDiyPage_AlreadyPublished() {
        // Given
        CmsDiyPagePublishReqVO publishReqVO = new CmsDiyPagePublishReqVO();
        publishReqVO.setId(100L);

        CmsDiyPageDO existingPage = new CmsDiyPageDO();
        existingPage.setId(100L);
        existingPage.setStatus(CmsDiyPageStatusEnum.PUBLISHED.getStatus());

        when(diyPageMapper.selectById(100L)).thenReturn(existingPage);

        // When & Then
        assertThrows(RuntimeException.class, () -> diyPageService.publishDiyPage(publishReqVO));
    }

    @Test
    void testOfflineDiyPage_Success() {
        // Given
        Long pageId = 100L;
        Integer version = 1;

        CmsDiyPageDO existingPage = new CmsDiyPageDO();
        existingPage.setId(100L);
        existingPage.setVersion(1);
        existingPage.setStatus(CmsDiyPageStatusEnum.PUBLISHED.getStatus());

        when(diyPageMapper.selectById(100L)).thenReturn(existingPage);
        when(diyPageMapper.updateByIdAndVersion(any(CmsDiyPageDO.class))).thenReturn(1);

        // When
        diyPageService.offlineDiyPage(pageId, version);

        // Then
        verify(diyPageMapper).updateByIdAndVersion(argThat(page -> 
            page.getStatus().equals(CmsDiyPageStatusEnum.OFFLINE.getStatus()) &&
            page.getOfflineTime() != null
        ));
        verify(cacheService).evictPageCache(100L);
    }

    @Test
    void testDeleteDiyPage_Success() {
        // Given
        Long pageId = 100L;

        CmsDiyPageDO existingPage = new CmsDiyPageDO();
        existingPage.setId(100L);
        existingPage.setStatus(CmsDiyPageStatusEnum.DRAFT.getStatus());

        when(diyPageMapper.selectById(100L)).thenReturn(existingPage);
        when(diyPageMapper.deleteById(100L)).thenReturn(1);

        // When
        diyPageService.deleteDiyPage(pageId);

        // Then
        verify(diyPageMapper).deleteById(100L);
        verify(versionMapper).deleteByPageId(100L);
        verify(visitLogMapper).deleteByPageId(100L);
        verify(cacheService).evictPageCache(100L);
    }

    @Test
    void testDeleteDiyPage_PublishedPage() {
        // Given
        Long pageId = 100L;

        CmsDiyPageDO existingPage = new CmsDiyPageDO();
        existingPage.setId(100L);
        existingPage.setStatus(CmsDiyPageStatusEnum.PUBLISHED.getStatus());

        when(diyPageMapper.selectById(100L)).thenReturn(existingPage);

        // When & Then
        assertThrows(RuntimeException.class, () -> diyPageService.deleteDiyPage(pageId));
    }

    @Test
    void testGetDiyPagePage() {
        // Given
        CmsDiyPagePageReqVO pageReqVO = new CmsDiyPagePageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);
        pageReqVO.setName("测试");

        List<CmsDiyPageDO> pageList = Arrays.asList(
            createMockPage(1L, "测试页面1"),
            createMockPage(2L, "测试页面2")
        );

        when(diyPageMapper.selectPage(pageReqVO)).thenReturn(new PageResult<>(pageList, 2L));

        // When
        PageResult<CmsDiyPageDO> result = diyPageService.getDiyPagePage(pageReqVO);

        // Then
        assertNotNull(result);
        assertEquals(2L, result.getTotal());
        assertEquals(2, result.getList().size());
    }

    @Test
    void testGetDiyPage_WithCache() {
        // Given
        Long pageId = 100L;
        CmsDiyPageDO cachedPage = createMockPage(pageId, "缓存页面");

        when(cacheService.getPageContent(eq(pageId), eq(CmsDiyPageDO.class), any()))
            .thenReturn(cachedPage);

        // When
        CmsDiyPageDO result = diyPageService.getDiyPage(pageId);

        // Then
        assertNotNull(result);
        assertEquals(pageId, result.getId());
        assertEquals("缓存页面", result.getName());
        verify(cacheService).getPageContent(eq(pageId), eq(CmsDiyPageDO.class), any());
    }

    @Test
    void testGetDiyPage_CacheMiss() {
        // Given
        Long pageId = 100L;
        CmsDiyPageDO dbPage = createMockPage(pageId, "数据库页面");

        when(cacheService.getPageContent(eq(pageId), eq(CmsDiyPageDO.class), any()))
            .thenAnswer(invocation -> {
                // 模拟缓存未命中，调用supplier
                return invocation.getArgument(2, java.util.function.Supplier.class).get();
            });
        when(diyPageMapper.selectById(pageId)).thenReturn(dbPage);

        // When
        CmsDiyPageDO result = diyPageService.getDiyPage(pageId);

        // Then
        assertNotNull(result);
        assertEquals(pageId, result.getId());
        assertEquals("数据库页面", result.getName());
        verify(diyPageMapper).selectById(pageId);
    }

    @Test
    void testGetPublishedPageByPath() {
        // Given
        String path = "/test/page";
        CmsDiyPageDO publishedPage = createMockPage(100L, "已发布页面");
        publishedPage.setPath(path);
        publishedPage.setStatus(CmsDiyPageStatusEnum.PUBLISHED.getStatus());

        when(cacheService.getPageByPath(eq(path), eq(CmsDiyPageDO.class), any()))
            .thenAnswer(invocation -> {
                return invocation.getArgument(2, java.util.function.Supplier.class).get();
            });
        when(diyPageMapper.selectByPathAndStatus(path, CmsDiyPageStatusEnum.PUBLISHED.getStatus()))
            .thenReturn(publishedPage);

        // When
        CmsDiyPageDO result = diyPageService.getPublishedPageByPath(path);

        // Then
        assertNotNull(result);
        assertEquals(path, result.getPath());
        assertEquals(CmsDiyPageStatusEnum.PUBLISHED.getStatus(), result.getStatus());
    }

    @Test
    void testGetPublishedPages() {
        // Given
        List<CmsDiyPageDO> publishedPages = Arrays.asList(
            createMockPage(1L, "页面1"),
            createMockPage(2L, "页面2")
        );
        publishedPages.forEach(page -> page.setStatus(CmsDiyPageStatusEnum.PUBLISHED.getStatus()));

        when(cacheService.getPublishedPages(eq(List.class), any()))
            .thenAnswer(invocation -> {
                return invocation.getArgument(1, java.util.function.Supplier.class).get();
            });
        when(diyPageMapper.selectByStatus(CmsDiyPageStatusEnum.PUBLISHED.getStatus()))
            .thenReturn(publishedPages);

        // When
        List<CmsDiyPageDO> result = diyPageService.getPublishedPages();

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(page -> 
            page.getStatus().equals(CmsDiyPageStatusEnum.PUBLISHED.getStatus())));
    }

    @Test
    void testBuildPagePath() {
        // Given
        CmsDiyPageDO page = new CmsDiyPageDO();
        page.setPath("/page");
        page.setMenuId(1L);

        CmsMenuDO menu = new CmsMenuDO();
        menu.setId(1L);
        menu.setPath("/menu");

        when(menuService.getMenu(1L)).thenReturn(menu);

        // When
        String result = diyPageService.buildPagePath(page);

        // Then
        assertEquals("/menu/page", result);
    }

    @Test
    void testBuildPagePath_NoMenu() {
        // Given
        CmsDiyPageDO page = new CmsDiyPageDO();
        page.setPath("/page");
        page.setMenuId(null);

        // When
        String result = diyPageService.buildPagePath(page);

        // Then
        assertEquals("/page", result);
    }

    @Test
    void testAsyncRecordPageVisit() {
        // Given
        Long pageId = 100L;
        String userAgent = "Mozilla/5.0";
        String ip = "***********";

        // When
        diyPageService.recordPageVisit(pageId, userAgent, ip);

        // Then
        // 异步方法，无法直接验证，但确保不抛出异常
        assertDoesNotThrow(() -> diyPageService.recordPageVisit(pageId, userAgent, ip));
    }

    @Test
    void testValidatePageForPublication_ValidPage() {
        // Given
        CmsDiyPageDO page = createMockPage(100L, "有效页面");
        page.setContent("{\"components\": [{\"type\": \"text\"}]}");

        // When & Then
        assertDoesNotThrow(() -> diyPageService.validatePageForPublication(page));
    }

    @Test
    void testValidatePageForPublication_EmptyContent() {
        // Given
        CmsDiyPageDO page = createMockPage(100L, "空内容页面");
        page.setContent("");

        // When & Then
        assertThrows(RuntimeException.class, () -> diyPageService.validatePageForPublication(page));
    }

    private CmsDiyPageDO createMockPage(Long id, String name) {
        CmsDiyPageDO page = new CmsDiyPageDO();
        page.setId(id);
        page.setName(name);
        page.setPath("/test/" + name);
        page.setUuid(java.util.UUID.randomUUID().toString());
        page.setContent("{\"components\": []}");
        page.setStatus(CmsDiyPageStatusEnum.DRAFT.getStatus());
        page.setVersion(1);
        page.setCreateTime(LocalDateTime.now());
        page.setUpdateTime(LocalDateTime.now());
        return page;
    }
}