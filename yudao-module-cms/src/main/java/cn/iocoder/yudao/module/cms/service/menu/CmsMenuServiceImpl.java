package cn.iocoder.yudao.module.cms.service.menu;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.menu.vo.CmsMenuListReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.menu.vo.CmsMenuRespVO;
import cn.iocoder.yudao.module.cms.controller.admin.menu.vo.CmsMenuSaveReqVO;
import cn.iocoder.yudao.module.cms.convert.menu.CmsMenuConvert;
import cn.iocoder.yudao.module.cms.dal.dataobject.menu.CmsMenuDO;
import cn.iocoder.yudao.module.cms.dal.mysql.menu.CmsMenuMapper;
import cn.iocoder.yudao.module.cms.framework.cache.core.CmsCacheService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.cms.enums.ErrorCodeConstants.*;

/**
 * CMS菜单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class CmsMenuServiceImpl implements CmsMenuService {

    @Resource
    private CmsMenuMapper cmsMenuMapper;

    @Resource
    private CmsCacheService cacheService;

    @Resource
    private cn.iocoder.yudao.module.cms.dal.mysql.diy.CmsDiyPageMapper cmsDiyPageMapper;

    // ========== 基础CRUD操作 ==========

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createMenu(CmsMenuSaveReqVO createReqVO) {
        // 1. 校验路径唯一性
        validatePathUnique(null, createReqVO.getPath());
        
        // 2. 校验父菜单
        if (createReqVO.getParentId() != null && createReqVO.getParentId() > 0) {
            validateParentMenu(createReqVO.getParentId());
        }

        // 3. 插入菜单
        CmsMenuDO menu = CmsMenuConvert.INSTANCE.convert(createReqVO);
        if (menu.getParentId() == null) {
            menu.setParentId(0L); // 根菜单的父ID设为0
        }
        
        // 4. 计算并设置物化路径
        String materializedPath = calculateMaterializedPath(menu.getParentId(), menu.getPath());
        menu.setMaterializedPath(materializedPath);
        
        cmsMenuMapper.insert(menu);

        // 4. 清除缓存
        clearMenuCache();
        
        log.info("创建菜单成功，菜单ID：{}，菜单名称：{}", menu.getId(), menu.getName());
        return menu.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMenu(CmsMenuSaveReqVO updateReqVO) {
        // 1. 校验菜单存在
        CmsMenuDO existingMenu = validateMenuExists(updateReqVO.getId());
        
        // 2. 校验路径唯一性
        validatePathUnique(updateReqVO.getId(), updateReqVO.getPath());
        
        // 3. 校验父菜单
        if (updateReqVO.getParentId() != null && updateReqVO.getParentId() > 0) {
            validateParentMenu(updateReqVO.getParentId());
            
            // 不能将菜单设置为自己的子菜单
            if (updateReqVO.getParentId().equals(updateReqVO.getId())) {
                throw exception(CMS_MENU_DELETE_FAIL_HAVE_CHILDREN, "不能将菜单设置为自己的子菜单");
            }
            
            // 不能将菜单设置为自己的子孙菜单
            List<Long> childrenIds = getChildrenMenuIds(updateReqVO.getId());
            if (childrenIds.contains(updateReqVO.getParentId())) {
                throw exception(CMS_MENU_DELETE_FAIL_HAVE_CHILDREN, "不能将菜单设置为自己的子孙菜单");
            }
        }

        // 4. 更新菜单
        CmsMenuDO updateObj = CmsMenuConvert.INSTANCE.convert(updateReqVO);
        if (updateObj.getParentId() == null) {
            updateObj.setParentId(0L);
        }

        // 5. 检查是否需要更新物化路径
        boolean needUpdatePath = !Objects.equals(existingMenu.getParentId(), updateReqVO.getParentId()) ||
                                !Objects.equals(existingMenu.getPath(), updateReqVO.getPath());
        
        String oldMaterializedPath = existingMenu.getMaterializedPath();
        if (needUpdatePath) {
            // 计算新的物化路径
            String newMaterializedPath = calculateMaterializedPath(updateObj.getParentId(), updateObj.getPath());
            updateObj.setMaterializedPath(newMaterializedPath);
        }

        cmsMenuMapper.updateById(updateObj);

        // 6. 如果路径发生变化，需要更新所有子菜单的物化路径
        if (needUpdatePath) {
            String newMaterializedPath = updateObj.getMaterializedPath();
            updateChildrenMaterializedPaths(updateReqVO.getId(), oldMaterializedPath, newMaterializedPath);
        }

        // 7. 如果菜单路径发生变化，需要更新关联页面的路径
        if (!Objects.equals(existingMenu.getPath(), updateReqVO.getPath()) || needUpdatePath) {
            updateRelatedPagePaths(updateReqVO.getId(), existingMenu.getMaterializedPath(), 
                                  updateObj.getMaterializedPath());
        }

        // 6. 清除缓存
        clearMenuCache();
        
        log.info("更新菜单成功，菜单ID：{}，菜单名称：{}", updateReqVO.getId(), updateReqVO.getName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMenu(Long id) {
        // 1. 校验菜单存在
        CmsMenuDO menu = validateMenuExists(id);
        
        // 2. 校验是否可以删除
        validateCanDelete(id);

        // 3. 删除菜单
        cmsMenuMapper.deleteById(id);

        // 4. 清除缓存
        clearMenuCache();
        
        log.info("删除菜单成功，菜单ID：{}，菜单名称：{}", id, menu.getName());
    }

    @Override
    public CmsMenuDO getMenu(Long id) {
        return cmsMenuMapper.selectById(id);
    }

    @Override
    public List<CmsMenuDO> getMenuList(CmsMenuListReqVO listReqVO) {
        return cmsMenuMapper.selectList(listReqVO);
    }

    @Override
    public PageResult<CmsMenuDO> getMenuPage(CmsMenuListReqVO pageReqVO) {
        return cmsMenuMapper.selectPage(pageReqVO);
    }

    // ========== 树形结构操作 ==========

    @Override
    public List<CmsMenuRespVO> getMenuTree(CmsMenuListReqVO listReqVO) {
        // 从缓存获取菜单树
        String cacheKey = buildMenuTreeCacheKey(listReqVO);
        return cacheService.getMenuTree(cacheKey, List.class, () -> {
            // 1. 查询所有菜单
            List<CmsMenuDO> menuList = getMenuList(listReqVO);
            if (CollUtil.isEmpty(menuList)) {
                return Collections.emptyList();
            }

            // 2. 转换为VO并构建完整路径
            List<CmsMenuRespVO> menuVOList = CmsMenuConvert.INSTANCE.convertList(menuList);
            for (CmsMenuRespVO menuVO : menuVOList) {
                menuVO.setFullPath(buildMenuPath(menuVO.getId()));
            }

            // 3. 构建树形结构
            return buildMenuTree(menuVOList);
        });
    }

    @Override
    public String buildMenuPath(Long menuId) {
        if (menuId == null || menuId <= 0) {
            return "";
        }

        // 优先使用物化路径，提高性能
        String materializedPath = cmsMenuMapper.selectMaterializedPathById(menuId);
        if (StrUtil.isNotBlank(materializedPath)) {
            // 物化路径已经包含完整路径，确保以/开头
            return materializedPath.startsWith("/") ? materializedPath : "/" + materializedPath;
        }

        // 如果物化路径为空，使用缓存的递归方法作为备用
        return cacheService.get(CmsCacheService.CACHE_MENU, "path:" + menuId, String.class, () -> {
            return buildMenuPathRecursive(menuId);
        });
    }

    @Override
    public List<Long> getChildrenMenuIds(Long parentId) {
        if (parentId == null || parentId <= 0) {
            return Collections.emptyList();
        }
        
        return cmsMenuMapper.selectChildrenIds(parentId);
    }

    // ========== App端接口 ==========

    @Override
    public List<CmsMenuRespVO> getEnabledMenuTree() {
        // 从缓存获取启用的菜单树
        return cacheService.getMenuTree("enabled", List.class, () -> {
            // 1. 查询所有启用的菜单
            List<CmsMenuDO> menuList = cmsMenuMapper.selectEnabledList();
            if (CollUtil.isEmpty(menuList)) {
                return Collections.emptyList();
            }

            // 2. 转换为VO并构建完整路径
            List<CmsMenuRespVO> menuVOList = CmsMenuConvert.INSTANCE.convertList(menuList);
            for (CmsMenuRespVO menuVO : menuVOList) {
                menuVO.setFullPath(buildMenuPath(menuVO.getId()));
            }

            // 3. 构建树形结构
            return buildMenuTree(menuVOList);
        });
    }

    @Override
    public CmsMenuDO getMenuByPath(String path) {
        if (StrUtil.isBlank(path)) {
            return null;
        }
        
        // 从缓存获取菜单
        return cacheService.getPageByPath("menu:" + path, CmsMenuDO.class, () -> {
            return cmsMenuMapper.selectByPath(path);
        });
    }

    // ========== 缓存管理 ==========

    @Override
    public void clearMenuCache() {
        cacheService.evictMenuCache();
        log.debug("清除菜单缓存成功");
    }

    @Override
    public void warmupMenuCache() {
        try {
            // 预热启用菜单树
            getEnabledMenuTree();
            
            // 预热管理端菜单树
            CmsMenuListReqVO listReqVO = new CmsMenuListReqVO();
            getMenuTree(listReqVO);
            
            log.info("菜单缓存预热成功");
        } catch (Exception e) {
            log.error("菜单缓存预热失败", e);
        }
    }

    // ========== 验证方法 ==========

    @Override
    public CmsMenuDO validateMenuExists(Long id) {
        if (id == null) {
            throw exception(CMS_MENU_NOT_EXISTS);
        }
        
        CmsMenuDO menu = cmsMenuMapper.selectById(id);
        if (menu == null) {
            throw exception(CMS_MENU_NOT_EXISTS);
        }
        
        return menu;
    }

    @Override
    public void validatePathUnique(Long id, String path) {
        if (StrUtil.isBlank(path)) {
            return;
        }
        
        CmsMenuDO existingMenu = cmsMenuMapper.selectByPathAndIdNot(path, id);
        if (existingMenu != null) {
            throw exception(CMS_MENU_PATH_DUPLICATE);
        }
    }

    @Override
    public void validateParentMenu(Long parentId) {
        if (parentId == null || parentId <= 0) {
            return;
        }
        
        CmsMenuDO parentMenu = cmsMenuMapper.selectById(parentId);
        if (parentMenu == null) {
            throw exception(CMS_MENU_NOT_EXISTS, "父菜单不存在");
        }
    }

    @Override
    public void validateCanDelete(Long id) {
        // 1. 检查是否存在子菜单
        Long childCount = cmsMenuMapper.selectCountByParentId(id);
        if (childCount > 0) {
            throw exception(CMS_MENU_DELETE_FAIL_HAVE_CHILDREN);
        }

        // 2. 检查是否存在关联页面
        List<cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageDO> relatedPages = 
            cmsDiyPageMapper.selectListByMenuId(id);
        if (CollUtil.isNotEmpty(relatedPages)) {
            // 统计各状态的页面数量，用于详细错误信息
            long publishedCount = relatedPages.stream().filter(page -> Integer.valueOf(1).equals(page.getStatus())).count();
            long draftCount = relatedPages.stream().filter(page -> Integer.valueOf(0).equals(page.getStatus())).count();
            long offlineCount = relatedPages.stream().filter(page -> Integer.valueOf(2).equals(page.getStatus())).count();
            
            String pageDetails = String.format("共%d个页面（已发布:%d，草稿:%d，已下线:%d）", 
                relatedPages.size(), publishedCount, draftCount, offlineCount);
            
            log.warn("菜单删除失败，存在关联页面。菜单ID：{}，{}", id, pageDetails);
            throw exception(CMS_MENU_DELETE_FAIL_HAVE_PAGES);
        }
    }

    // ========== 私有方法 ==========

    /**
     * 构建菜单树缓存键
     */
    private String buildMenuTreeCacheKey(CmsMenuListReqVO listReqVO) {
        StringBuilder keyBuilder = new StringBuilder("tree");
        
        if (StrUtil.isNotBlank(listReqVO.getName())) {
            keyBuilder.append(":name:").append(listReqVO.getName());
        }
        
        if (listReqVO.getStatus() != null) {
            keyBuilder.append(":status:").append(listReqVO.getStatus());
        }
        
        return keyBuilder.toString();
    }

    /**
     * 构建菜单树形结构
     */
    private List<CmsMenuRespVO> buildMenuTree(List<CmsMenuRespVO> menuList) {
        if (CollUtil.isEmpty(menuList)) {
            return Collections.emptyList();
        }

        // 1. 构建ID到菜单的映射
        Map<Long, CmsMenuRespVO> menuMap = menuList.stream()
                .collect(Collectors.toMap(CmsMenuRespVO::getId, menu -> menu));

        // 2. 构建树形结构
        List<CmsMenuRespVO> rootMenus = new ArrayList<>();
        
        for (CmsMenuRespVO menu : menuList) {
            Long parentId = menu.getParentId();
            
            if (parentId == null || parentId == 0) {
                // 根菜单
                rootMenus.add(menu);
            } else {
                // 子菜单
                CmsMenuRespVO parentMenu = menuMap.get(parentId);
                if (parentMenu != null) {
                    if (parentMenu.getChildren() == null) {
                        parentMenu.setChildren(new ArrayList<>());
                    }
                    parentMenu.getChildren().add(menu);
                }
            }
        }

        // 3. 对每个层级的菜单按sort排序
        sortMenuTree(rootMenus);
        
        return rootMenus;
    }

    /**
     * 对菜单树进行排序
     */
    private void sortMenuTree(List<CmsMenuRespVO> menuList) {
        if (CollUtil.isEmpty(menuList)) {
            return;
        }

        // 按sort升序，id降序排序
        menuList.sort((m1, m2) -> {
            int sortCompare = Integer.compare(m1.getSort(), m2.getSort());
            if (sortCompare != 0) {
                return sortCompare;
            }
            return Long.compare(m2.getId(), m1.getId());
        });

        // 递归排序子菜单
        for (CmsMenuRespVO menu : menuList) {
            if (CollUtil.isNotEmpty(menu.getChildren())) {
                sortMenuTree(menu.getChildren());
            }
        }
    }

    /**
     * 递归构建菜单路径
     */
    private String buildMenuPathRecursive(Long menuId) {
        CmsMenuDO menu = cmsMenuMapper.selectById(menuId);
        if (menu == null) {
            return "";
        }

        if (menu.getParentId() == null || menu.getParentId() == 0) {
            // 根菜单
            return "/" + menu.getPath();
        } else {
            // 子菜单，递归构建父路径
            String parentPath = buildMenuPathRecursive(menu.getParentId());
            return parentPath + "/" + menu.getPath();
        }
    }

    /**
     * 批量构建菜单路径（优化性能）
     */
    private void buildMenuPathsBatch(List<CmsMenuRespVO> menuVOList) {
        if (CollUtil.isEmpty(menuVOList)) {
            return;
        }

        // 构建ID到菜单的映射，一次查询避免N+1问题
        Map<Long, CmsMenuDO> allMenuMap = new HashMap<>();
        Set<Long> allMenuIds = new HashSet<>();
        
        // 收集所有需要查询的菜单ID（包括父菜单）
        for (CmsMenuRespVO menuVO : menuVOList) {
            collectParentIds(menuVO.getId(), allMenuIds);
        }
        
        // 批量查询所有相关菜单
        if (!allMenuIds.isEmpty()) {
            List<CmsMenuDO> allMenus = cmsMenuMapper.selectBatchIds(allMenuIds);
            allMenuMap = allMenus.stream().collect(Collectors.toMap(CmsMenuDO::getId, m -> m));
        }

        // 为每个菜单构建路径
        for (CmsMenuRespVO menuVO : menuVOList) {
            String fullPath = buildMenuPathFromMap(menuVO.getId(), allMenuMap);
            menuVO.setFullPath(fullPath);
        }
    }

    /**
     * 收集菜单及其所有父菜单ID
     */
    private void collectParentIds(Long menuId, Set<Long> menuIds) {
        if (menuId == null || menuId <= 0 || menuIds.contains(menuId)) {
            return;
        }
        
        menuIds.add(menuId);
        CmsMenuDO menu = cmsMenuMapper.selectById(menuId);
        if (menu != null && menu.getParentId() != null && menu.getParentId() > 0) {
            collectParentIds(menu.getParentId(), menuIds);
        }
    }

    /**
     * 从内存映射中构建菜单路径
     */
    private String buildMenuPathFromMap(Long menuId, Map<Long, CmsMenuDO> menuMap) {
        if (menuId == null || menuId <= 0) {
            return "";
        }

        CmsMenuDO menu = menuMap.get(menuId);
        if (menu == null) {
            return "";
        }

        if (menu.getParentId() == null || menu.getParentId() == 0) {
            // 根菜单
            return "/" + menu.getPath();
        } else {
            // 子菜单，递归构建父路径
            String parentPath = buildMenuPathFromMap(menu.getParentId(), menuMap);
            return parentPath + "/" + menu.getPath();
        }
    }

    /**
     * 计算菜单的物化路径
     */
    private String calculateMaterializedPath(Long parentId, String currentPath) {
        if (parentId == null || parentId == 0) {
            // 根菜单
            return "/" + currentPath;
        }

        // 获取父菜单的物化路径
        String parentMaterializedPath = cmsMenuMapper.selectMaterializedPathById(parentId);
        if (StrUtil.isNotBlank(parentMaterializedPath)) {
            return parentMaterializedPath + "/" + currentPath;
        }

        // 如果父菜单物化路径为空，递归构建
        return buildMenuPathRecursive(parentId) + "/" + currentPath;
    }

    /**
     * 更新子菜单的物化路径
     */
    private void updateChildrenMaterializedPaths(Long menuId, String oldMaterializedPath, String newMaterializedPath) {
        if (StrUtil.isBlank(oldMaterializedPath) || StrUtil.isBlank(newMaterializedPath)) {
            return;
        }

        // 查询所有子菜单ID
        List<Long> childrenIds = getChildrenMenuIds(menuId);
        if (CollUtil.isEmpty(childrenIds)) {
            return;
        }

        // 批量更新子菜单的物化路径
        cmsMenuMapper.updateChildrenMaterializedPaths(menuId, oldMaterializedPath, newMaterializedPath);
        
        log.info("Updated materialized paths for {} children of menu {}", childrenIds.size(), menuId);
    }

    /**
     * 更新关联页面的路径
     * 当菜单路径发生变化时，需要更新所有关联到该菜单的页面路径
     */
    private void updateRelatedPagePaths(Long menuId, String oldMenuPath, String newMenuPath) {
        if (StrUtil.isBlank(oldMenuPath) && StrUtil.isBlank(newMenuPath)) {
            return;
        }

        try {
            // 查询所有关联到该菜单的页面
            List<cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageDO> relatedPages = 
                cmsDiyPageMapper.selectListByMenuId(menuId);
            
            if (CollUtil.isEmpty(relatedPages)) {
                log.debug("菜单{}没有关联的页面，无需更新页面路径", menuId);
                return;
            }

            // 构建页面路径更新列表
            List<cn.iocoder.yudao.module.cms.service.menu.dto.PagePathUpdateDTO> pageUpdates = new ArrayList<>();
            
            for (cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageDO page : relatedPages) {
                // 构建新的完整页面路径
                String newFullPath = buildNewPagePath(newMenuPath, page);
                
                if (!Objects.equals(page.getPath(), newFullPath)) {
                    pageUpdates.add(new cn.iocoder.yudao.module.cms.service.menu.dto.PagePathUpdateDTO(
                        page.getId(), newFullPath, page.getPath()));
                }
            }

            // 批量更新页面路径
            if (CollUtil.isNotEmpty(pageUpdates)) {
                cmsDiyPageMapper.batchUpdatePagePaths(pageUpdates);
                
                // 清除相关页面缓存
                for (cn.iocoder.yudao.module.cms.service.menu.dto.PagePathUpdateDTO update : pageUpdates) {
                    cacheService.evict(CmsCacheService.CACHE_PAGE_PATH, update.getOriginalPath());
                    cacheService.evict(CmsCacheService.CACHE_PAGE_PATH, update.getNewFullPath());
                }
                
                log.info("菜单路径变更完成，已更新{}个关联页面的路径。菜单ID：{}，旧路径：{}，新路径：{}", 
                        pageUpdates.size(), menuId, oldMenuPath, newMenuPath);
            }
            
        } catch (Exception e) {
            log.error("更新关联页面路径失败，菜单ID：{}，旧路径：{}，新路径：{}", 
                    menuId, oldMenuPath, newMenuPath, e);
            // 不抛出异常，避免影响菜单更新的主流程
        }
    }

    /**
     * 构建新的页面路径
     * 页面路径 = 菜单完整路径 + 页面相对路径（如果页面有父页面关系，还需要考虑父页面路径）
     */
    private String buildNewPagePath(String newMenuPath, cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageDO page) {
        if (StrUtil.isBlank(newMenuPath)) {
            return page.getPath(); // 如果菜单路径为空，保持原页面路径
        }
        
        // 确保菜单路径以/开头
        String menuPath = newMenuPath.startsWith("/") ? newMenuPath : "/" + newMenuPath;
        
        // 获取页面的相对路径（去掉原来的菜单路径前缀）
        String pagePath = page.getPath();
        if (StrUtil.isNotBlank(pagePath)) {
            // 如果页面路径以/开头，直接使用，否则加上/前缀
            if (!pagePath.startsWith("/")) {
                pagePath = "/" + pagePath;
            }
            
            // 组合新路径：菜单路径 + 页面路径
            // 避免重复的/符号
            if (menuPath.endsWith("/") && pagePath.startsWith("/")) {
                return menuPath + pagePath.substring(1);
            } else if (!menuPath.endsWith("/") && !pagePath.startsWith("/")) {
                return menuPath + "/" + pagePath;
            } else {
                return menuPath + pagePath;
            }
        }
        
        return menuPath;
    }

}