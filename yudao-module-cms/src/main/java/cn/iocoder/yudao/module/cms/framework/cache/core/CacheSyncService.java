package cn.iocoder.yudao.module.cms.framework.cache.core;

import cn.hutool.core.util.IdUtil;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Service;


/**
 * 缓存同步服务
 * 
 * 基于 Redis Pub/Sub 实现分布式缓存同步：
 * 1. 当某个实例更新缓存时，发布同步消息
 * 2. 其他实例接收消息后，清除本地 L1 缓存
 * 3. 避免分布式环境下的缓存不一致问题
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CacheSyncService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final RedisMessageListenerContainer messageListenerContainer;
    private final MultiLevelCacheManager cacheManager;
    private final String instanceId;

    // Redis 频道常量
    private static final String CACHE_SYNC_CHANNEL = "cms:cache:sync";
    private static final String CACHE_EVICT_CHANNEL = "cms:cache:evict";
    private static final String CACHE_CLEAR_CHANNEL = "cms:cache:clear";

    public CacheSyncService(RedisTemplate<String, Object> redisTemplate,
                           RedisMessageListenerContainer messageListenerContainer,
                           MultiLevelCacheManager cacheManager) {
        this.redisTemplate = redisTemplate;
        this.messageListenerContainer = messageListenerContainer;
        this.cacheManager = cacheManager;
        this.instanceId = IdUtil.fastSimpleUUID();
    }

    @PostConstruct
    public void init() {
        // 订阅缓存同步消息
        messageListenerContainer.addMessageListener((message, pattern) -> {
            try {
                CacheSyncMessage syncMessage = (CacheSyncMessage) redisTemplate
                        .getValueSerializer().deserialize(message.getBody());
                
                if (syncMessage != null && !instanceId.equals(syncMessage.getInstanceId())) {
                    handleSyncMessage(syncMessage);
                }
            } catch (Exception e) {
                log.error("Failed to handle cache sync message", e);
            }
        }, new ChannelTopic(CACHE_SYNC_CHANNEL));

        // 订阅缓存驱逐消息
        messageListenerContainer.addMessageListener((message, pattern) -> {
            try {
                CacheSyncMessage syncMessage = (CacheSyncMessage) redisTemplate
                        .getValueSerializer().deserialize(message.getBody());
                
                if (syncMessage != null && !instanceId.equals(syncMessage.getInstanceId())) {
                    handleEvictMessage(syncMessage);
                }
            } catch (Exception e) {
                log.error("Failed to handle cache evict message", e);
            }
        }, new ChannelTopic(CACHE_EVICT_CHANNEL));

        // 订阅缓存清除消息
        messageListenerContainer.addMessageListener((message, pattern) -> {
            try {
                CacheSyncMessage syncMessage = (CacheSyncMessage) redisTemplate
                        .getValueSerializer().deserialize(message.getBody());
                
                if (syncMessage != null && !instanceId.equals(syncMessage.getInstanceId())) {
                    handleClearMessage(syncMessage);
                }
            } catch (Exception e) {
                log.error("Failed to handle cache clear message", e);
            }
        }, new ChannelTopic(CACHE_CLEAR_CHANNEL));

        log.info("Cache sync service initialized with instance ID: {}", instanceId);
    }

    @PreDestroy
    public void destroy() {
        log.info("Cache sync service destroyed for instance: {}", instanceId);
    }

    /**
     * 发布缓存更新消息
     */
    public void publishCacheUpdate(String cacheName, Object key, Object value) {
        CacheSyncMessage message = CacheSyncMessage.builder()
                .instanceId(instanceId)
                .tenantId(TenantContextHolder.getTenantId())
                .cacheName(cacheName)
                .key(key)
                .value(value)
                .operation(CacheOperation.PUT)
                .timestamp(System.currentTimeMillis())
                .build();

        publishMessage(CACHE_SYNC_CHANNEL, message);
        log.debug("Published cache update message: cacheName={}, key={}", cacheName, key);
    }

    /**
     * 发布缓存驱逐消息
     */
    public void publishCacheEvict(String cacheName, Object key) {
        CacheSyncMessage message = CacheSyncMessage.builder()
                .instanceId(instanceId)
                .tenantId(TenantContextHolder.getTenantId())
                .cacheName(cacheName)
                .key(key)
                .operation(CacheOperation.EVICT)
                .timestamp(System.currentTimeMillis())
                .build();

        publishMessage(CACHE_EVICT_CHANNEL, message);
        log.debug("Published cache evict message: cacheName={}, key={}", cacheName, key);
    }

    /**
     * 发布缓存清除消息
     */
    public void publishCacheClear(String cacheName) {
        CacheSyncMessage message = CacheSyncMessage.builder()
                .instanceId(instanceId)
                .tenantId(TenantContextHolder.getTenantId())
                .cacheName(cacheName)
                .operation(CacheOperation.CLEAR)
                .timestamp(System.currentTimeMillis())
                .build();

        publishMessage(CACHE_CLEAR_CHANNEL, message);
        log.debug("Published cache clear message: cacheName={}", cacheName);
    }

    /**
     * 发布消息到 Redis
     */
    private void publishMessage(String channel, CacheSyncMessage message) {
        try {
            redisTemplate.convertAndSend(channel, message);
        } catch (Exception e) {
            log.error("Failed to publish cache sync message to channel: {}", channel, e);
        }
    }

    /**
     * 处理缓存同步消息
     */
    private void handleSyncMessage(CacheSyncMessage message) {
        try {
            // 设置租户上下文
            if (message.getTenantId() != null) {
                TenantContextHolder.setTenantId(message.getTenantId());
            }

            MultiLevelCache cache = (MultiLevelCache) cacheManager.getCache(message.getCacheName());
            if (cache != null && message.getKey() != null && message.getValue() != null) {
                // 只更新 L1 缓存，L2 缓存已经由发送方更新
                cache.getL1Cache().put(message.getKey(), message.getValue());
                log.debug("Synced cache update: cacheName={}, key={}", 
                         message.getCacheName(), message.getKey());
            }
        } catch (Exception e) {
            log.error("Failed to handle sync message: {}", message, e);
        } finally {
            TenantContextHolder.clear();
        }
    }

    /**
     * 处理缓存驱逐消息
     */
    private void handleEvictMessage(CacheSyncMessage message) {
        try {
            // 设置租户上下文
            if (message.getTenantId() != null) {
                TenantContextHolder.setTenantId(message.getTenantId());
            }

            MultiLevelCache cache = (MultiLevelCache) cacheManager.getCache(message.getCacheName());
            if (cache != null && message.getKey() != null) {
                // 只清除 L1 缓存，L2 缓存已经由发送方清除
                cache.getL1Cache().evict(message.getKey());
                log.debug("Synced cache evict: cacheName={}, key={}", 
                         message.getCacheName(), message.getKey());
            }
        } catch (Exception e) {
            log.error("Failed to handle evict message: {}", message, e);
        } finally {
            TenantContextHolder.clear();
        }
    }

    /**
     * 处理缓存清除消息
     */
    private void handleClearMessage(CacheSyncMessage message) {
        try {
            // 设置租户上下文
            if (message.getTenantId() != null) {
                TenantContextHolder.setTenantId(message.getTenantId());
            }

            MultiLevelCache cache = (MultiLevelCache) cacheManager.getCache(message.getCacheName());
            if (cache != null) {
                // 只清除 L1 缓存，L2 缓存已经由发送方清除
                cache.getL1Cache().clear();
                log.debug("Synced cache clear: cacheName={}", message.getCacheName());
            }
        } catch (Exception e) {
            log.error("Failed to handle clear message: {}", message, e);
        } finally {
            TenantContextHolder.clear();
        }
    }

    /**
     * 获取实例 ID
     */
    public String getInstanceId() {
        return instanceId;
    }
}