package cn.iocoder.yudao.module.cms.service.car;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelSpecCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelSpecPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelSpecUpdateReqVO;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarModelSpecDO;
import cn.iocoder.yudao.module.cms.dal.mysql.car.CarModelSpecMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.cms.enums.ErrorCodeConstants.CAR_MODEL_SPEC_NOT_EXISTS;

/**
 * 车型规格管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CarModelSpecServiceImpl implements CarModelSpecService {

    @Resource
    private CarModelSpecMapper carModelSpecMapper;

    @Override
    public Long createCarModelSpec(CarModelSpecCreateReqVO createReqVO) {
        // 插入
        CarModelSpecDO carModelSpec = BeanUtils.toBean(createReqVO, CarModelSpecDO.class);
        carModelSpecMapper.insert(carModelSpec);
        // 返回
        return carModelSpec.getId();
    }

    @Override
    public void updateCarModelSpec(CarModelSpecUpdateReqVO updateReqVO) {
        // 校验存在
        validateCarModelSpecExists(updateReqVO.getId());

        // 更新
        CarModelSpecDO updateObj = BeanUtils.toBean(updateReqVO, CarModelSpecDO.class);
        carModelSpecMapper.updateById(updateObj);
    }

    @Override
    public void deleteCarModelSpec(Long id) {
        // 校验存在
        validateCarModelSpecExists(id);
        // 删除
        carModelSpecMapper.deleteById(id);
    }

    private void validateCarModelSpecExists(Long id) {
        if (carModelSpecMapper.selectById(id) == null) {
            throw exception(CAR_MODEL_SPEC_NOT_EXISTS);
        }
    }

    @Override
    public CarModelSpecDO getCarModelSpec(Long id) {
        return carModelSpecMapper.selectById(id);
    }

    @Override
    public PageResult<CarModelSpecDO> getCarModelSpecPage(CarModelSpecPageReqVO pageReqVO) {
        return carModelSpecMapper.selectPage(pageReqVO);
    }

    @Override
    public List<CarModelSpecDO> getCarModelSpecList(Long modelId, String category, Integer status) {
        return carModelSpecMapper.selectListByModelIdAndCategoryAndStatus(modelId, category, status);
    }

    @Override
    public List<CarModelSpecDO> getCarModelSpecListByGroup(Long modelId, String specGroup) {
        return carModelSpecMapper.selectListByModelIdAndSpecGroup(modelId, specGroup);
    }

    @Override
    public void batchCreateCarModelSpec(Long modelId, List<CarModelSpecCreateReqVO> createReqVOList) {
        List<CarModelSpecDO> specList = BeanUtils.toBean(createReqVOList, CarModelSpecDO.class);
        // 设置车型ID
        specList.forEach(spec -> spec.setModelId(modelId));
        // 批量插入
        carModelSpecMapper.insertBatch(specList);
    }

}