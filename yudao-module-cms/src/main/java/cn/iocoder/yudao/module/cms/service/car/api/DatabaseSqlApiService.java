package cn.iocoder.yudao.module.cms.service.car.api;

import cn.iocoder.yudao.module.cms.dal.dataobject.car.api.ApiSqlConfigDO;
import cn.iocoder.yudao.module.cms.dal.mysql.car.api.ApiSqlConfigMapper;
import cn.iocoder.yudao.module.cms.framework.car.constant.CarCacheConstants;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.*;

/**
 * 数据库SQL API服务
 * 基于数据库配置的SQL执行和版本管理
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class DatabaseSqlApiService {

    @Resource
    private ApiSqlConfigMapper apiSqlConfigMapper;

    @Resource
    private SqlExecutionEngine sqlExecutionEngine;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 获取API SQL配置
     *
     * @param apiCode API标识码
     * @param version 版本号，为空时使用默认版本
     * @return API配置
     */
//    @Cacheable(cacheNames = CarCacheConstants.API_SQL_CONFIG,
//               key = "T(cn.iocoder.yudao.module.cms.framework.car.util.CarCacheHelper).generateKey(#apiCode, #version != null ? #version : 'default')")
    public ApiSqlConfigDO getApiSqlConfig(String apiCode, String version) {
        // 1. 尝试获取指定版本
        if (StringUtils.hasText(version)) {
            ApiSqlConfigDO config = apiSqlConfigMapper.selectByApiCodeAndVersion(apiCode, version);
            if (config != null && config.getStatus() == 0) {
                log.debug("获取到指定版本API配置: {}-{}", apiCode, version);
                return config;
            }
        }

        // 2. 获取默认版本
        ApiSqlConfigDO defaultConfig = apiSqlConfigMapper.selectByApiCodeAndIsDefault(apiCode, true);
        if (defaultConfig != null && defaultConfig.getStatus() == 0) {
            log.debug("获取到默认版本API配置: {}", apiCode);
            return defaultConfig;
        }

        throw new RuntimeException("未找到API配置: " + apiCode + ", version: " + version);
    }

    /**
     * 执行API SQL查询
     *
     * @param apiCode API标识码
     * @param version 版本号
     * @param params 参数
     * @return 执行结果
     */
//    @Cacheable(cacheNames = CarCacheConstants.API_SQL_RESULT,
//               key = "T(cn.iocoder.yudao.module.cms.framework.car.util.CarCacheHelper).generateKey(#apiCode, #version != null ? #version : 'default', #params.hashCode())",
//               condition = "#params.size() <= 10") // 只有当参数不太多时才缓存
    public Map<String, Object> executeApiSql(String apiCode, String version, Map<String, Object> params) {
        try {
            // 1. 获取SQL配置
            ApiSqlConfigDO config = getApiSqlConfig(apiCode, version);
            
            log.info("执行API: {}-{}, 参数: {}", apiCode, version, params);
            
            // 2. 执行SQL
            List<Map<String, Object>> results = sqlExecutionEngine.executeQuery(
                config.getSqlContent(), params);
            
            if (results.isEmpty()) {
                log.warn("API查询无结果: {}-{}", apiCode, version);
                return Collections.emptyMap();
            }
            
            // 3. 处理响应
            Map<String, Object> result = results.get(0);
            if (result.containsKey("api_response")) {
                // 如果SQL返回的是JSON格式的api_response字段
                Object jsonResponse = result.get("api_response");
                if (jsonResponse instanceof String) {
                    return parseJsonToMap((String) jsonResponse);
                } else {
                    // 已经是Map类型，直接返回
                    return (Map<String, Object>) jsonResponse;
                }
            } else {
                // 如果SQL返回的是普通字段，直接返回
                return result;
            }
            
        } catch (Exception e) {
            log.error("执行API SQL失败: apiCode={}, version={}", apiCode, version, e);
            throw new RuntimeException("API执行失败: " + e.getMessage());
        }
    }

    /**
     * 测试API SQL配置
     *
     * @param configId 配置ID
     * @param params 测试参数
     * @return 测试结果
     */
    public Map<String, Object> testApiSqlConfig(Long configId, Map<String, Object> params) {
        try {
            ApiSqlConfigDO config = apiSqlConfigMapper.selectById(configId);
            if (config == null) {
                throw new RuntimeException("API配置不存在: " + configId);
            }

            // 验证SQL格式
            if (!sqlExecutionEngine.validateSqlFormat(config.getSqlContent())) {
                throw new RuntimeException("SQL格式验证失败");
            }

            // 执行测试查询
            List<Map<String, Object>> results = sqlExecutionEngine.executeQuery(
                config.getSqlContent(), params);

            Map<String, Object> testResult = new HashMap<>();
            testResult.put("success", true);
            testResult.put("rowCount", results.size());
            testResult.put("data", results.isEmpty() ? Collections.emptyMap() : results.get(0));
            
            return testResult;
            
        } catch (Exception e) {
            log.error("测试API SQL配置失败: configId={}", configId, e);
            Map<String, Object> testResult = new HashMap<>();
            testResult.put("success", false);
            testResult.put("error", e.getMessage());
            return testResult;
        }
    }

    /**
     * 验证API配置
     *
     * @param apiCode API代码
     * @param sqlContent SQL内容
     * @return 验证结果
     */
    public boolean validateApiConfig(String apiCode, String sqlContent) {
        try {
            // 1. 验证SQL格式
            if (!sqlExecutionEngine.validateSqlFormat(sqlContent)) {
                log.warn("API配置SQL格式无效: {}", apiCode);
                return false;
            }

            // 2. 检查参数
            List<String> paramNames = sqlExecutionEngine.extractParameterNames(sqlContent);
            log.debug("API配置参数列表: {} - {}", apiCode, paramNames);

            return true;
            
        } catch (Exception e) {
            log.error("验证API配置失败: {}", apiCode, e);
            return false;
        }
    }

    /**
     * 获取API配置的参数列表
     *
     * @param apiCode API代码
     * @param version 版本
     * @return 参数列表
     */
    public List<String> getApiParameters(String apiCode, String version) {
        try {
            ApiSqlConfigDO config = getApiSqlConfig(apiCode, version);
            return sqlExecutionEngine.extractParameterNames(config.getSqlContent());
        } catch (Exception e) {
            log.error("获取API参数列表失败: {}-{}", apiCode, version, e);
            return Collections.emptyList();
        }
    }

    /**
     * 刷新API配置缓存
     *
     * @param apiCode API代码
     */
    @CacheEvict(cacheNames = {CarCacheConstants.API_SQL_CONFIG, CarCacheConstants.API_SQL_RESULT}, allEntries = true)
    public void refreshApiCache(String apiCode) {
        // 清理相关缓存
        log.info("刷新API缓存: {}", apiCode);
    }

    /**
     * 解析JSON字符串为Map
     */
    private Map<String, Object> parseJsonToMap(String jsonStr) {
        try {
            if (!StringUtils.hasText(jsonStr)) {
                return Collections.emptyMap();
            }
            return objectMapper.readValue(jsonStr, Map.class);
        } catch (Exception e) {
            log.error("解析JSON失败: {}", jsonStr, e);
            return Collections.emptyMap();
        }
    }

    /**
     * 获取API配置统计信息
     */
    public Map<String, Object> getApiStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 获取配置总数
            Long totalConfigs = apiSqlConfigMapper.selectCount(null);
            stats.put("totalConfigs", totalConfigs);
            
            // 获取启用配置数
            stats.put("enabledConfigs", apiSqlConfigMapper.selectCountByStatus(0));
            
            // 获取SQL缓存统计
            stats.putAll(sqlExecutionEngine.getCacheStats());
            
            return stats;
            
        } catch (Exception e) {
            log.error("获取API统计信息失败", e);
            return Collections.emptyMap();
        }
    }
}