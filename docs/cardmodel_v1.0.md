# 车型车系管理系统设计文档 v1.0

## 1. 系统概述

基于现有的静态数据结构，设计一套完整的车型车系管理系统，支持动态配置和API接口服务，满足管理端和App端的所有业务需求。

## 2. 现有数据结构分析

### 2.1 核心数据模型

系统包含以下核心数据模型：

1. **CarModel（车型基础信息）**
   - 基础信息：id, name, category, description, basePrice, image
   - 技术规格：specs（包含engine, engineType, fuelConsumption, power, torque, seatsNum）
   - 融资信息：financing（包含weeklyPayment, comparisonRate, deposit, term, gfv, kmAllowance）
   - 配置包：packages（多个配置包选项）
   - 颜色选项：colors（外观颜色选项）
   - 内饰选项：interiors（内饰颜色选项）
   - 轮毂选项：wheels（轮毂配置选项）

2. **CarModelTab（车型标签分类）**
   - 标签信息：id, label
   - 对应车型列表

3. **CarModelCard（车型卡片展示）**
   - 展示信息：name, description, priceType, price, image, link
   - 融资信息：comparisonRate, deposit, term, gfv

## 3. 数据库设计

### 3.1 数据库表结构

```sql
-- 1. 车系表（car_series）
CREATE TABLE car_series (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  code VARCHAR(50) UNIQUE NOT NULL COMMENT '车系代码，如：tiggo',
  name VARCHAR(100) NOT NULL COMMENT '车系名称，如：Tiggo',
  description TEXT COMMENT '车系描述',
  display_order INT DEFAULT 0 COMMENT '显示顺序',
  status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL,
  INDEX idx_code (code),
  INDEX idx_status (status)
) COMMENT='车系表';

-- 2. 车型基础信息表（car_models）
CREATE TABLE car_models (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  series_id BIGINT NOT NULL COMMENT '车系ID',
  code VARCHAR(100) UNIQUE NOT NULL COMMENT '车型代码，如：tiggo-7',
  name VARCHAR(200) NOT NULL COMMENT '车型名称',
  category VARCHAR(100) COMMENT '车型分类',
  description TEXT COMMENT '车型描述',
  base_price DECIMAL(10,2) COMMENT '基础价格',
  currency VARCHAR(10) DEFAULT 'GBP' COMMENT '货币单位',
  image_url VARCHAR(500) COMMENT '主图URL',
  ev_icon_url VARCHAR(500) COMMENT '电动标识图标',
  badge VARCHAR(50) COMMENT '标识：Value, Luxury等',
  end_date DATE COMMENT '优惠截止日期',
  display_order INT DEFAULT 0 COMMENT '显示顺序',
  status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  is_hybrid BOOLEAN DEFAULT FALSE COMMENT '是否混动车型',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL,
  FOREIGN KEY (series_id) REFERENCES car_series(id),
  INDEX idx_series (series_id),
  INDEX idx_code (code),
  INDEX idx_status (status)
) COMMENT='车型基础信息表';

-- 3. 车型技术规格表（car_specs）
CREATE TABLE car_specs (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  model_id BIGINT NOT NULL COMMENT '车型ID',
  engine VARCHAR(100) COMMENT '发动机',
  engine_type VARCHAR(50) COMMENT '发动机类型',
  fuel_consumption VARCHAR(50) COMMENT '油耗',
  power VARCHAR(50) COMMENT '功率',
  torque VARCHAR(50) COMMENT '扭矩',
  seats_num VARCHAR(20) COMMENT '座位数',
  transmission VARCHAR(100) COMMENT '变速箱',
  drivetrain VARCHAR(50) COMMENT '驱动方式',
  length INT COMMENT '长度(mm)',
  width INT COMMENT '宽度(mm)',
  height INT COMMENT '高度(mm)',
  wheelbase INT COMMENT '轴距(mm)',
  luggage_capacity INT COMMENT '行李箱容量(L)',
  battery_capacity VARCHAR(50) COMMENT '电池容量(kWh)',
  electric_range VARCHAR(50) COMMENT '纯电续航(km)',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (model_id) REFERENCES car_models(id),
  UNIQUE KEY uk_model (model_id)
) COMMENT='车型技术规格表';

-- 4. 车型融资信息表（car_financing）
CREATE TABLE car_financing (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  model_id BIGINT NOT NULL COMMENT '车型ID',
  weekly_payment VARCHAR(50) COMMENT '每周付款',
  comparison_rate VARCHAR(50) COMMENT '比较利率',
  deposit VARCHAR(50) COMMENT '押金/首付',
  term VARCHAR(50) COMMENT '期限',
  gfv VARCHAR(50) COMMENT '保证未来价值',
  km_allowance VARCHAR(50) COMMENT '公里数限制',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (model_id) REFERENCES car_models(id),
  UNIQUE KEY uk_model (model_id)
) COMMENT='车型融资信息表';

-- 5. 配置包表（car_packages）
CREATE TABLE car_packages (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  model_id BIGINT NOT NULL COMMENT '车型ID',
  code VARCHAR(100) NOT NULL COMMENT '配置包代码',
  name VARCHAR(200) NOT NULL COMMENT '配置包名称',
  price DECIMAL(10,2) DEFAULT 0 COMMENT '配置包价格',
  features_title VARCHAR(200) COMMENT '特性标题文案',
  display_order INT DEFAULT 0 COMMENT '显示顺序',
  status TINYINT DEFAULT 1 COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (model_id) REFERENCES car_models(id),
  INDEX idx_model (model_id),
  INDEX idx_code (code)
) COMMENT='配置包表';

-- 6. 配置包特性表（package_features）
CREATE TABLE package_features (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  package_id BIGINT NOT NULL COMMENT '配置包ID',
  feature_name VARCHAR(200) NOT NULL COMMENT '特性名称',
  is_included BOOLEAN DEFAULT TRUE COMMENT '是否包含',
  display_order INT DEFAULT 0 COMMENT '显示顺序',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (package_id) REFERENCES car_packages(id),
  INDEX idx_package (package_id)
) COMMENT='配置包特性表';

-- 7. 车型颜色选项表（car_colors）
CREATE TABLE car_colors (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  model_id BIGINT NOT NULL COMMENT '车型ID',
  code VARCHAR(50) NOT NULL COMMENT '颜色代码',
  name VARCHAR(100) NOT NULL COMMENT '颜色名称',
  price DECIMAL(10,2) DEFAULT 0 COMMENT '加价',
  image_url VARCHAR(500) COMMENT '颜色图片URL',
  color_code JSON COMMENT '颜色代码数组',
  secondary_color_code JSON COMMENT '第二色颜色代码(双色调)',
  is_two_tone BOOLEAN DEFAULT FALSE COMMENT '是否双色调',
  is_premium BOOLEAN DEFAULT FALSE COMMENT '是否高级颜色',
  required_package VARCHAR(100) COMMENT '需要的配置包',
  display_order INT DEFAULT 0 COMMENT '显示顺序',
  status TINYINT DEFAULT 1 COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (model_id) REFERENCES car_models(id),
  INDEX idx_model (model_id)
) COMMENT='车型颜色选项表';

-- 8. 车型内饰选项表（car_interiors）
CREATE TABLE car_interiors (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  model_id BIGINT NOT NULL COMMENT '车型ID',
  code VARCHAR(50) NOT NULL COMMENT '内饰代码',
  name VARCHAR(100) NOT NULL COMMENT '内饰名称',
  price DECIMAL(10,2) DEFAULT 0 COMMENT '加价',
  image_url VARCHAR(500) COMMENT '内饰图片URL',
  color_code JSON COMMENT '颜色代码数组',
  required_package VARCHAR(100) COMMENT '需要的配置包',
  display_order INT DEFAULT 0 COMMENT '显示顺序',
  status TINYINT DEFAULT 1 COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (model_id) REFERENCES car_models(id),
  INDEX idx_model (model_id)
) COMMENT='车型内饰选项表';

-- 9. 车型轮毂选项表（car_wheels）
CREATE TABLE car_wheels (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  model_id BIGINT NOT NULL COMMENT '车型ID',
  code VARCHAR(50) NOT NULL COMMENT '轮毂代码',
  name VARCHAR(100) NOT NULL COMMENT '轮毂名称',
  size VARCHAR(50) NOT NULL COMMENT '轮毂尺寸',
  price DECIMAL(10,2) DEFAULT 0 COMMENT '加价',
  image_url VARCHAR(500) COMMENT '轮毂图片URL',
  required_package VARCHAR(100) COMMENT '需要的配置包',
  display_order INT DEFAULT 0 COMMENT '显示顺序',
  status TINYINT DEFAULT 1 COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (model_id) REFERENCES car_models(id),
  INDEX idx_model (model_id)
) COMMENT='车型轮毂选项表';

-- 10. 车型标签分类表（car_model_tabs）
CREATE TABLE car_model_tabs (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  code VARCHAR(50) UNIQUE NOT NULL COMMENT '标签代码',
  label VARCHAR(100) NOT NULL COMMENT '标签名称',
  display_order INT DEFAULT 0 COMMENT '显示顺序',
  status TINYINT DEFAULT 1 COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_code (code)
) COMMENT='车型标签分类表';

-- 11. 车型标签关联表（model_tab_relations）
CREATE TABLE model_tab_relations (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tab_id BIGINT NOT NULL COMMENT '标签ID',
  model_id BIGINT NOT NULL COMMENT '车型ID',
  display_order INT DEFAULT 0 COMMENT '显示顺序',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (tab_id) REFERENCES car_model_tabs(id),
  FOREIGN KEY (model_id) REFERENCES car_models(id),
  UNIQUE KEY uk_tab_model (tab_id, model_id),
  INDEX idx_tab (tab_id),
  INDEX idx_model (model_id)
) COMMENT='车型标签关联表';

-- 12. 车型卡片展示信息表（car_model_cards）
CREATE TABLE car_model_cards (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  model_id BIGINT NOT NULL COMMENT '车型ID',
  tab_id BIGINT COMMENT '所属标签ID',
  name VARCHAR(200) COMMENT '展示名称',
  description VARCHAR(500) COMMENT '描述',
  price_type ENUM('weekly', 'driveaway') DEFAULT 'driveaway' COMMENT '价格类型',
  price VARCHAR(50) COMMENT '价格',
  price_footnote VARCHAR(50) COMMENT '价格备注',
  comparison_rate VARCHAR(50) COMMENT '比较利率',
  comparison_footnote VARCHAR(50) COMMENT '利率备注',
  deposit VARCHAR(50) COMMENT '押金',
  term VARCHAR(50) COMMENT '期限',
  gfv_value VARCHAR(50) COMMENT 'GFV值',
  gfv_footnote VARCHAR(50) COMMENT 'GFV备注',
  gfv_details VARCHAR(500) COMMENT 'GFV详情',
  available_for VARCHAR(500) COMMENT '适用说明',
  image_url VARCHAR(500) COMMENT '卡片图片',
  link_url VARCHAR(500) COMMENT '链接地址',
  display_order INT DEFAULT 0 COMMENT '显示顺序',
  status TINYINT DEFAULT 1 COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (model_id) REFERENCES car_models(id),
  FOREIGN KEY (tab_id) REFERENCES car_model_tabs(id),
  INDEX idx_model (model_id),
  INDEX idx_tab (tab_id)
) COMMENT='车型卡片展示信息表';

-- 13. 车型图片轮播表（car_model_images）
CREATE TABLE car_model_images (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  model_id BIGINT NOT NULL COMMENT '车型ID',
  image_type VARCHAR(50) NOT NULL COMMENT '图片类型：carousel/hero/design等',
  image_url VARCHAR(500) NOT NULL COMMENT '图片URL',
  mobile_image_url VARCHAR(500) COMMENT '移动端图片URL',
  title VARCHAR(200) COMMENT '图片标题',
  description TEXT COMMENT '图片描述',
  label VARCHAR(100) COMMENT '标签',
  display_order INT DEFAULT 0 COMMENT '显示顺序',
  status TINYINT DEFAULT 1 COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (model_id) REFERENCES car_models(id),
  INDEX idx_model (model_id),
  INDEX idx_type (image_type)
) COMMENT='车型图片轮播表';

-- 14. 车型特性亮点表（car_model_features）
CREATE TABLE car_model_features (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  model_id BIGINT NOT NULL COMMENT '车型ID',
  feature_type VARCHAR(50) NOT NULL COMMENT '特性类型：design/technology/safety等',
  title VARCHAR(200) NOT NULL COMMENT '特性标题',
  description TEXT COMMENT '特性描述',
  image_url VARCHAR(500) COMMENT '特性图片',
  icon_url VARCHAR(500) COMMENT '图标URL',
  display_order INT DEFAULT 0 COMMENT '显示顺序',
  status TINYINT DEFAULT 1 COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (model_id) REFERENCES car_models(id),
  INDEX idx_model (model_id),
  INDEX idx_type (feature_type)
) COMMENT='车型特性亮点表';

-- 15. 融资方案选项表（finance_options）
CREATE TABLE finance_options (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  code VARCHAR(50) UNIQUE NOT NULL COMMENT '融资方案代码',
  name VARCHAR(100) NOT NULL COMMENT '方案名称',
  description VARCHAR(500) COMMENT '方案描述',
  display_order INT DEFAULT 0 COMMENT '显示顺序',
  status TINYINT DEFAULT 1 COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT='融资方案选项表';

-- 16. 融资期限选项表（finance_terms）
CREATE TABLE finance_terms (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  option_id BIGINT NOT NULL COMMENT '融资方案ID',
  term_code VARCHAR(50) NOT NULL COMMENT '期限代码',
  term_name VARCHAR(100) NOT NULL COMMENT '期限名称',
  rate DECIMAL(5,4) NOT NULL COMMENT '利率',
  display_order INT DEFAULT 0 COMMENT '显示顺序',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (option_id) REFERENCES finance_options(id),
  INDEX idx_option (option_id)
) COMMENT='融资期限选项表';

-- 17. 融资首付选项表（finance_down_payments）
CREATE TABLE finance_down_payments (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  option_id BIGINT NOT NULL COMMENT '融资方案ID',
  payment_code VARCHAR(50) NOT NULL COMMENT '首付代码',
  payment_name VARCHAR(100) NOT NULL COMMENT '首付名称',
  value DECIMAL(5,4) NOT NULL COMMENT '首付比例',
  display_order INT DEFAULT 0 COMMENT '显示顺序',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (option_id) REFERENCES finance_options(id),
  INDEX idx_option (option_id)
) COMMENT='融资首付选项表';
```

### 3.2 数据字典

#### 状态码定义
- 0: 禁用/删除
- 1: 启用/正常

#### 价格类型
- weekly: 每周付款
- driveaway: 落地价

#### 图片类型（image_type）
- main: 主图
- carousel: 轮播图
- color: 颜色展示图
- interior: 内饰图
- wheel: 轮毂图
- feature: 特性展示图

#### 特性类型（feature_type）
- design: 设计特性
- technology: 技术特性
- safety: 安全特性
- comfort: 舒适特性
- performance: 性能特性

## 4. API接口设计

### 4.1 管理端API

#### 4.1.1 车系管理

```typescript
// 车系列表
GET /admin/api/car-series
Query: {
  page?: number
  pageSize?: number
  keyword?: string
  status?: number
}
Response: {
  total: number
  list: CarSeries[]
}

// 创建车系
POST /admin/api/car-series
Body: {
  code: string
  name: string
  description?: string
  displayOrder?: number
  status?: number
}

// 更新车系
PUT /admin/api/car-series/{id}
Body: {
  name?: string
  description?: string
  displayOrder?: number
  status?: number
}

// 删除车系
DELETE /admin/api/car-series/{id}
```

#### 4.1.2 车型管理

```typescript
// 车型列表
GET /admin/api/car-models
Query: {
  seriesId?: number
  page?: number
  pageSize?: number
  keyword?: string
  status?: number
  isHybrid?: boolean
}

// 创建车型
POST /admin/api/car-models
Body: {
  seriesId: number
  code: string
  name: string
  category?: string
  description?: string
  basePrice?: number
  imageUrl?: string
  evIconUrl?: string
  badge?: string
  endDate?: string
  isHybrid?: boolean
  status?: number
  // 关联数据
  specs?: CarSpecs
  financing?: CarFinancing
}

// 更新车型
PUT /admin/api/car-models/{id}

// 删除车型
DELETE /admin/api/car-models/{id}

// 批量更新排序
PUT /admin/api/car-models/sort
Body: {
  items: Array<{id: number, displayOrder: number}>
}
```

#### 4.1.3 配置包管理

```typescript
// 获取车型配置包列表
GET /admin/api/car-models/{modelId}/packages

// 创建配置包
POST /admin/api/car-packages
Body: {
  modelId: number
  code: string
  name: string
  price?: number
  featuresTitle?: string
  features: Array<{
    featureName: string
    isIncluded: boolean
  }>
}

// 更新配置包
PUT /admin/api/car-packages/{id}

// 删除配置包
DELETE /admin/api/car-packages/{id}
```

#### 4.1.4 颜色管理

```typescript
// 获取车型颜色列表
GET /admin/api/car-models/{modelId}/colors

// 创建颜色选项
POST /admin/api/car-colors
Body: {
  modelId: number
  code: string
  name: string
  price?: number
  imageUrl?: string
  colorCode?: string[]
  secondaryColorCode?: string[]
  isTwoTone?: boolean
  isPremium?: boolean
  requiredPackage?: string
}

// 更新颜色选项
PUT /admin/api/car-colors/{id}

// 删除颜色选项
DELETE /admin/api/car-colors/{id}
```

#### 4.1.5 图片管理

```typescript
// 上传图片
POST /admin/api/upload/image
Body: FormData (file)
Response: {
  url: string
  thumbnailUrl?: string
}

// 获取车型图片列表
GET /admin/api/car-models/{modelId}/images
Query: {
  imageType?: string
}

// 添加车型图片
POST /admin/api/car-model-images
Body: {
  modelId: number
  imageType: string
  imageUrl: string
  mobileImageUrl?: string
  title?: string
  description?: string
  label?: string
  displayOrder?: number
}

// 更新图片信息
PUT /admin/api/car-model-images/{id}

// 删除图片
DELETE /admin/api/car-model-images/{id}
```

### 4.2 App端API

#### 4.2.1 车型列表接口

```typescript
// 获取所有车型（带分类）
GET /app/api/car-models
Response: {
  tabs: Array<{
    id: string
    label: string
  }>
  models: Record<string, Array<{
    id: string
    name: string
    description: string
    price: string
    priceType: string
    image: string
    specs: {
      engine: string
      engineType: string
      fuelConsumption: string
      power: string
      torque: string
      seatsNum: string
    }
    financing?: {
      weeklyPayment: string
      comparisonRate: string
      deposit: string
      term: string
      gfv: string
      kmAllowance: string
    }
  }>>
}

// 根据标签获取车型
GET /app/api/car-models/by-tab/{tabCode}
Response: {
  models: CarModel[]
}

// 获取推荐车型
GET /app/api/car-models/recommended
Query: {
  limit?: number
}
Response: {
  models: CarModel[]
}
```

#### 4.2.2 车型详情接口

```typescript
// 获取车型详情
GET /app/api/car-models/{code}
Response: {
  // 基础信息
  model: {
    id: string
    code: string
    name: string
    category: string
    description: string
    basePrice: number
    image: string
    evIcon?: string
    badge?: string
    endDate?: string
  }
  // 技术规格
  specs: {
    engine: string
    engineType: string
    fuelConsumption: string
    power: string
    torque: string
    seatsNum: string
    transmission?: string
    drivetrain?: string
    dimensions?: {
      length: number
      width: number
      height: number
      wheelbase: number
      luggageCapacity: number
    }
  }
  // 融资信息
  financing?: {
    weeklyPayment: string
    comparisonRate: string
    deposit: string
    term: string
    gfv: string
    kmAllowance: string
  }
  // 配置包
  packages: Package[]
  // 颜色选项
  colors: ColorOption[]
  // 内饰选项
  interiors: InteriorOption[]
  // 轮毂选项
  wheels: WheelOption[]
  // 图片列表
  images: Array<{
    id: string
    imageType: string
    imageUrl: string
    mobileImageUrl?: string
    title?: string
    description?: string
  }>
  // 特性列表
  features: Array<{
    id: string
    featureType: string
    title: string
    description: string
    imageUrl?: string
    iconUrl?: string
  }>
}
```

#### 4.2.3 配置器接口

```typescript
// 获取配置器数据
GET /app/api/configurator/models
Response: {
  models: Array<{
    id: string
    name: string
    basePrice: number
    image: string
    packages: Package[]
    colors: ColorOption[]
    interiors: InteriorOption[]
    wheels: WheelOption[]
  }>
}

// 计算配置价格
POST /app/api/configurator/calculate
Body: {
  modelId: string
  packageId: string
  colorId: string
  interiorId?: string
  wheelId?: string
}
Response: {
  basePrice: number
  packagePrice: number
  colorPrice: number
  interiorPrice: number
  wheelPrice: number
  totalPrice: number
}

// 保存配置方案
POST /app/api/configurator/save
Body: {
  modelId: string
  packageId: string
  colorId: string
  interiorId?: string
  wheelId?: string
  customerInfo?: {
    name: string
    email: string
    phone: string
  }
}
Response: {
  configId: string
  shareUrl: string
}

// 获取保存的配置
GET /app/api/configurator/config/{configId}
```

#### 4.2.4 融资计算器接口

```typescript
// 获取融资选项
GET /app/api/finance/options
Response: {
  options: Array<{
    id: string
    name: string
    description: string
    terms: Array<{
      id: string
      name: string
      rate: number
    }>
    downPayments: Array<{
      id: string
      name: string
      value: number
    }>
  }>
}

// 计算融资方案
POST /app/api/finance/calculate
Body: {
  price: number
  optionId: string
  termId: string
  downPaymentId: string
}
Response: {
  totalPrice: number
  downPayment: number
  loanAmount: number
  monthlyPayment: number
  weeklyPayment: number
  totalInterest: number
  totalPayment: number
}
```

#### 4.2.5 搜索接口

```typescript
// 搜索车型
GET /app/api/search/models
Query: {
  keyword?: string
  priceMin?: number
  priceMax?: number
  seats?: number
  engineType?: string
  isHybrid?: boolean
}
Response: {
  total: number
  models: CarModel[]
}
```

## 5. 数据迁移方案

### 5.1 迁移步骤

1. **准备阶段**
   - 创建数据库表结构
   - 准备数据导入脚本
   - 备份现有静态数据

2. **数据导入**
   - 导入车系数据
   - 导入车型基础数据
   - 导入技术规格
   - 导入融资信息
   - 导入配置包及特性
   - 导入颜色、内饰、轮毂选项
   - 导入页面配置数据
   - 导入图片和特性数据

3. **验证阶段**
   - 数据完整性验证
   - API接口测试
   - 前端展示测试

### 5.2 数据导入脚本示例

```javascript
// 数据导入脚本示例
const importCarModels = async () => {
  const { carModels } = require('./src/config/carModels');
  const { MODELS_DATA } = require('./src/config/models');
  
  for (const [code, model] of Object.entries(carModels)) {
    // 1. 导入车型基础信息
    const modelId = await insertCarModel({
      code,
      name: model.name,
      category: model.category,
      description: model.description,
      basePrice: model.basePrice,
      imageUrl: model.image,
      evIconUrl: model.evIcon,
      badge: model.badge,
      endDate: model.endDate
    });
    
    // 2. 导入技术规格
    if (model.specs) {
      await insertCarSpecs({
        modelId,
        ...model.specs
      });
    }
    
    // 3. 导入融资信息
    if (model.financing) {
      await insertCarFinancing({
        modelId,
        ...model.financing
      });
    }
    
    // 4. 导入配置包
    for (const pkg of model.packages) {
      const packageId = await insertPackage({
        modelId,
        code: pkg.id,
        name: pkg.name,
        price: pkg.price,
        featuresTitle: pkg.featuresTitle
      });
      
      // 导入配置包特性
      for (const feature of pkg.features) {
        await insertPackageFeature({
          packageId,
          featureName: feature[0],
          isIncluded: feature[1]
        });
      }
    }
    
    // 5. 导入颜色选项
    for (const color of model.colors) {
      await insertCarColor({
        modelId,
        code: color.id,
        name: color.name,
        price: color.price,
        imageUrl: color.image,
        colorCode: color.colorCode,
        secondaryColorCode: color.secondaryColorCode,
        isTwoTone: color.isTwoTone,
        requiredPackage: color.requiredPackage
      });
    }
    
    // 6. 导入图片数据
    if (model.image) {
      await insertCarModelImage({
        modelId,
        imageType: 'main',
        imageUrl: model.image
      });
    }
    
    // 7. 导入特性数据
    if (model.features) {
      for (const feature of model.features) {
        await insertCarModelFeature({
          modelId,
          featureType: 'general',
          title: feature,
          description: ''
        });
      }
    }
  }
};
```

## 6. 管理端功能设计

### 6.1 功能模块

1. **车系管理**
   - 车系列表（增删改查）
   - 车系排序
   - 车系状态管理

2. **车型管理**
   - 车型列表（增删改查）
   - 车型基础信息编辑
   - 技术规格编辑
   - 融资信息编辑
   - 车型排序
   - 车型状态管理

3. **配置管理**
   - 配置包管理
   - 颜色选项管理
   - 内饰选项管理
   - 轮毂选项管理
   - 配置项排序

4. **标签分类管理**
   - 标签管理
   - 车型标签关联
   - 标签排序

5. **媒体资源管理**
   - 图片上传
   - 图片管理
   - 图片分类

6. **特性管理**
   - 车型特性配置
   - 特性分类管理
   - 特性排序

### 6.2 管理端界面设计

```typescript
// 管理端路由结构
/admin
  /car-series          // 车系管理
    /list             // 车系列表
    /add              // 新增车系
    /edit/:id         // 编辑车系
  
  /car-models          // 车型管理
    /list             // 车型列表
    /add              // 新增车型
    /edit/:id         // 编辑车型
    /config/:id       // 车型配置
      /packages       // 配置包
      /colors         // 颜色
      /interiors      // 内饰
      /wheels         // 轮毂
    /features/:id     // 特性管理
      /list           // 特性列表
      /add            // 添加特性
      /edit           // 编辑特性
    /images/:id       // 图片管理
      /list           // 图片列表
      /upload         // 上传图片
  
  /model-tabs         // 标签管理
    /list            // 标签列表
    /relations       // 关联管理
  
  /media              // 媒体管理
    /images          // 图片管理
    /upload          // 上传
```

## 7. 性能优化方案

### 7.1 数据库优化

1. **索引优化**
   - 为常用查询字段建立索引
   - 复合索引优化
   - 定期分析查询性能

2. **查询优化**
   - 使用分页查询
   - 避免N+1查询问题
   - 合理使用缓存

### 7.2 API优化

1. **接口缓存**
   ```typescript
   // Redis缓存配置
   const cacheConfig = {
     carModels: {
       ttl: 3600,  // 1小时
       key: 'car:models:{tab}'
     },
     modelDetail: {
       ttl: 7200,  // 2小时
       key: 'car:model:{code}'
     },
     configurator: {
       ttl: 1800,  // 30分钟
       key: 'car:configurator'
     }
   };
   ```

2. **数据压缩**
   - 启用Gzip压缩
   - 图片CDN加速
   - 静态资源优化

3. **并发控制**
   - API限流
   - 队列处理
   - 负载均衡

## 8. 安全设计

### 8.1 数据安全

1. **权限控制**
   - 基于角色的访问控制（RBAC）
   - API Token认证
   - 操作日志记录

2. **数据验证**
   - 输入参数验证
   - SQL注入防护
   - XSS防护

### 8.2 接口安全

```typescript
// API认证中间件
const authMiddleware = {
  // 管理端认证
  adminAuth: (req, res, next) => {
    const token = req.headers.authorization;
    // 验证管理员Token
    verifyAdminToken(token);
  },
  
  // App端认证（可选）
  appAuth: (req, res, next) => {
    const apiKey = req.headers['x-api-key'];
    // 验证API Key
    verifyApiKey(apiKey);
  },
  
  // 限流控制
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100 // 限制100次请求
  }
};
```

## 9. 监控与维护

### 9.1 监控指标

1. **性能监控**
   - API响应时间
   - 数据库查询性能
   - 缓存命中率

2. **业务监控**
   - 车型浏览量
   - 配置器使用率
   - 错误率统计

### 9.2 日志管理

```typescript
// 日志配置
const logConfig = {
  levels: ['error', 'warn', 'info', 'debug'],
  formats: {
    api: '{timestamp} {level} {method} {url} {status} {duration}ms',
    business: '{timestamp} {level} {action} {userId} {modelId} {details}'
  },
  storage: {
    error: '/logs/error.log',
    access: '/logs/access.log',
    business: '/logs/business.log'
  }
};
```

## 10. 部署方案

### 10.1 环境配置

```yaml
# Docker Compose配置
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: car_models
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
  
  redis:
    image: redis:7.0
    volumes:
      - redis_data:/data
  
  api:
    build: ./api
    environment:
      DB_HOST: mysql
      REDIS_HOST: redis
    depends_on:
      - mysql
      - redis
  
  admin:
    build: ./admin
    environment:
      API_URL: http://api:3000
    depends_on:
      - api
```

### 10.2 部署流程

1. **开发环境**
   - 本地数据库
   - 本地API服务
   - 热重载开发

2. **测试环境**
   - 测试数据库
   - API测试
   - 自动化测试

3. **生产环境**
   - 数据库集群
   - API负载均衡
   - CDN加速
   - 监控告警

## 11. 总结

本方案基于现有的静态数据结构，设计了完整的数据库架构和API接口体系，支持：

1. **核心数据管理**：完整的车系、车型、配置、融资等核心业务数据管理
2. **灵活的配置系统**：支持配置包、颜色、内饰、轮毂等多维度配置
3. **高性能API服务**：提供缓存、分页、优化查询等性能优化方案
4. **完整的业务支持**：覆盖车型展示、配置器、融资计算等所有业务场景
5. **良好的扩展性**：预留扩展字段，支持未来业务扩展
6. **安全可靠**：包含认证、授权、监控等安全机制

该方案聚焦于核心的车型数据管理，去除了页面配置等前端相关内容，使系统更加专注和高效。通过标准化的API接口，可以支持多种前端展示方式，实现数据与展示的完全解耦。