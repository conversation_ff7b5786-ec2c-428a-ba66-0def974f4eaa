package cn.iocoder.yudao.module.cms.dal.dataobject.statistics;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDate;

/**
 * CMS页面访问统计汇总 DO
 *
 * <AUTHOR>
 */
@TableName("cms_page_visit_stats")
@KeySequence("cms_page_visit_stats_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsPageVisitStatsDO extends BaseDO {

    /**
     * 统计ID
     */
    @TableId
    private Long id;

    /**
     * 页面ID
     */
    private Long pageId;

    /**
     * 统计日期
     */
    private LocalDate statDate;

    /**
     * 总访问次数
     */
    private Integer totalVisits;

    /**
     * 独立访客数
     */
    private Integer uniqueVisitors;

    /**
     * 平均停留时间（秒）
     */
    private Integer avgStayTime;

    /**
     * 跳出次数
     */
    private Integer bounceCount;

}