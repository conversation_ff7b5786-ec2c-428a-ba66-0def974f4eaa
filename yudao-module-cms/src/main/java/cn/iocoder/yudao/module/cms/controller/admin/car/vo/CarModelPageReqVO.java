package cn.iocoder.yudao.module.cms.controller.admin.car.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 车型分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CarModelPageReqVO extends PageParam {

    @Schema(description = "车型编码", example = "TIGGO8_PRO_1_5T")
    private String code;

    @Schema(description = "车型名称", example = "瑞虎8 PRO")
    private String name;

    @Schema(description = "车系ID", example = "1024")
    private Long seriesId;

    @Schema(description = "最小指导价", example = "100000.00")
    private BigDecimal minGuidePrice;

    @Schema(description = "最大指导价", example = "200000.00")
    private BigDecimal maxGuidePrice;

    @Schema(description = "状态：0-禁用，1-启用", example = "1")
    private Integer status;

}