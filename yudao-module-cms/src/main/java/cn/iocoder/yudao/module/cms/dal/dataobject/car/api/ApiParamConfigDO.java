package cn.iocoder.yudao.module.cms.dal.dataobject.car.api;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * API参数配置 DO
 *
 * <AUTHOR>
 */
@TableName("cms_api_param_configs")
@KeySequence("cms_api_param_configs_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiParamConfigDO extends BaseDO {

    /**
     * 参数ID
     */
    @TableId
    private Long id;

    /**
     * API配置ID
     */
    private Long apiConfigId;

    /**
     * 参数名
     */
    private String paramName;

    /**
     * 参数类型
     * 枚举：string, integer, decimal, boolean
     */
    private String paramType;

    /**
     * 是否必需
     */
    private Boolean isRequired;

    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 验证规则
     */
    private String validationRule;

    /**
     * 参数描述
     */
    private String description;

}