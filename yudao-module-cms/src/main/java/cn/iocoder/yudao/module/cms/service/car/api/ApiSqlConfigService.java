package cn.iocoder.yudao.module.cms.service.car.api;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.api.ApiSqlConfigCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.api.ApiSqlConfigPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.api.ApiSqlConfigUpdateReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.api.ApiSqlConfigDO;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * API SQL配置 Service 接口
 *
 * <AUTHOR>
 */
public interface ApiSqlConfigService {

    /**
     * 创建API SQL配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createApiSqlConfig(@Valid ApiSqlConfigCreateReqVO createReqVO);

    /**
     * 更新API SQL配置
     *
     * @param updateReqVO 更新信息
     */
    void updateApiSqlConfig(@Valid ApiSqlConfigUpdateReqVO updateReqVO);

    /**
     * 删除API SQL配置
     *
     * @param id 编号
     */
    void deleteApiSqlConfig(Long id);

    /**
     * 获得API SQL配置
     *
     * @param id 编号
     * @return API SQL配置
     */
    ApiSqlConfigDO getApiSqlConfig(Long id);

    /**
     * 获得API SQL配置分页
     *
     * @param pageReqVO 分页查询
     * @return API SQL配置分页
     */
    PageResult<ApiSqlConfigDO> getApiSqlConfigPage(ApiSqlConfigPageReqVO pageReqVO);

    /**
     * 获得API SQL配置列表
     *
     * @param status 状态
     * @return API SQL配置列表
     */
    List<ApiSqlConfigDO> getApiSqlConfigList(Integer status);

    /**
     * 根据API标识码和版本获取配置
     *
     * @param apiCode API标识码
     * @param version 版本
     * @return API SQL配置
     */
    ApiSqlConfigDO getApiSqlConfigByCodeAndVersion(String apiCode, String version);

    /**
     * 根据API标识码获取默认配置
     *
     * @param apiCode API标识码
     * @return API SQL配置
     */
    ApiSqlConfigDO getDefaultApiSqlConfig(String apiCode);

    /**
     * 根据API标识码获取所有版本配置
     *
     * @param apiCode API标识码
     * @return API SQL配置列表
     */
    List<ApiSqlConfigDO> getApiSqlConfigListByCode(String apiCode);

    /**
     * 获取所有不同的API标识码
     *
     * @return API标识码列表
     */
    List<String> getAllApiCodes();

    /**
     * 设置为默认版本
     *
     * @param id 配置ID
     */
    void setAsDefaultVersion(Long id);

    /**
     * 验证SQL语句格式
     *
     * @param sqlContent SQL内容
     * @return 验证结果
     */
    boolean validateSqlFormat(String sqlContent);

    /**
     * 测试SQL执行
     *
     * @param sqlContent SQL内容
     * @param params 参数
     * @return 执行结果
     */
    List<Map<String, Object>> testSqlExecution(String sqlContent, Map<String, Object> params);

    /**
     * 复制API配置为新版本
     *
     * @param id 原配置ID
     * @param newVersion 新版本号
     * @return 新配置ID
     */
    Long copyApiConfig(Long id, String newVersion);

    /**
     * 解释SQL执行计划
     *
     * @param sqlContent SQL内容
     * @return 执行计划
     */
    List<Map<String, Object>> explainSql(String sqlContent);

    /**
     * 解释SQL执行计划（带参数）
     *
     * @param sqlContent SQL内容
     * @param params 参数
     * @return 执行计划
     */
    List<Map<String, Object>> explainSql(String sqlContent, Map<String, Object> params);

}