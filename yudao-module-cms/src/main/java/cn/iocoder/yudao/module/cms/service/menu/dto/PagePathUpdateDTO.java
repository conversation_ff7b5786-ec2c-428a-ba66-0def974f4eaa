package cn.iocoder.yudao.module.cms.service.menu.dto;

import lombok.Data;

/**
 * 页面路径更新DTO
 * 用于菜单路径变更时批量更新关联页面的路径
 *
 * <AUTHOR>
 */
@Data
public class PagePathUpdateDTO {

    /**
     * 页面ID
     */
    private Long pageId;

    /**
     * 新的完整路径
     */
    private String newFullPath;

    /**
     * 页面原路径（用于构建新路径）
     */
    private String originalPath;

    public PagePathUpdateDTO() {}

    public PagePathUpdateDTO(Long pageId, String newFullPath, String originalPath) {
        this.pageId = pageId;
        this.newFullPath = newFullPath;
        this.originalPath = originalPath;
    }
}