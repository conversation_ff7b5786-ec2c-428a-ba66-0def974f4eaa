package cn.iocoder.yudao.module.cms.dal.mysql.car;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarSeriesPageReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarSeriesDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 车系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CarSeriesMapper extends BaseMapperX<CarSeriesDO> {

    default PageResult<CarSeriesDO> selectPage(CarSeriesPageReqVO pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<CarSeriesDO>()
                .likeIfPresent(CarSeriesDO::getCode, pageReqVO.getCode())
                .likeIfPresent(CarSeriesDO::getName, pageReqVO.getName())
                .eqIfPresent(CarSeriesDO::getStatus, pageReqVO.getStatus())
                .orderByAsc(CarSeriesDO::getSortOrder)
                .orderByDesc(CarSeriesDO::getId));
    }

    default CarSeriesDO selectByCode(String code) {
        return selectOne(CarSeriesDO::getCode, code);
    }

    default List<CarSeriesDO> selectListByBrandAndStatus(String brand, Integer status) {
        return selectList(new LambdaQueryWrapperX<CarSeriesDO>()
                .eqIfPresent(CarSeriesDO::getStatus, status)
                .orderByAsc(CarSeriesDO::getSortOrder)
                .orderByDesc(CarSeriesDO::getId));
    }

}