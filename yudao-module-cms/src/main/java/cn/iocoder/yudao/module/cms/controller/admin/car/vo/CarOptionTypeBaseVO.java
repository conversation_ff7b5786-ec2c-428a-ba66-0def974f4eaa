package cn.iocoder.yudao.module.cms.controller.admin.car.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotNull;
import java.util.Map;

/**
 * 配置选项类型 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class CarOptionTypeBaseVO {

    @Schema(description = "选项类型代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "colors")
    @NotNull(message = "选项类型代码不能为空")
    @Length(max = 50, message = "选项类型代码长度不能超过50个字符")
    private String typeCode;

    @Schema(description = "选项类型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "外观颜色")
    @NotNull(message = "选项类型名称不能为空")
    @Length(max = 100, message = "选项类型名称长度不能超过100个字符")
    private String name;

    @Schema(description = "英文名称", example = "Colors")
    @Length(max = 100, message = "英文名称长度不能超过100个字符")
    private String nameEn;

    @Schema(description = "类型描述", example = "车辆外观颜色配置")
    @Length(max = 500, message = "类型描述长度不能超过500个字符")
    private String description;

    @Schema(description = "配置字段定义", example = "{\"type\":\"object\",\"properties\":{\"colorCode\":{\"type\":\"string\"}}}")
    private Map<String, Object> configSchema;

    @Schema(description = "排序", example = "1")
    private Integer sortOrder;

    @Schema(description = "状态：0-禁用，1-启用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

}