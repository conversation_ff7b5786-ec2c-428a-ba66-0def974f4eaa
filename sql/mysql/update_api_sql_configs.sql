-- 更新已存在的API SQL配置
-- 修复OFFSET语法错误
-- 作者：开发团队
-- 日期：2025-01-10

SET NAMES utf8mb4;

-- 更新car_models_list的SQL配置，修复OFFSET语法错误
UPDATE cms_api_sql_configs 
SET sql_content = 'SELECT 
    JSON_OBJECT(
        ''total'', COUNT(*) OVER(),
        ''models'', JSON_ARRAYAGG(
            JSON_OBJECT(
                ''id'', m.code,
                ''name'', m.name,
                ''category'', m.category,
                ''base_price'', m.base_price,
                ''image'', m.image_url,
                ''badge'', m.badge,
                ''series_name'', cs.name
            )
        )
    ) as api_response
FROM cms_car_models m
LEFT JOIN cms_car_series cs ON m.series_id = cs.id
WHERE m.status = 0 AND m.deleted = 0
<if test="series != null">AND cs.code = #{series}</if>
<if test="category != null">AND m.category = #{category}</if>
ORDER BY m.sort_order, m.id
LIMIT #{pageSize} OFFSET (#{pageNo} - 1) * #{pageSize}'
WHERE api_code = 'car_models_list' AND version = 'default';

-- 如果你已经通过管理后台保存了SQL配置，并且SQL中的单引号已经正常（不是双单引号），
-- 则不需要执行下面的更新。
-- 如果SQL配置是通过SQL文件导入的，并且JSON_OBJECT的键名是双单引号，
-- 则需要手动在管理后台重新编辑并保存，使用正常的单引号。

COMMIT;