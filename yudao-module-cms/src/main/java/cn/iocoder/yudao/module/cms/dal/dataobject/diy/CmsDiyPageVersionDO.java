package cn.iocoder.yudao.module.cms.dal.dataobject.diy;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * CMS DIY页面版本 DO
 *
 * <AUTHOR>
 */
@TableName("cms_diy_page_version")
@KeySequence("cms_diy_page_version_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsDiyPageVersionDO extends BaseDO {

    /**
     * 版本ID
     */
    @TableId
    private Long id;

    /**
     * 页面ID
     */
    private Long pageId;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 版本名称
     */
    private String name;

    /**
     * 页面内容JSON快照
     */
    private String content;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 是否已发布
     */
    private Boolean isPublished;

    /**
     * 版本备注
     */
    private String remark;

}