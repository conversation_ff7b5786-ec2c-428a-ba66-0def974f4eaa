package cn.iocoder.yudao.module.cms.service.car;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarOptionTypeCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarOptionTypePageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarOptionTypeUpdateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarOptionTypeUpdateSchemaReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarOptionTypeDO;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 配置选项类型 Service 接口
 *
 * <AUTHOR>
 */
public interface CarOptionTypeService {

    /**
     * 创建配置选项类型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCarOptionType(@Valid CarOptionTypeCreateReqVO createReqVO);

    /**
     * 更新配置选项类型
     *
     * @param updateReqVO 更新信息
     */
    void updateCarOptionType(@Valid CarOptionTypeUpdateReqVO updateReqVO);

    /**
     * 更新配置选项类型
     *
     * @param updateReqVO 更新信息
     */
    void updateCarOptionTypeSchema(@Valid CarOptionTypeUpdateSchemaReqVO updateReqVO);


    /**
     * 删除配置选项类型
     *
     * @param id 编号
     */
    void deleteCarOptionType(Long id);

    /**
     * 获得配置选项类型
     *
     * @param id 编号
     * @return 配置选项类型
     */
    CarOptionTypeDO getCarOptionType(Long id);

    /**
     * 获得配置选项类型分页
     *
     * @param pageReqVO 分页查询
     * @return 配置选项类型分页
     */
    PageResult<CarOptionTypeDO> getCarOptionTypePage(CarOptionTypePageReqVO pageReqVO);

    /**
     * 获得配置选项类型列表
     *
     * @param status 状态
     * @return 配置选项类型列表
     */
    List<CarOptionTypeDO> getCarOptionTypeList(Integer status);

    /**
     * 根据类型编码获取配置选项类型
     *
     * @param typeCode 类型编码
     * @return 配置选项类型信息
     */
    CarOptionTypeDO getCarOptionTypeByCode(String typeCode);

    /**
     * 验证JSON Schema配置
     *
     * @param schema JSON Schema配置
     * @return 验证结果，true表示验证通过
     */
    boolean validateJsonSchema(Map<String, Object> schema);

    /**
     * 验证配置数据是否符合Schema
     *
     * @param typeCode 类型编码
     * @param configData 配置数据
     * @return 验证结果，true表示验证通过
     */
    boolean validateConfigData(String typeCode, Map<String, Object> configData);

}