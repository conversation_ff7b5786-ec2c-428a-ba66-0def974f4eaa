package cn.iocoder.yudao.module.cms.controller.app.dealer.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "应用 App - 经销商综合查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppDealerSearchReqVO extends PageParam {

    @Schema(description = "名称关键词", example = "Allen Motor")
    private String name;

    @Schema(description = "地区", example = "London")
    private String region;

    @Schema(description = "服务标签数组", example = "[\"Sales\", \"Service\"]")
    private List<String> services;

    @Schema(description = "中心点纬度（用于距离排序）", example = "51.5")
    private Double latitude;

    @Schema(description = "中心点经度（用于距离排序）", example = "0.1")
    private Double longitude;

    @Schema(description = "邮政编码", example = "1")
    private String postcode;
}