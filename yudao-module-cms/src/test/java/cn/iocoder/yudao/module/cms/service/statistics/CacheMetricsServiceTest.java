package cn.iocoder.yudao.module.cms.service.statistics;

import cn.iocoder.yudao.module.cms.controller.admin.statistics.vo.CacheMetricsRespVO;
import cn.iocoder.yudao.module.cms.controller.admin.statistics.vo.CacheStatsRespVO;
import cn.iocoder.yudao.module.cms.framework.cache.core.CacheWarmupService;
import cn.iocoder.yudao.module.cms.framework.cache.core.MultiLevelCacheManager;
import cn.iocoder.yudao.module.cms.service.statistics.CacheMetricsServiceImpl;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CacheMetricsServiceTest {

    @Mock
    private MultiLevelCacheManager multiLevelCacheManager;

    @Mock
    private CacheWarmupService cacheWarmupService;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    private RedisConnectionFactory redisConnectionFactory;

    @Mock
    private CacheManager caffeineCacheManager;

    @Mock
    private RedisConnection redisConnection;

    @Mock
    private CaffeineCache caffeineCache;

    @Mock
    private Cache<Object, Object> nativeCache;

    private CacheMetricsServiceImpl cacheMetricsService;

    @BeforeEach
    void setUp() throws Exception {
        cacheMetricsService = new CacheMetricsServiceImpl(
            multiLevelCacheManager,
            cacheWarmupService,
            redisTemplate,
            redisConnectionFactory
        );

        // 使用反射设置 caffeineCacheManager
        Field field = CacheMetricsServiceImpl.class.getDeclaredField("caffeineCacheManager");
        field.setAccessible(true);
        field.set(cacheMetricsService, caffeineCacheManager);

        // Mock Redis连接
        when(redisConnectionFactory.getConnection()).thenReturn(redisConnection);
        when(redisConnection.ping()).thenReturn("PONG".getBytes());
    }

    @Test
    void testGetCacheMetrics() throws Exception {
        // Given
        setupCacheHitsAndMisses();

        // When
        CacheMetricsRespVO metrics = cacheMetricsService.getCacheMetrics();

        // Then
        assertNotNull(metrics);
        assertEquals(1500L, metrics.getTotalHits());
        assertEquals(500L, metrics.getTotalMisses());
        assertEquals(50L, metrics.getTotalEvictions());
        assertEquals(0.75, metrics.getOverallHitRate(), 0.01);
        assertTrue(metrics.getRedisConnected());
        assertEquals(5, metrics.getCacheCount());
        assertNotNull(metrics.getMemoryUsed());
        assertNotNull(metrics.getMemoryMax());
        assertNotNull(metrics.getMemoryUsagePercent());
    }

    @Test
    void testGetCacheStatistics() {
        // Given
        String cacheName = "menu-tree";
        CacheStats mockStats = mock(CacheStats.class);
        when(mockStats.hitCount()).thenReturn(1000L);
        when(mockStats.missCount()).thenReturn(100L);
        when(mockStats.evictionCount()).thenReturn(10L);
        when(mockStats.loadCount()).thenReturn(110L);
        when(mockStats.loadSuccessCount()).thenReturn(105L);
        when(mockStats.loadFailureCount()).thenReturn(5L);
        when(mockStats.hitRate()).thenReturn(0.9091);
        when(mockStats.averageLoadPenalty()).thenReturn(50000.0);
        when(mockStats.totalLoadTime()).thenReturn(5500000L);
        
        when(caffeineCacheManager.getCache(cacheName)).thenReturn(caffeineCache);
        when(caffeineCache.getNativeCache()).thenReturn(nativeCache);
        when(nativeCache.stats()).thenReturn(mockStats);
        when(nativeCache.estimatedSize()).thenReturn(50L);

        // When
        CacheStatsRespVO stats = cacheMetricsService.getCacheStatistics(cacheName);

        // Then
        assertNotNull(stats);
        assertEquals(cacheName, stats.getCacheName());
        assertEquals(1000L, stats.getHitCount());
        assertEquals(100L, stats.getMissCount());
        assertEquals(10L, stats.getEvictionCount());
        assertEquals(110L, stats.getLoadCount());
        assertEquals(105L, stats.getLoadSuccessCount());
        assertEquals(5L, stats.getLoadFailureCount());
        assertEquals(0.9091, stats.getHitRate(), 0.001);
        assertEquals(50000.0, stats.getAverageLoadPenalty());
        assertEquals(5500000L, stats.getTotalLoadTime());
        assertEquals(50L, stats.getSize());
    }

    @Test
    void testGetAllCacheStatistics() {
        // Given
        setupMockCacheForAllStats();

        // When
        List<CacheStatsRespVO> allStats = cacheMetricsService.getAllCacheStatistics();

        // Then
        assertNotNull(allStats);
        assertEquals(5, allStats.size());
        assertTrue(allStats.stream().anyMatch(stats -> "menu-tree".equals(stats.getCacheName())));
        assertTrue(allStats.stream().anyMatch(stats -> "diy-page".equals(stats.getCacheName())));
    }

    @Test
    void testGetCacheHitRates() throws Exception {
        // Given
        setupCacheHitsAndMisses();

        // When
        Map<String, Double> hitRates = cacheMetricsService.getCacheHitRates();

        // Then
        assertNotNull(hitRates);
        assertEquals(5, hitRates.size());
        assertTrue(hitRates.containsKey("menu-tree"));
        assertTrue(hitRates.containsKey("diy-page"));
        
        // 验证命中率计算正确
        Double menuTreeRate = hitRates.get("menu-tree");
        assertNotNull(menuTreeRate);
        assertTrue(menuTreeRate >= 0.0 && menuTreeRate <= 1.0);
    }

    @Test
    void testWarmupCache_MenuTree() {
        // Given
        String cacheName = "menu-tree";

        // When
        cacheMetricsService.warmupCache(cacheName);

        // Then
        verify(cacheWarmupService).warmupMenuCache();
        verifyNoMoreInteractions(cacheWarmupService);
    }

    @Test
    void testWarmupCache_DiyPage() {
        // Given
        String cacheName = "diy-page";

        // When
        cacheMetricsService.warmupCache(cacheName);

        // Then
        verify(cacheWarmupService).warmupDiyPageCache();
        verifyNoMoreInteractions(cacheWarmupService);
    }

    @Test
    void testWarmupCache_All() {
        // Given
        String cacheName = "unknown-cache";

        // When
        cacheMetricsService.warmupCache(cacheName);

        // Then
        verify(cacheWarmupService).warmupAllCaches();
    }

    @Test
    void testWarmupCache_Exception() {
        // Given
        String cacheName = "menu-tree";
        doThrow(new RuntimeException("Warmup failed")).when(cacheWarmupService).warmupMenuCache();

        // When & Then
        assertThrows(RuntimeException.class, () -> cacheMetricsService.warmupCache(cacheName));
    }

    @Test
    void testClearCache() {
        // Given
        String cacheName = "menu-tree";
        org.springframework.cache.Cache springCache = mock(org.springframework.cache.Cache.class);
        Set<String> redisKeys = Set.of("cms:cache:menu-tree:key1", "cms:cache:menu-tree:key2");
        
        when(caffeineCacheManager.getCache(cacheName)).thenReturn(springCache);
        when(redisTemplate.keys("cms:cache:" + cacheName + ":*")).thenReturn(redisKeys);

        // When
        cacheMetricsService.clearCache(cacheName);

        // Then
        verify(springCache).clear();
        verify(redisTemplate).delete(redisKeys);
    }

    @Test
    void testClearAllCaches() {
        // Given
        setupMockCacheForClear();

        // When
        cacheMetricsService.clearAllCaches();

        // Then
        verify(caffeineCacheManager, times(5)).getCache(any());
        verify(redisTemplate, times(5)).keys(any());
    }

    @Test
    void testCheckCacheHealth_Healthy() {
        // When
        boolean isHealthy = cacheMetricsService.checkCacheHealth();

        // Then
        assertTrue(isHealthy);
    }

    @Test
    void testCheckCacheHealth_RedisDown() {
        // Given
        when(redisConnection.ping()).thenThrow(new RuntimeException("Redis down"));

        // When
        boolean isHealthy = cacheMetricsService.checkCacheHealth();

        // Then
        assertFalse(isHealthy);
    }

    @Test
    void testGetCacheConfiguration() {
        // When
        Map<String, Object> config = cacheMetricsService.getCacheConfiguration();

        // Then
        assertNotNull(config);
        assertTrue(config.containsKey("cacheNames"));
        assertTrue(config.containsKey("caffeineEnabled"));
        assertTrue(config.containsKey("redisEnabled"));
        assertTrue(config.containsKey("caffeineConfiguration"));
        assertTrue(config.containsKey("redisConfiguration"));
    }

    @Test
    void testUpdateCacheConfiguration() {
        // Given
        String cacheName = "menu-tree";
        Map<String, Object> config = new HashMap<>();
        config.put("maximumSize", 5000);
        config.put("expireAfterWrite", "5m");

        // When
        assertDoesNotThrow(() -> cacheMetricsService.updateCacheConfiguration(cacheName, config));
    }

    @Test
    void testGetCacheAlerts_NoAlerts() {
        // Given
        setupCacheHitsAndMisses();

        // When
        List<String> alerts = cacheMetricsService.getCacheAlerts();

        // Then
        assertNotNull(alerts);
        // 应该没有告警，因为所有指标都是健康的
        assertTrue(alerts.size() <= 1); // 可能只有内存告警
    }

    @Test
    void testGetCacheAlerts_WithRedisDown() {
        // Given
        when(redisConnection.ping()).thenThrow(new RuntimeException("Redis down"));

        // When
        List<String> alerts = cacheMetricsService.getCacheAlerts();

        // Then
        assertNotNull(alerts);
        assertTrue(alerts.stream().anyMatch(alert -> alert.contains("Redis connection is down")));
    }

    @Test
    void testResetCacheStatistics() {
        // When
        cacheMetricsService.resetCacheStatistics();

        // Then - 应该不抛出异常
        assertDoesNotThrow(() -> cacheMetricsService.resetCacheStatistics());
    }

    @Test
    void testRecordCacheHit() {
        // Given
        String cacheName = "test-cache";

        // When
        cacheMetricsService.recordCacheHit(cacheName);
        cacheMetricsService.recordCacheHit(cacheName);

        // Then - 应该正确记录命中次数
        CacheStatsRespVO stats = cacheMetricsService.getCacheStatistics(cacheName);
        // 这里无法直接验证，因为recordCacheHit是私有方法的功能
        // 但确保不抛出异常
        assertDoesNotThrow(() -> cacheMetricsService.recordCacheHit(cacheName));
    }

    @Test
    void testRecordCacheMiss() {
        // Given
        String cacheName = "test-cache";

        // When & Then
        assertDoesNotThrow(() -> cacheMetricsService.recordCacheMiss(cacheName));
    }

    @Test
    void testRecordCacheEviction() {
        // Given
        String cacheName = "test-cache";

        // When & Then
        assertDoesNotThrow(() -> cacheMetricsService.recordCacheEviction(cacheName));
    }

    @Test
    void testRecordCacheAccessTime() {
        // Given
        String cacheName = "test-cache";
        long timeMs = 50L;

        // When & Then
        assertDoesNotThrow(() -> cacheMetricsService.recordCacheAccessTime(cacheName, timeMs));
    }

    private void setupCacheHitsAndMisses() throws Exception {
        // 使用反射设置内部统计数据
        Field cacheHitsField = CacheMetricsServiceImpl.class.getDeclaredField("cacheHits");
        cacheHitsField.setAccessible(true);
        Map<String, AtomicLong> cacheHits = (Map<String, AtomicLong>) cacheHitsField.get(cacheMetricsService);
        cacheHits.put("menu-tree", new AtomicLong(500));
        cacheHits.put("diy-page", new AtomicLong(400));
        cacheHits.put("diy-page-content", new AtomicLong(300));
        cacheHits.put("diy-page-version", new AtomicLong(200));
        cacheHits.put("page-stats", new AtomicLong(100));

        Field cacheMissesField = CacheMetricsServiceImpl.class.getDeclaredField("cacheMisses");
        cacheMissesField.setAccessible(true);
        Map<String, AtomicLong> cacheMisses = (Map<String, AtomicLong>) cacheMissesField.get(cacheMetricsService);
        cacheMisses.put("menu-tree", new AtomicLong(50));
        cacheMisses.put("diy-page", new AtomicLong(100));
        cacheMisses.put("diy-page-content", new AtomicLong(150));
        cacheMisses.put("diy-page-version", new AtomicLong(100));
        cacheMisses.put("page-stats", new AtomicLong(100));

        Field cacheEvictionsField = CacheMetricsServiceImpl.class.getDeclaredField("cacheEvictions");
        cacheEvictionsField.setAccessible(true);
        Map<String, AtomicLong> cacheEvictions = (Map<String, AtomicLong>) cacheEvictionsField.get(cacheMetricsService);
        cacheEvictions.put("menu-tree", new AtomicLong(10));
        cacheEvictions.put("diy-page", new AtomicLong(10));
        cacheEvictions.put("diy-page-content", new AtomicLong(10));
        cacheEvictions.put("diy-page-version", new AtomicLong(10));
        cacheEvictions.put("page-stats", new AtomicLong(10));
    }

    private void setupMockCacheForAllStats() {
        for (String cacheName : Arrays.asList("menu-tree", "diy-page", "diy-page-content", "diy-page-version", "page-stats")) {
            when(caffeineCacheManager.getCache(cacheName)).thenReturn(caffeineCache);
        }
        when(caffeineCache.getNativeCache()).thenReturn(nativeCache);
        when(nativeCache.stats()).thenReturn(mock(CacheStats.class));
        when(nativeCache.estimatedSize()).thenReturn(10L);
    }

    private void setupMockCacheForClear() {
        for (String cacheName : Arrays.asList("menu-tree", "diy-page", "diy-page-content", "diy-page-version", "page-stats")) {
            org.springframework.cache.Cache springCache = mock(org.springframework.cache.Cache.class);
            when(caffeineCacheManager.getCache(cacheName)).thenReturn(springCache);
            when(redisTemplate.keys("cms:cache:" + cacheName + ":*")).thenReturn(new HashSet<>());
        }
    }
}