package cn.iocoder.yudao.module.cms.controller.admin.statistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 缓存指标 Response VO")
@Data
public class CacheMetricsRespVO {

    @Schema(description = "总命中次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "10000")
    private Long totalHits;

    @Schema(description = "总未命中次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "1000")
    private Long totalMisses;

    @Schema(description = "总驱逐次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private Long totalEvictions;

    @Schema(description = "总体命中率", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.9091")
    private Double overallHitRate;

    @Schema(description = "内存使用量（字节）", requiredMode = Schema.RequiredMode.REQUIRED, example = "104857600")
    private Long memoryUsed;

    @Schema(description = "最大内存（字节）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1073741824")
    private Long memoryMax;

    @Schema(description = "内存使用百分比", requiredMode = Schema.RequiredMode.REQUIRED, example = "9.76")
    private Double memoryUsagePercent;

    @Schema(description = "Redis连接状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean redisConnected;

    @Schema(description = "缓存数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "5")
    private Integer cacheCount;

    @Schema(description = "平均响应时间（毫秒）", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    private Long averageResponseTimeMs;
}