package cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotNull;

/**
 * 融资选项 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class FinanceOptionBaseVO {

    @Schema(description = "融资选项代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "finance_loan")
    @NotNull(message = "融资选项代码不能为空")
    @Length(max = 50, message = "融资选项代码长度不能超过50个字符")
    private String optionCode;

    @Schema(description = "融资选项名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "银行贷款")
    @NotNull(message = "融资选项名称不能为空")
    @Length(max = 100, message = "融资选项名称长度不能超过100个字符")
    private String name;

    @Schema(description = "融资选项描述", example = "通过银行提供贷款服务")
    @Length(max = 500, message = "融资选项描述长度不能超过500个字符")
    private String description;

    @Schema(description = "排序", example = "1")
    private Integer sortOrder;

    @Schema(description = "状态：0-禁用，1-启用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

}