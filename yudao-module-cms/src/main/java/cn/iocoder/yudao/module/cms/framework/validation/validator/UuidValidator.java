package cn.iocoder.yudao.module.cms.framework.validation.validator;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.module.cms.framework.validation.CmsValidationUtils;
import cn.iocoder.yudao.module.cms.framework.validation.annotation.ValidUuid;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * UUID 格式验证器
 *
 * <AUTHOR>
 */
public class UuidValidator implements ConstraintValidator<ValidUuid, String> {

    private boolean allowEmpty;

    @Override
    public void initialize(ValidUuid constraintAnnotation) {
        this.allowEmpty = constraintAnnotation.allowEmpty();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // 允许为空的情况
        if (StrUtil.isBlank(value)) {
            return allowEmpty;
        }
        
        // 验证 UUID 格式
        return CmsValidationUtils.isValidUuid(value);
    }
}