-- 车型配置管理系统 DDL
-- 作者：开发团队
-- 日期：2025-01-09
-- 版本：v4.0

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. 基础表（无外键依赖）
-- ----------------------------

-- ----------------------------
-- Table structure for cms_car_series 车系表
-- ----------------------------
DROP TABLE IF EXISTS `cms_car_series`;
CREATE TABLE `cms_car_series` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '车系ID',
    `code` varchar(50) NOT NULL COMMENT '车系代码，如tiggo-7',
    `name` varchar(100) NOT NULL COMMENT '车系名称',
    `name_en` varchar(100) DEFAULT NULL COMMENT '英文名称',
    `description` text COMMENT '车系描述',
    `sort_order` int(11) DEFAULT '0' COMMENT '排序',
    `status` tinyint(4) DEFAULT '0' COMMENT '状态：0-启用，1-禁用',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_code` (`code`) USING BTREE,
    KEY `idx_status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车系表';

-- ----------------------------
-- Table structure for cms_car_models 车型表
-- ----------------------------
DROP TABLE IF EXISTS `cms_car_models`;
CREATE TABLE `cms_car_models` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '车型ID',
    `series_id` bigint(20) NOT NULL COMMENT '车系ID',
    `code` varchar(50) NOT NULL COMMENT '车型代码',
    `name` varchar(100) NOT NULL COMMENT '车型名称',
    `name_en` varchar(100) DEFAULT NULL COMMENT '英文名称',
    `description` text COMMENT '车型描述',
    `category` varchar(50) DEFAULT NULL COMMENT '车型分类',
    `base_price` decimal(10,2) NOT NULL COMMENT '基础价格',
    `image_url` varchar(500) DEFAULT NULL COMMENT '主图片URL',
    `ev_icon_url` varchar(500) DEFAULT NULL COMMENT 'EV图标URL',
    `poster_url` varchar(500) DEFAULT NULL COMMENT '配置图URL',
    `features` json DEFAULT NULL COMMENT '车型特性列表',
    `badge` varchar(50) DEFAULT NULL COMMENT '标识，如Value、Luxury',
    `end_date` date DEFAULT NULL COMMENT '优惠截止日期',
    `sort_order` int(11) DEFAULT '0' COMMENT '排序',
    `status` tinyint(4) DEFAULT '0' COMMENT '状态：0-启用，1-禁用',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_code` (`code`) USING BTREE,
    KEY `idx_series_status` (`series_id`,`status`) USING BTREE,
    KEY `idx_category` (`category`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车型表';

-- ----------------------------
-- Table structure for cms_car_model_specs 车型规格表
-- ----------------------------
DROP TABLE IF EXISTS `cms_car_model_specs`;
CREATE TABLE `cms_car_model_specs` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '规格ID',
    `model_id` bigint(20) NOT NULL COMMENT '车型ID',
    `engine` varchar(100) DEFAULT NULL COMMENT '发动机信息',
    `engine_type` varchar(50) DEFAULT NULL COMMENT '发动机类型',
    `fuel_consumption` varchar(50) DEFAULT NULL COMMENT '燃油消耗',
    `power` varchar(50) DEFAULT NULL COMMENT '功率',
    `torque` varchar(50) DEFAULT NULL COMMENT '扭矩',
    `seats_num` varchar(20) DEFAULT NULL COMMENT '座位数',
    `seats_unit` varchar(20) DEFAULT NULL COMMENT '座位数单位',
    `dimensions` json DEFAULT NULL COMMENT '车身尺寸信息：长宽高、轴距等',
    `performance` json DEFAULT NULL COMMENT '性能参数：加速、最高速度等',
    `safety_rating` varchar(20) DEFAULT NULL COMMENT '安全评级',
    `warranty_info` varchar(100) DEFAULT NULL COMMENT '质保信息',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_model_specs` (`model_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车型规格表';

-- ----------------------------
-- Table structure for cms_car_packages 配置包表
-- ----------------------------
DROP TABLE IF EXISTS `cms_car_packages`;
CREATE TABLE `cms_car_packages` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置包ID',
    `model_id` bigint(20) NOT NULL COMMENT '车型ID',
    `package_code` varchar(50) NOT NULL COMMENT '配置包代码',
    `name` varchar(200) NOT NULL COMMENT '配置包名称',
    `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '配置包价格',
    `features_title` varchar(100) DEFAULT NULL COMMENT '特性标题',
    `features` json DEFAULT NULL COMMENT '配置包特性列表',
    `sort_order` int(11) DEFAULT '0' COMMENT '排序',
    `status` tinyint(4) DEFAULT '0' COMMENT '状态：0-启用，1-禁用',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_model_package` (`model_id`,`package_code`) USING BTREE,
    KEY `idx_model_status` (`model_id`,`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配置包表';

-- ----------------------------
-- Table structure for cms_car_option_types 配置选项类型表
-- ----------------------------
DROP TABLE IF EXISTS `cms_car_option_types`;
CREATE TABLE `cms_car_option_types` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '选项类型ID',
    `type_code` varchar(50) NOT NULL COMMENT '选项类型代码：colors、interiors、wheels、audio等',
    `name` varchar(100) NOT NULL COMMENT '选项类型名称',
    `name_en` varchar(100) DEFAULT NULL COMMENT '英文名称',
    `description` text COMMENT '类型描述',
    `config_schema` json DEFAULT NULL COMMENT '配置字段定义，定义该类型特有的字段结构',
    `sort_order` int(11) DEFAULT '0' COMMENT '排序',
    `status` tinyint(4) DEFAULT '0' COMMENT '状态：0-启用，1-禁用',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_type_code` (`type_code`) USING BTREE,
    KEY `idx_status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配置选项类型表';

-- ----------------------------
-- Table structure for cms_car_model_options 车型配置选项表
-- ----------------------------
DROP TABLE IF EXISTS `cms_car_model_options`;
CREATE TABLE `cms_car_model_options` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置选项ID',
    `model_id` bigint(20) NOT NULL COMMENT '车型ID',
    `option_type_id` bigint(20) NOT NULL COMMENT '选项类型ID',
    `option_code` varchar(50) NOT NULL COMMENT '选项代码',
    `name` varchar(200) NOT NULL COMMENT '选项名称',
    `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '选项价格',
    `image_url` varchar(500) DEFAULT NULL COMMENT '选项图片URL',
    `thumbnail_urls` json DEFAULT NULL COMMENT '缩略图URL数组',
    `required_package` varchar(50) DEFAULT NULL COMMENT '需要的配置包',
    `config_data` json DEFAULT NULL COMMENT '选项配置数据，根据option_type的schema存储',
    `sort_order` int(11) DEFAULT '0' COMMENT '排序',
    `status` tinyint(4) DEFAULT '0' COMMENT '状态：0-启用，1-禁用',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_model_option` (`model_id`,`option_type_id`,`option_code`) USING BTREE,
    KEY `idx_model_type` (`model_id`,`option_type_id`) USING BTREE,
    KEY `idx_option_type` (`option_type_id`) USING BTREE,
    KEY `idx_status` (`status`,`sort_order`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车型配置选项表';

-- ----------------------------
-- 2. 融资方案相关表
-- ----------------------------

-- ----------------------------
-- Table structure for cms_finance_options 融资选项表
-- ----------------------------
DROP TABLE IF EXISTS `cms_finance_options`;
CREATE TABLE `cms_finance_options` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '融资选项ID',
    `option_code` varchar(50) NOT NULL COMMENT '融资选项代码',
    `name` varchar(100) NOT NULL COMMENT '融资选项名称',
    `description` text COMMENT '融资选项描述',
    `sort_order` int(11) DEFAULT '0' COMMENT '排序',
    `status` tinyint(4) DEFAULT '0' COMMENT '状态：0-启用，1-禁用',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_option_code` (`option_code`) USING BTREE,
    KEY `idx_status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='融资选项表';

-- ----------------------------
-- Table structure for cms_finance_terms 融资期限表
-- ----------------------------
DROP TABLE IF EXISTS `cms_finance_terms`;
CREATE TABLE `cms_finance_terms` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '期限ID',
    `option_id` bigint(20) NOT NULL COMMENT '融资选项ID',
    `term_code` varchar(50) NOT NULL COMMENT '期限代码',
    `name` varchar(100) NOT NULL COMMENT '期限名称',
    `rate` decimal(5,4) NOT NULL COMMENT '利率',
    `months` int(11) NOT NULL COMMENT '期限月数',
    `sort_order` int(11) DEFAULT '0' COMMENT '排序',
    `status` tinyint(4) DEFAULT '0' COMMENT '状态：0-启用，1-禁用',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_option_term` (`option_id`,`term_code`) USING BTREE,
    KEY `idx_option_status` (`option_id`,`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='融资期限表';

-- ----------------------------
-- Table structure for cms_down_payment_options 首付选项表
-- ----------------------------
DROP TABLE IF EXISTS `cms_down_payment_options`;
CREATE TABLE `cms_down_payment_options` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '首付选项ID',
    `option_id` bigint(20) NOT NULL COMMENT '融资选项ID',
    `payment_code` varchar(50) NOT NULL COMMENT '首付代码',
    `name` varchar(100) NOT NULL COMMENT '首付名称',
    `value` decimal(3,2) NOT NULL COMMENT '首付比例值',
    `sort_order` int(11) DEFAULT '0' COMMENT '排序',
    `status` tinyint(4) DEFAULT '0' COMMENT '状态：0-启用，1-禁用',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_option_payment` (`option_id`,`payment_code`) USING BTREE,
    KEY `idx_option_status` (`option_id`,`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='首付选项表';

-- ----------------------------
-- Table structure for cms_car_model_finance_plans 车型融资方案关联表
-- ----------------------------
DROP TABLE IF EXISTS `cms_car_model_finance_plans`;
CREATE TABLE `cms_car_model_finance_plans` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '方案ID',
    `model_id` bigint(20) NOT NULL COMMENT '车型ID',
    `finance_option_id` bigint(20) NOT NULL COMMENT '融资选项ID',
    `term_id` bigint(20) DEFAULT NULL COMMENT '期限ID',
    `down_payment_id` bigint(20) DEFAULT NULL COMMENT '首付选项ID',
    `weekly_payment` decimal(8,2) DEFAULT NULL COMMENT '每周付款金额',
    `monthly_payment` decimal(8,2) DEFAULT NULL COMMENT '每月付款金额',
    `total_amount` decimal(10,2) DEFAULT NULL COMMENT '总金额',
    `gfv` decimal(10,2) DEFAULT NULL COMMENT '保证未来价值',
    `km_allowance` int(11) DEFAULT NULL COMMENT '公里数限制',
    `is_default` tinyint(4) DEFAULT '0' COMMENT '是否默认方案',
    `is_featured` tinyint(4) DEFAULT '0' COMMENT '是否推荐方案',
    `sort_order` int(11) DEFAULT '0' COMMENT '排序',
    `status` tinyint(4) DEFAULT '0' COMMENT '状态：0-启用，1-禁用',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_model_finance_plan` (`model_id`,`finance_option_id`,`term_id`,`down_payment_id`) USING BTREE,
    KEY `idx_model_status` (`model_id`,`status`) USING BTREE,
    KEY `idx_finance_option` (`finance_option_id`) USING BTREE,
    KEY `idx_term` (`term_id`) USING BTREE,
    KEY `idx_down_payment` (`down_payment_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车型融资方案关联表';

-- ----------------------------
-- 3. API配置相关表
-- ----------------------------

-- ----------------------------
-- Table structure for cms_api_sql_configs API SQL配置表
-- ----------------------------
DROP TABLE IF EXISTS `cms_api_sql_configs`;
CREATE TABLE `cms_api_sql_configs` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
    `api_code` varchar(100) NOT NULL COMMENT 'API标识码',
    `version` varchar(20) DEFAULT 'default' COMMENT 'API版本，default为默认版本',
    `sql_content` text NOT NULL COMMENT 'SQL查询语句',
    `response_processor` varchar(100) DEFAULT NULL COMMENT '响应处理器类名',
    `description` text COMMENT '配置描述',
    `is_default` tinyint(4) DEFAULT '0' COMMENT '是否默认版本',
    `status` tinyint(4) DEFAULT '0' COMMENT '状态：0-启用，1-禁用',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_api_version` (`api_code`,`version`) USING BTREE,
    KEY `idx_api_default` (`api_code`,`is_default`) USING BTREE,
    KEY `idx_status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API SQL配置表';

-- ----------------------------
-- Table structure for cms_api_param_configs API参数配置表
-- ----------------------------
DROP TABLE IF EXISTS `cms_api_param_configs`;
CREATE TABLE `cms_api_param_configs` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '参数ID',
    `api_config_id` bigint(20) NOT NULL COMMENT 'API配置ID',
    `param_name` varchar(50) NOT NULL COMMENT '参数名',
    `param_type` enum('string','integer','decimal','boolean') DEFAULT 'string' COMMENT '参数类型',
    `is_required` tinyint(4) DEFAULT '1' COMMENT '是否必需',
    `default_value` varchar(200) DEFAULT NULL COMMENT '默认值',
    `validation_rule` varchar(500) DEFAULT NULL COMMENT '验证规则',
    `description` varchar(200) DEFAULT NULL COMMENT '参数描述',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_api_config` (`api_config_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API参数配置表';

-- ----------------------------
-- 4. 前端展示相关表
-- ----------------------------

-- ----------------------------
-- Table structure for cms_car_model_tabs 车型标签表
-- ----------------------------
DROP TABLE IF EXISTS `cms_car_model_tabs`;
CREATE TABLE `cms_car_model_tabs` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签ID',
    `tab_code` varchar(50) NOT NULL COMMENT '标签代码',
    `label` varchar(100) NOT NULL COMMENT '标签名称',
    `sort_order` int(11) DEFAULT '0' COMMENT '排序',
    `status` tinyint(4) DEFAULT '0' COMMENT '状态：0-启用，1-禁用',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_tab_code` (`tab_code`) USING BTREE,
    KEY `idx_status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车型标签表';

-- ----------------------------
-- Table structure for cms_car_model_cards 车型卡片表
-- ----------------------------
DROP TABLE IF EXISTS `cms_car_model_cards`;
CREATE TABLE `cms_car_model_cards` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '卡片ID',
    `tab_id` bigint(20) NOT NULL COMMENT '标签ID',
    `model_id` bigint(20) DEFAULT NULL COMMENT '关联车型ID',
    `name` varchar(100) NOT NULL COMMENT '卡片名称',
    `description` text COMMENT '卡片描述',
    `price_type` enum('weekly','driveaway') NOT NULL COMMENT '价格类型',
    `price` varchar(50) DEFAULT NULL COMMENT '价格',
    `price_footnote` varchar(50) DEFAULT NULL COMMENT '价格脚注',
    `comparison_rate` json DEFAULT NULL COMMENT '比较利率信息',
    `deposit` varchar(50) DEFAULT NULL COMMENT '押金',
    `term` varchar(50) DEFAULT NULL COMMENT '期限',
    `gfv` json DEFAULT NULL COMMENT 'GFV信息',
    `available_for` varchar(200) DEFAULT NULL COMMENT '适用范围',
    `image_url` varchar(500) DEFAULT NULL COMMENT '卡片图片URL',
    `link_url` varchar(200) DEFAULT NULL COMMENT '链接地址',
    `sort_order` int(11) DEFAULT '0' COMMENT '排序',
    `status` tinyint(4) DEFAULT '0' COMMENT '状态：0-启用，1-禁用',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_tab_status` (`tab_id`,`status`) USING BTREE,
    KEY `idx_model` (`model_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车型卡片表';

-- ----------------------------
-- 5. 多语言支持表
-- ----------------------------

-- ----------------------------
-- Table structure for cms_translations 多语言配置表
-- ----------------------------
DROP TABLE IF EXISTS `cms_translations`;
CREATE TABLE `cms_translations` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '翻译ID',
    `table_name` varchar(50) NOT NULL COMMENT '表名',
    `record_id` bigint(20) NOT NULL COMMENT '记录ID',
    `field_name` varchar(50) NOT NULL COMMENT '字段名',
    `language_code` varchar(10) NOT NULL COMMENT '语言代码',
    `translated_value` text COMMENT '翻译值',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_translation` (`table_name`,`record_id`,`field_name`,`language_code`) USING BTREE,
    KEY `idx_table_record` (`table_name`,`record_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='多语言配置表';

-- ----------------------------
-- 添加外键约束
-- ----------------------------
ALTER TABLE `cms_car_models` 
ADD CONSTRAINT `fk_car_models_series` FOREIGN KEY (`series_id`) REFERENCES `cms_car_series` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE `cms_car_model_specs` 
ADD CONSTRAINT `fk_car_specs_model` FOREIGN KEY (`model_id`) REFERENCES `cms_car_models` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `cms_car_packages` 
ADD CONSTRAINT `fk_car_packages_model` FOREIGN KEY (`model_id`) REFERENCES `cms_car_models` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `cms_car_model_options` 
ADD CONSTRAINT `fk_car_options_model` FOREIGN KEY (`model_id`) REFERENCES `cms_car_models` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `fk_car_options_type` FOREIGN KEY (`option_type_id`) REFERENCES `cms_car_option_types` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE `cms_finance_terms` 
ADD CONSTRAINT `fk_finance_terms_option` FOREIGN KEY (`option_id`) REFERENCES `cms_finance_options` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `cms_down_payment_options` 
ADD CONSTRAINT `fk_down_payment_option` FOREIGN KEY (`option_id`) REFERENCES `cms_finance_options` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `cms_car_model_finance_plans` 
ADD CONSTRAINT `fk_finance_plans_model` FOREIGN KEY (`model_id`) REFERENCES `cms_car_models` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `fk_finance_plans_option` FOREIGN KEY (`finance_option_id`) REFERENCES `cms_finance_options` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
ADD CONSTRAINT `fk_finance_plans_term` FOREIGN KEY (`term_id`) REFERENCES `cms_finance_terms` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
ADD CONSTRAINT `fk_finance_plans_payment` FOREIGN KEY (`down_payment_id`) REFERENCES `cms_down_payment_options` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `cms_api_param_configs` 
ADD CONSTRAINT `fk_api_params_config` FOREIGN KEY (`api_config_id`) REFERENCES `cms_api_sql_configs` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `cms_car_model_cards` 
ADD CONSTRAINT `fk_car_cards_tab` FOREIGN KEY (`tab_id`) REFERENCES `cms_car_model_tabs` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `fk_car_cards_model` FOREIGN KEY (`model_id`) REFERENCES `cms_car_models` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- 初始化基础数据
-- ----------------------------

-- 插入基础选项类型
INSERT INTO `cms_car_option_types` (`type_code`, `name`, `name_en`, `description`, `config_schema`, `sort_order`, `status`) VALUES
('colors', '颜色', 'Colors', '车身颜色选项', '{"type":"object","properties":{"is_two_tone":{"type":"boolean","description":"是否双色调"},"color_codes":{"type":"array","description":"CSS颜色代码数组"},"is_premium":{"type":"boolean","description":"是否高级颜色"}}}', 1, 0),
('interiors', '内饰', 'Interiors', '内饰材质和颜色', '{"type":"object","properties":{"material":{"type":"string","description":"材质"},"color":{"type":"string","description":"颜色"}}}', 2, 0),
('wheels', '轮毂', 'Wheels', '轮毂尺寸和样式', '{"type":"object","properties":{"size":{"type":"integer","description":"尺寸(英寸)"},"style":{"type":"string","description":"样式"}}}', 3, 0),
('audio', '音响', 'Audio', '音响系统配置', '{"type":"object","properties":{"brand":{"type":"string","enum":["SONY","Bose","Harman Kardon"]},"speakers_count":{"type":"integer","minimum":4,"maximum":20},"power":{"type":"string","pattern":"^\\d+W$"},"features":{"type":"array","items":{"type":"string"}},"has_subwoofer":{"type":"boolean","default":false}}}', 4, 0);

COMMIT;