package cn.iocoder.yudao.module.cms.service.car.api;

import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SQL执行引擎
 * 负责执行动态SQL查询并返回结果
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class SqlExecutionEngine {

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    // SQL模板缓存
    private final Map<String, String> sqlTemplateCache = new ConcurrentHashMap<>();
    
    // 参数提取正则表达式
    private static final Pattern PARAM_PATTERN = Pattern.compile("#\\{(\\w+)\\}");

    /**
     * 执行SQL查询
     *
     * @param sqlTemplate SQL模板
     * @param params 参数
     * @return 查询结果
     */
    public List<Map<String, Object>> executeQuery(String sqlTemplate, Map<String, Object> params) {
        try {
            // 预处理SQL模板
            String processedSql = preprocessSql(sqlTemplate, params);
            
            log.debug("执行SQL: {}", processedSql);
            log.debug("参数: {}", params);
            
            // 检查SQL中是否包含命名参数（:param格式）
            boolean hasNamedParameters = processedSql.contains(":");
            
            // 执行查询
            if (hasNamedParameters && params != null && !params.isEmpty()) {
                // 使用命名参数查询
                return namedParameterJdbcTemplate.queryForList(processedSql, params);
            } else if (!hasNamedParameters) {
                // 没有参数的普通SQL查询
                return jdbcTemplate.queryForList(processedSql);
            } else {
                // 有命名参数但没有提供参数值，使用空参数Map
                return namedParameterJdbcTemplate.queryForList(processedSql, params != null ? params : new HashMap<>());
            }
            
        } catch (Exception e) {
            log.error("SQL执行失败: {}", sqlTemplate, e);
            throw new RuntimeException("SQL执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证SQL格式
     *
     * @param sqlTemplate SQL模板
     * @return 验证结果
     */
    public boolean validateSqlFormat(String sqlTemplate) {
        if (!StringUtils.hasText(sqlTemplate)) {
            return false;
        }

        try {
            // 基本SQL语法检查
            String upperSql = sqlTemplate.trim().toUpperCase();
            
            // 只允许SELECT语句
            if (!upperSql.startsWith("SELECT")) {
                log.warn("只允许SELECT语句: {}", sqlTemplate);
                return false;
            }
            
            // 检查是否包含危险关键字
            String[] dangerousKeywords = {
                "DROP", "DELETE", "UPDATE", "INSERT", "CREATE", "ALTER", 
                "TRUNCATE", "EXEC", "EXECUTE", "DECLARE", "CALL"
            };
            
            for (String keyword : dangerousKeywords) {
                if (upperSql.contains(keyword)) {
                    log.warn("SQL包含危险关键字{}: {}", keyword, sqlTemplate);
                    return false;
                }
            }
            
            // 检查参数格式
            return validateParameters(sqlTemplate);
            
        } catch (Exception e) {
            log.error("SQL格式验证失败: {}", sqlTemplate, e);
            return false;
        }
    }

    /**
     * 提取SQL中的参数名称
     *
     * @param sqlTemplate SQL模板
     * @return 参数名称列表
     */
    public List<String> extractParameterNames(String sqlTemplate) {
        List<String> paramNames = new ArrayList<>();
        Matcher matcher = PARAM_PATTERN.matcher(sqlTemplate);
        
        while (matcher.find()) {
            String paramName = matcher.group(1);
            if (!paramNames.contains(paramName)) {
                paramNames.add(paramName);
            }
        }
        
        return paramNames;
    }

    /**
     * 预处理SQL模板
     */
    private String preprocessSql(String sqlTemplate, Map<String, Object> params) {
        if (!StringUtils.hasText(sqlTemplate)) {
            throw new IllegalArgumentException("SQL模板不能为空");
        }

        // 使用缓存的SQL模板（如果有）
        String cacheKey = generateCacheKey(sqlTemplate, params);
        if (sqlTemplateCache.containsKey(cacheKey)) {
            return sqlTemplateCache.get(cacheKey);
        }

        // 处理条件SQL片段
        String processedSql = processConditionalFragments(sqlTemplate, params);
        
        // 处理参数占位符（从 #{param} 转换为 :param）
        processedSql = processedSql.replaceAll("#\\{(\\w+)\\}", ":$1");
        
        // 缓存处理后的SQL
        sqlTemplateCache.put(cacheKey, processedSql);
        
        return processedSql;
    }

    /**
     * 处理条件SQL片段
     * 支持类似 <if test="param != null">AND column = #{param}</if> 的语法
     */
    private String processConditionalFragments(String sqlTemplate, Map<String, Object> params) {
        // 简化版条件处理，实际项目中可以使用MyBatis的动态SQL处理逻辑
        String result = sqlTemplate;
        
        // 处理简单的条件判断
        Pattern ifPattern = Pattern.compile("<if\\s+test=\"(\\w+)\\s*!=\\s*null\">(.*?)</if>", Pattern.DOTALL);
        Matcher matcher = ifPattern.matcher(result);
        
        while (matcher.find()) {
            String paramName = matcher.group(1);
            String fragment = matcher.group(2);
            
            if (params != null && params.containsKey(paramName) && params.get(paramName) != null) {
                result = result.replace(matcher.group(0), fragment);
            } else {
                result = result.replace(matcher.group(0), "");
            }
        }
        
        return result;
    }

    /**
     * 验证参数格式
     */
    private boolean validateParameters(String sqlTemplate) {
        List<String> paramNames = extractParameterNames(sqlTemplate);
        
        // 检查参数名是否合法（只允许字母、数字、下划线）
        Pattern validParamPattern = Pattern.compile("^[a-zA-Z_][a-zA-Z0-9_]*$");
        
        for (String paramName : paramNames) {
            if (!validParamPattern.matcher(paramName).matches()) {
                log.warn("非法参数名: {}", paramName);
                return false;
            }
        }
        
        return true;
    }

    /**
     * 生成缓存键
     */
    private String generateCacheKey(String sqlTemplate, Map<String, Object> params) {
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(sqlTemplate.hashCode());
        
        if (params != null) {
            // 只考虑参数的键，不考虑值
            List<String> sortedKeys = new ArrayList<>(params.keySet());
            Collections.sort(sortedKeys);
            keyBuilder.append("_").append(sortedKeys);
        }
        
        return keyBuilder.toString();
    }

    /**
     * 清理SQL缓存
     */
    public void clearSqlCache() {
        sqlTemplateCache.clear();
        log.info("SQL模板缓存已清理");
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cacheSize", sqlTemplateCache.size());
        stats.put("cacheKeys", new ArrayList<>(sqlTemplateCache.keySet()));
        return stats;
    }

    /**
     * 执行SQL EXPLAIN查询
     *
     * @param sqlTemplate SQL模板
     * @return EXPLAIN结果
     */
    public List<Map<String, Object>> explainQuery(String sqlTemplate) {
        return explainQuery(sqlTemplate, new HashMap<>());
    }
    
    /**
     * 执行EXPLAIN分析（带参数）
     * 
     * @param sqlTemplate SQL模板
     * @param params 参数
     * @return EXPLAIN结果
     */
    public List<Map<String, Object>> explainQuery(String sqlTemplate, Map<String, Object> params) {
        try {
            // 预处理SQL模板
            String processedSql = preprocessSql(sqlTemplate, params);
            
            // 为EXPLAIN查询替换参数占位符为实际值
            // 遍历所有参数，将 :param 格式替换为实际值
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                String paramName = entry.getKey();
                Object paramValue = entry.getValue();
                
                // 根据参数类型处理值
                String replacementValue;
                if (paramValue == null) {
                    replacementValue = "NULL";
                } else if (paramValue instanceof Number) {
                    replacementValue = paramValue.toString();
                } else {
                    // 字符串和其他类型需要加引号，并转义单引号
                    replacementValue = "'" + paramValue.toString().replace("'", "''") + "'";
                }
                
                // 替换参数占位符
                processedSql = processedSql.replaceAll(":" + paramName + "\\b", replacementValue);
            }
            
            // 如果还有未替换的参数，使用默认值
            processedSql = processedSql.replaceAll(":([a-zA-Z_][a-zA-Z0-9_]*)", "'sample_value'");
            
            // 构建EXPLAIN语句
            String explainSql = "EXPLAIN " + processedSql;
            
            log.debug("执行EXPLAIN: {}", explainSql);
            
            // 执行EXPLAIN查询
            return jdbcTemplate.queryForList(explainSql);
            
        } catch (Exception e) {
            log.error("EXPLAIN执行失败: {}", sqlTemplate, e);
            throw new RuntimeException("EXPLAIN执行失败: " + e.getMessage(), e);
        }
    }

}