package cn.iocoder.yudao.module.cms.controller.app.car.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * App车型配置器响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "App API - 车型配置器响应")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppCarConfiguratorRespVO {

    @Schema(description = "车型信息")
    private ModelInfo model;

    @Schema(description = "配置包列表")
    private List<PackageInfo> packages;

    @Schema(description = "配置选项")
    private OptionsInfo options;

    @Schema(description = "融资方案列表")
    private List<FinancePlanInfo> financePlans;

    @Schema(description = "车型信息")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ModelInfo {
        
        @Schema(description = "车型ID", example = "tiggo-7")
        private String id;
        
        @Schema(description = "车型名称", example = "TIGGO 7 PRO")
        private String name;
        
        @Schema(description = "车型分类", example = "SUV")
        private String category;
        
        @Schema(description = "车型描述")
        private String description;
        
        @Schema(description = "基础价格", example = "25900.00")
        private BigDecimal basePrice;
        
        @Schema(description = "车型图片URL")
        private String image;
        
        @Schema(description = "车型特性列表")
        private List<String> features;
        
        @Schema(description = "EV图标URL")
        private String evIcon;
        
        @Schema(description = "标识", example = "Value")
        private String badge;
    }

    @Schema(description = "配置包信息")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PackageInfo {
        
        @Schema(description = "配置包ID", example = "luxury-package")
        private String id;
        
        @Schema(description = "配置包名称", example = "豪华配置包")
        private String name;
        
        @Schema(description = "配置包价格", example = "3000.00")
        private BigDecimal price;
        
        @Schema(description = "特性标题", example = "豪华舒适配置")
        private String featuresTitle;
        
        @Schema(description = "配置包特性列表")
        private List<String> features;
    }

    @Schema(description = "配置选项")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OptionsInfo {
        
        @Schema(description = "颜色选项")
        private List<OptionItem> colors;
        
        @Schema(description = "内饰选项")
        private List<OptionItem> interiors;
        
        @Schema(description = "轮毂选项")
        private List<OptionItem> wheels;
    }

    @Schema(description = "选项项目")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OptionItem {
        
        @Schema(description = "选项ID", example = "red-color")
        private String id;
        
        @Schema(description = "选项名称", example = "红色")
        private String name;
        
        @Schema(description = "选项价格", example = "500.00")
        private BigDecimal price;
        
        @Schema(description = "选项图片URL")
        private String image;
        
        @Schema(description = "配置数据")
        private Map<String, Object> configData;
    }

    @Schema(description = "融资方案信息")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FinancePlanInfo {
        
        @Schema(description = "方案ID", example = "1")
        private Long id;
        
        @Schema(description = "融资选项")
        private FinanceOption financeOption;
        
        @Schema(description = "期限信息")
        private TermInfo term;
        
        @Schema(description = "首付信息")
        private DownPaymentInfo downPayment;
        
        @Schema(description = "每周付款", example = "89.00")
        private BigDecimal weeklyPayment;
        
        @Schema(description = "每月付款", example = "386.00")
        private BigDecimal monthlyPayment;
        
        @Schema(description = "保证未来价值", example = "12000.00")
        private BigDecimal gfv;
        
        @Schema(description = "公里数限制", example = "15000")
        private Integer kmAllowance;
        
        @Schema(description = "是否推荐方案")
        private Boolean isFeatured;
    }

    @Schema(description = "融资选项")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FinanceOption {
        
        @Schema(description = "选项ID", example = "pcp")
        private String id;
        
        @Schema(description = "选项名称", example = "Personal Contract Purchase")
        private String name;
        
        @Schema(description = "选项描述")
        private String description;
    }

    @Schema(description = "期限信息")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TermInfo {
        
        @Schema(description = "期限名称", example = "48 months")
        private String name;
        
        @Schema(description = "利率", example = "0.0499")
        private BigDecimal rate;
    }

    @Schema(description = "首付信息")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DownPaymentInfo {
        
        @Schema(description = "首付名称", example = "20% Deposit")
        private String name;
        
        @Schema(description = "首付比例", example = "0.20")
        private BigDecimal value;
    }
}