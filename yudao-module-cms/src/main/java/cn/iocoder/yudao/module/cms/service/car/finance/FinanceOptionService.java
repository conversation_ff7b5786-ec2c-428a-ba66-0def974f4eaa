package cn.iocoder.yudao.module.cms.service.car.finance;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.FinanceOptionCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.FinanceOptionPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.FinanceOptionUpdateReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.finance.FinanceOptionDO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 融资选项 Service 接口
 *
 * <AUTHOR>
 */
public interface FinanceOptionService {

    /**
     * 创建融资选项
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createFinanceOption(@Valid FinanceOptionCreateReqVO createReqVO);

    /**
     * 更新融资选项
     *
     * @param updateReqVO 更新信息
     */
    void updateFinanceOption(@Valid FinanceOptionUpdateReqVO updateReqVO);

    /**
     * 删除融资选项
     *
     * @param id 编号
     */
    void deleteFinanceOption(Long id);

    /**
     * 获得融资选项
     *
     * @param id 编号
     * @return 融资选项
     */
    FinanceOptionDO getFinanceOption(Long id);

    /**
     * 获得融资选项分页
     *
     * @param pageReqVO 分页查询
     * @return 融资选项分页
     */
    PageResult<FinanceOptionDO> getFinanceOptionPage(FinanceOptionPageReqVO pageReqVO);

    /**
     * 获得融资选项列表
     *
     * @param status 状态
     * @return 融资选项列表
     */
    List<FinanceOptionDO> getFinanceOptionList(Integer status);

    /**
     * 根据代码获取融资选项
     *
     * @param optionCode 选项代码
     * @return 融资选项信息
     */
    FinanceOptionDO getFinanceOptionByCode(String optionCode);

}