-- 车型配置管理系统测试数据
-- 作者：开发团队
-- 日期：2025-01-10
-- 版本：v1.0
-- 说明：根据data_comparison_analysis.md设计文档生成的测试数据

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. 车系数据
-- ----------------------------
DELETE FROM cms_car_series WHERE code IN ('tiggo', 'arrizo', 'fulwin');

INSERT INTO cms_car_series (id, code, name, name_en, description, sort_order, status, deleted, tenant_id, creator, create_time, updater, update_time) VALUES
(1, 'tiggo', 'Tiggo 系列', 'Tiggo Series', 'Tiggo SUV系列车型', 1, 0, b'0', 0, '1', NOW(), '1', NOW()),
(2, 'arrizo', 'Arrizo 系列', 'Arrizo Series', 'Arrizo 轿车系列', 2, 0, b'0', 0, '1', NOW(), '1', NOW()),
(3, 'fulwin', 'Fulwin 系列', 'Fulwin Series', 'Fulwin 电动车系列', 3, 0, b'0', 0, '1', NOW(), '1', NOW());

-- ----------------------------
-- 2. 车型基础数据
-- ----------------------------
DELETE FROM cms_car_models WHERE code IN ('tiggo-7', 'tiggo-8', 'arrizo-8', 'fulwin-t9');

INSERT INTO cms_car_models (
    id, series_id, code, name, name_en, category, description, 
    base_price, image_url, ev_icon_url, poster_url, features, 
    badge, end_date, sort_order, status, creator, create_time, updater, update_time
) VALUES
(1, 1, 'tiggo-7', 'Tiggo 7', 'Tiggo 7', 'SUV', '5-seater Medium SUV From $29,990 Driveaway', 
29990.00, '/images/2025UK/models/auto/Tiggo_7.png', NULL, '/images/2025UK/models/auto/Tiggo_7_poster.png',
JSON_ARRAY('5 seats', '1.6L Turbocharged Petrol Engine', '7 DCT automatic transmission', 'Apple CarPlay & Android Auto', 'Advanced Driver Assistance Systems'),
'Value', '2025-06-30', 1, 0, '1', NOW(), '1', NOW()),
-- 注意：没有deleted和tenant_id字段在INSERT中，因为表定义可能不同

(2, 1, 'tiggo-8', 'Tiggo 8 Pro Max', 'Tiggo 8 Pro Max', 'SUV', '7-seater Large SUV From $33,990 Driveaway',
33990.00, '/images/2025UK/models/auto/Tiggo_8_ProMax.png', NULL, '/images/2025UK/models/auto/Tiggo_8_ProMax_poster.png',
JSON_ARRAY('7 seats', '2.0L TGDI engine', '7 DCT automatic transmission', 'Panoramic sunroof', 'Advanced Driver Assistance Systems'),
'Premium', '2025-06-30', 2, 0, '1', NOW(), '1', NOW()),

(3, 2, 'arrizo-8', 'Arrizo 8', 'Arrizo 8', 'Sedan', 'Premium Sedan From $27,990 Driveaway',
27990.00, '/images/2025UK/models/auto/Arrizo_8.png', NULL, '/images/2025UK/models/auto/Arrizo_8_poster.png',
JSON_ARRAY('5 seats', '1.6L TGDI engine', 'CVT transmission', 'SONY audio system', 'Ambient lighting'),
'Luxury', '2025-06-30', 3, 0, '1', NOW(), '1', NOW()),

(4, 3, 'fulwin-t9', 'Fulwin T9', 'Fulwin T9', 'Pickup', 'Electric Pickup From $79,900 Driveaway',
79900.00, '/images/2025UK/models/auto/Fulwin_T9.png', '/images/2025UK/models/auto/Fulwin_T9_ev_icon.png', '/images/2025UK/models/auto/Fulwin_T9_poster.png',
JSON_ARRAY('5 seats', 'Dual Motor AWD', '550km range', 'Vehicle-to-Load capability', 'Advanced Driver Assistance Systems'),
'Electric', '2025-06-30', 4, 0, '1', NOW(), '1', NOW());

-- ----------------------------
-- 3. 车型规格数据
-- ----------------------------
DELETE FROM cms_car_model_specs WHERE model_id IN (1, 2, 3, 4);

INSERT INTO cms_car_model_specs (
    id, model_id, engine, engine_type, fuel_consumption, power, torque, 
    seats_num, seats_unit, dimensions, performance, safety_rating, warranty_info,
    deleted, tenant_id, creator, create_time, updater, update_time
) VALUES
(1, 1, '1.6L Turbo', 'Petrol', '32.7MPG', '108kW', '147PS', '5', 'seats',
JSON_OBJECT('length', '4553', 'width', '1862', 'height', '1696'),
JSON_OBJECT('acceleration', '10.9s', 'top_speed', '190km/h'),
'5-Star', '7-year or 100,000 miles warranty', b'0', 0, '1', NOW(), '1', NOW()),

(2, 2, '2.0L TGDI', 'Petrol', '28.5MPG', '192kW', '400Nm', '7', 'seats',
JSON_OBJECT('length', '4730', 'width', '1860', 'height', '1747'),
JSON_OBJECT('acceleration', '8.9s', 'top_speed', '210km/h'),
'5-Star', '7-year or 100,000 miles warranty', b'0', 0, '1', NOW(), '1', NOW()),

(3, 3, '1.6L TGDI', 'Petrol', '36.2MPG', '145kW', '290Nm', '5', 'seats',
JSON_OBJECT('length', '4780', 'width', '1843', 'height', '1469'),
JSON_OBJECT('acceleration', '7.9s', 'top_speed', '210km/h'),
'5-Star', '7-year or 100,000 miles warranty', b'0', 0, '1', NOW(), '1', NOW()),

(4, 4, 'Dual Motor', 'Electric', '3.5km/kWh', '315kW', '690Nm', '5', 'seats',
JSON_OBJECT('length', '5457', 'width', '2000', 'height', '1900'),
JSON_OBJECT('acceleration', '4.5s', 'top_speed', '170km/h'),
'5-Star', '7-year or 100,000 miles warranty', b'0', 0, '1', NOW(), '1', NOW());

-- ----------------------------
-- 4. 配置选项类型
-- ----------------------------
DELETE FROM cms_car_option_types WHERE type_code IN ('colors', 'interiors', 'wheels');

INSERT INTO cms_car_option_types (
    id, type_code, name, name_en, description, config_schema, sort_order, status, deleted, tenant_id, creator, create_time, updater, update_time
) VALUES
(1, 'colors', '车身颜色', 'Colors', '车身颜色选项',
JSON_OBJECT(
    'is_two_tone', JSON_OBJECT('type', 'boolean', 'description', '是否双色调'),
    'color_codes', JSON_OBJECT('type', 'array', 'description', 'CSS颜色代码数组'),
    'is_premium', JSON_OBJECT('type', 'boolean', 'description', '是否高级颜色')
), 1, 0, b'0', 0, '1', NOW(), '1', NOW()),

(2, 'interiors', '内饰', 'Interiors', '内饰选项',
JSON_OBJECT(
    'material', JSON_OBJECT('type', 'string', 'description', '材质'),
    'color', JSON_OBJECT('type', 'string', 'description', '颜色')
), 2, 0, b'0', 0, '1', NOW(), '1', NOW()),

(3, 'wheels', '轮毂', 'Wheels', '轮毂选项',
JSON_OBJECT(
    'size', JSON_OBJECT('type', 'string', 'description', '尺寸'),
    'type', JSON_OBJECT('type', 'string', 'description', '类型')
), 3, 0, b'0', 0, '1', NOW(), '1', NOW());

-- ----------------------------
-- 5. 车型配置选项（以Tiggo 7为例）
-- ----------------------------
DELETE FROM cms_car_model_options WHERE model_id = 1;

-- Tiggo 7 颜色选项
INSERT INTO cms_car_model_options (
    id, model_id, option_type_id, option_code, name, price, image_url, 
    thumbnail_urls, required_package, config_data, sort_order, status, deleted, tenant_id, creator, create_time, updater, update_time
) VALUES
-- 颜色选项
(1, 1, 1, 'white', 'Arctic White', 0.00, '/images/2025UK/models/colors/Tiggo_7_White.png',
NULL, NULL,
JSON_OBJECT('is_two_tone', false, 'color_codes', JSON_ARRAY('#EEF0F4', '#CBD3DB'), 'is_premium', false),
1, 0, b'0', 0, '1', NOW(), '1', NOW()),

(2, 1, 1, 'black', 'Galaxy Black', 0.00, '/images/2025UK/models/colors/Tiggo_7_Black.png',
NULL, NULL,
JSON_OBJECT('is_two_tone', false, 'color_codes', JSON_ARRAY('#1C1D21'), 'is_premium', false),
2, 0, b'0', 0, '1', NOW(), '1', NOW()),

(3, 1, 1, 'silver', 'Nebula Grey', 0.00, '/images/2025UK/models/colors/Tiggo_7_Silver.png',
NULL, NULL,
JSON_OBJECT('is_two_tone', false, 'color_codes', JSON_ARRAY('#7C878E'), 'is_premium', false),
3, 0, b'0', 0, '1', NOW(), '1', NOW()),

(4, 1, 1, 'red', 'Phoenix Red', 500.00, '/images/2025UK/models/colors/Tiggo_7_Red.png',
NULL, NULL,
JSON_OBJECT('is_two_tone', false, 'color_codes', JSON_ARRAY('#B91C1C'), 'is_premium', true),
4, 0, b'0', 0, '1', NOW(), '1', NOW()),

(5, 1, 1, 'blue', 'Deep Ocean Blue', 500.00, '/images/2025UK/models/colors/Tiggo_7_Blue.png',
NULL, NULL,
JSON_OBJECT('is_two_tone', false, 'color_codes', JSON_ARRAY('#1E3A8A'), 'is_premium', true),
5, 0, b'0', 0, '1', NOW(), '1', NOW()),

(6, 1, 1, 'grey-black', 'Ash Grey + Galaxy Black', 750.00, '/images/2025UK/models/colors/Tiggo_7_Grey_Black.png',
NULL, NULL,
JSON_OBJECT('is_two_tone', true, 'color_codes', JSON_ARRAY('#7C878E', '#1C1D21'), 'is_premium', true),
6, 0, b'0', 0, '1', NOW(), '1', NOW()),

(7, 1, 1, 'white-black', 'Arctic White + Galaxy Black', 750.00, '/images/2025UK/models/colors/Tiggo_7_White_Black.png',
NULL, NULL,
JSON_OBJECT('is_two_tone', true, 'color_codes', JSON_ARRAY('#EEF0F4', '#1C1D21'), 'is_premium', true),
7, 0, b'0', 0, '1', NOW(), '1', NOW()),

-- 内饰选项
(8, 1, 2, 'black-fabric', 'Black Fabric', 0.00, '/images/2025UK/models/interiors/Tiggo_7_Black_Fabric.png',
NULL, NULL,
JSON_OBJECT('material', 'Fabric', 'color', 'Black'),
1, 0, b'0', 0, '1', NOW(), '1', NOW()),

(9, 1, 2, 'black-leather', 'Black Leatherette', 1000.00, '/images/2025UK/models/interiors/Tiggo_7_Black_Leather.png',
NULL, NULL,
JSON_OBJECT('material', 'Leatherette', 'color', 'Black'),
2, 0, b'0', 0, '1', NOW(), '1', NOW()),

-- 轮毂选项
(10, 1, 3, '18-alloy', '18" Alloy Wheels', 0.00, '/images/2025UK/models/wheels/Tiggo_7_18_Alloy.png',
NULL, NULL,
JSON_OBJECT('size', '18"', 'type', 'Alloy'),
1, 0, b'0', 0, '1', NOW(), '1', NOW()),

(11, 1, 3, '19-alloy', '19" Diamond Cut Alloy Wheels', 800.00, '/images/2025UK/models/wheels/Tiggo_7_19_Alloy.png',
NULL, NULL,
JSON_OBJECT('size', '19"', 'type', 'Diamond Cut Alloy'),
2, 0, b'0', 0, '1', NOW(), '1', NOW());

-- ----------------------------
-- 6. 配置包数据
-- ----------------------------
DELETE FROM cms_car_packages WHERE model_id = 1;

INSERT INTO cms_car_packages (
    id, model_id, package_code, name, price, features_title, features, 
    sort_order, status, deleted, tenant_id, creator, create_time, updater, update_time
) VALUES
(1, 1, 'aspire', 'Specification List - Tiggo 7 Aspire', 0.00, 'Included features:',
JSON_ARRAY(
    JSON_ARRAY('18" alloy wheel', true),
    JSON_ARRAY('LED Headlights and rear lights', true),
    JSON_ARRAY('Powered rear lift gate', false),
    JSON_ARRAY('Keyless entry and start', true),
    JSON_ARRAY('Dual zone climate control', true),
    JSON_ARRAY('Panoramic sunroof', false),
    JSON_ARRAY('Wireless phone charging', false)
),
1, 0, b'0', 0, '1', NOW(), '1', NOW()),

(2, 1, 'ultimate', 'Specification List - Tiggo 7 Ultimate', 2500.00, 'Included features:',
JSON_ARRAY(
    JSON_ARRAY('19" alloy wheel', true),
    JSON_ARRAY('LED Headlights and rear lights', true),
    JSON_ARRAY('Powered rear lift gate', true),
    JSON_ARRAY('Keyless entry and start', true),
    JSON_ARRAY('Dual zone climate control', true),
    JSON_ARRAY('Panoramic sunroof', true),
    JSON_ARRAY('Wireless phone charging', true),
    JSON_ARRAY('360-degree camera', true),
    JSON_ARRAY('Heated seats', true)
),
2, 0, b'0', 0, '1', NOW(), '1', NOW());

-- ----------------------------
-- 7. 融资选项
-- ----------------------------
DELETE FROM cms_finance_options WHERE option_code IN ('loan', 'lease', 'full');

INSERT INTO cms_finance_options (
    id, option_code, name, description, sort_order, status, deleted, tenant_id, creator, create_time, updater, update_time
) VALUES
(1, 'full', 'Full Payment', 'Purchase the vehicle with full payment', 1, 0, b'0', 0, '1', NOW(), '1', NOW()),
(2, 'loan', 'Car Loan', 'Purchase the vehicle with installment payments', 2, 0, b'0', 0, '1', NOW(), '1', NOW()),
(3, 'lease', 'Car Lease', 'Lease the vehicle with monthly payments', 3, 0, b'0', 0, '1', NOW(), '1', NOW());

-- ----------------------------
-- 8. 融资期限
-- ----------------------------
DELETE FROM cms_finance_terms WHERE option_id = 2;

INSERT INTO cms_finance_terms (
    id, option_id, term_code, name, rate, months, sort_order, status, deleted, tenant_id, creator, create_time, updater, update_time
) VALUES
(1, 2, '36', '36 months', 0.0490, 36, 1, 0, b'0', 0, '1', NOW(), '1', NOW()),
(2, 2, '48', '48 months', 0.0590, 48, 2, 0, b'0', 0, '1', NOW(), '1', NOW()),
(3, 2, '60', '60 months', 0.0690, 60, 3, 0, b'0', 0, '1', NOW(), '1', NOW());

-- ----------------------------
-- 9. 首付选项
-- ----------------------------
DELETE FROM cms_down_payment_options WHERE option_id = 2;

INSERT INTO cms_down_payment_options (
    id, option_id, payment_code, name, value, sort_order, status, deleted, tenant_id, creator, create_time, updater, update_time
) VALUES
(1, 2, '20', '20%', 0.20, 1, 0, b'0', 0, '1', NOW(), '1', NOW()),
(2, 2, '30', '30%', 0.30, 2, 0, b'0', 0, '1', NOW(), '1', NOW()),
(3, 2, '40', '40%', 0.40, 3, 0, b'0', 0, '1', NOW(), '1', NOW());

-- ----------------------------
-- 10. 车型融资方案
-- ----------------------------
DELETE FROM cms_car_model_finance_plans WHERE model_id = 1;

INSERT INTO cms_car_model_finance_plans (
    id, model_id, finance_option_id, term_id, down_payment_id, 
    weekly_payment, monthly_payment, total_amount, gfv, km_allowance,
    is_default, is_featured, sort_order, status, deleted, tenant_id, creator, create_time, updater, update_time
) VALUES
(1, 1, 2, 3, 1, 90.00, 390.00, 29990.00, 7497.00, 15000, 1, 1, 1, 0, b'0', 0, '1', NOW(), '1', NOW()),
(2, 1, 2, 2, 2, 110.00, 476.00, 29990.00, 8997.00, 15000, 0, 0, 2, 0, b'0', 0, '1', NOW(), '1', NOW()),
(3, 1, 2, 1, 3, 140.00, 607.00, 29990.00, 10497.00, 15000, 0, 0, 3, 0, b'0', 0, '1', NOW(), '1', NOW());

-- ----------------------------
-- 11. API SQL配置表（如果不存在则创建）
-- ----------------------------
CREATE TABLE IF NOT EXISTS `cms_api_sql_configs` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
    `api_code` varchar(100) NOT NULL COMMENT 'API标识码',
    `version` varchar(20) DEFAULT 'default' COMMENT 'API版本，default为默认版本',
    `sql_content` text NOT NULL COMMENT 'SQL查询语句',
    `response_processor` varchar(100) DEFAULT NULL COMMENT '响应处理器类名',
    `description` varchar(500) DEFAULT NULL COMMENT '配置描述',
    `params_schema` json DEFAULT NULL COMMENT '参数schema定义',
    `cache_config` json DEFAULT NULL COMMENT '缓存配置',
    `is_default` tinyint(1) DEFAULT 0 COMMENT '是否默认版本',
    `status` tinyint(4) DEFAULT 0 COMMENT '状态：0-启用，1-禁用',
    `deleted` bit(1) DEFAULT b'0' COMMENT '是否删除',
    `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_api_version` (`api_code`, `version`),
    KEY `idx_api_code` (`api_code`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API SQL配置表';

COMMIT;
SET FOREIGN_KEY_CHECKS = 1;

-- 执行完成后，验证数据
SELECT COUNT(*) as car_models_count FROM cms_car_models;
SELECT COUNT(*) as car_specs_count FROM cms_car_model_specs;
SELECT COUNT(*) as car_options_count FROM cms_car_model_options;
SELECT COUNT(*) as car_packages_count FROM cms_car_packages;
SELECT COUNT(*) as finance_plans_count FROM cms_car_model_finance_plans;