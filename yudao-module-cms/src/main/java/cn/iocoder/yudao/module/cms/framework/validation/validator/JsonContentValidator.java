package cn.iocoder.yudao.module.cms.framework.validation.validator;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.module.cms.framework.validation.annotation.ValidJsonContent;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * JSON 内容验证器
 *
 * <AUTHOR>
 */
public class JsonContentValidator implements ConstraintValidator<ValidJsonContent, String> {

    private int maxLength;
    private boolean sanitize;

    @Override
    public void initialize(ValidJsonContent constraintAnnotation) {
        this.maxLength = constraintAnnotation.maxLength();
        this.sanitize = constraintAnnotation.sanitize();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // 允许为空
        if (StrUtil.isBlank(value)) {
            return false; // JSON 内容不能为空
        }
        
        // 检查长度
        if (value.length() > maxLength) {
            return false;
        }
        
        // 验证 JSON 格式
        try {
            JSONUtil.parse(value);
            
            // 如果需要清理，检查是否包含危险内容
            if (sanitize) {
                String lowerContent = value.toLowerCase();
                if (lowerContent.contains("<script") || 
                    lowerContent.contains("javascript:") ||
                    lowerContent.contains("onerror=") ||
                    lowerContent.contains("onload=")) {
                    return false;
                }
            }
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}