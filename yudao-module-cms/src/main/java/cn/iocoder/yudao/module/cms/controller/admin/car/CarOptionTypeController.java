package cn.iocoder.yudao.module.cms.controller.admin.car;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.*;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarOptionTypeDO;
import cn.iocoder.yudao.module.cms.service.car.CarOptionTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 配置选项类型")
@RestController
@RequestMapping("/cms/car/option-type")
@Validated
public class CarOptionTypeController {

    @Resource
    private CarOptionTypeService carOptionTypeService;

    @PostMapping("/create")
    @Operation(summary = "创建配置选项类型")
    @PreAuthorize("@ss.hasPermission('cms:car-option-type:create')")
    public CommonResult<Long> createCarOptionType(@Valid @RequestBody CarOptionTypeCreateReqVO createReqVO) {
        return success(carOptionTypeService.createCarOptionType(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新配置选项类型")
    @PreAuthorize("@ss.hasPermission('cms:car-option-type:update')")
    public CommonResult<Boolean> updateCarOptionType(@Valid @RequestBody CarOptionTypeUpdateReqVO updateReqVO) {
        carOptionTypeService.updateCarOptionType(updateReqVO);
        return success(true);
    }


    @PutMapping("/update-schema")
    @Operation(summary = "更新配置选项类型")
    @PreAuthorize("@ss.hasPermission('cms:car-option-type:update')")
    public CommonResult<Boolean> updateCarOptionTypeSchema(@Valid @RequestBody CarOptionTypeUpdateSchemaReqVO updateReqVO) {
        carOptionTypeService.updateCarOptionTypeSchema(updateReqVO);
        return success(true);
    }


    @DeleteMapping("/delete")
    @Operation(summary = "删除配置选项类型")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('cms:car-option-type:delete')")
    public CommonResult<Boolean> deleteCarOptionType(@RequestParam("id") Long id) {
        carOptionTypeService.deleteCarOptionType(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得配置选项类型")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('cms:car-option-type:query')")
    public CommonResult<CarOptionTypeRespVO> getCarOptionType(@RequestParam("id") Long id) {
        CarOptionTypeDO carOptionType = carOptionTypeService.getCarOptionType(id);
        return success(BeanUtils.toBean(carOptionType, CarOptionTypeRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得配置选项类型分页")
    @PreAuthorize("@ss.hasPermission('cms:car-option-type:query')")
    public CommonResult<PageResult<CarOptionTypeRespVO>> getCarOptionTypePage(@Valid CarOptionTypePageReqVO pageVO) {
        PageResult<CarOptionTypeDO> pageResult = carOptionTypeService.getCarOptionTypePage(pageVO);
        return success(BeanUtils.toBean(pageResult, CarOptionTypeRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得配置选项类型列表")
    @Parameter(name = "status", description = "状态", example = "1")
    @PreAuthorize("@ss.hasPermission('cms:car-option-type:query')")
    public CommonResult<List<CarOptionTypeRespVO>> getCarOptionTypeList(@RequestParam(value = "status", required = false) Integer status) {
        List<CarOptionTypeDO> list = carOptionTypeService.getCarOptionTypeList(status);
        return success(BeanUtils.toBean(list, CarOptionTypeRespVO.class));
    }

    @GetMapping("/get-by-code")
    @Operation(summary = "根据类型编码获取配置选项类型")
    @Parameter(name = "typeCode", description = "类型编码", required = true, example = "colors")
    @PreAuthorize("@ss.hasPermission('cms:car-option-type:query')")
    public CommonResult<CarOptionTypeRespVO> getCarOptionTypeByCode(@RequestParam("typeCode") String typeCode) {
        CarOptionTypeDO carOptionType = carOptionTypeService.getCarOptionTypeByCode(typeCode);
        return success(BeanUtils.toBean(carOptionType, CarOptionTypeRespVO.class));
    }

    @PostMapping("/validate-schema")
    @Operation(summary = "验证JSON Schema配置")
    @PreAuthorize("@ss.hasPermission('cms:car-option-type:query')")
    public CommonResult<Boolean> validateJsonSchema(@RequestBody Map<String, Object> schema) {
        boolean result = carOptionTypeService.validateJsonSchema(schema);
        return success(result);
    }

    @PostMapping("/validate-config-data")
    @Operation(summary = "验证配置数据是否符合Schema")
    @PreAuthorize("@ss.hasPermission('cms:car-option-type:query')")
    public CommonResult<Boolean> validateConfigData(@RequestParam("typeCode") String typeCode,
                                                     @RequestBody Map<String, Object> configData) {
        boolean result = carOptionTypeService.validateConfigData(typeCode, configData);
        return success(result);
    }

}