# CMS经销商管理系统设计文档 v1.1（最简版）

## 1. 系统概述

将现有前端经销商配置迁移到数据库管理，实现基础的增删改查功能。

## 2. 数据表设计

### 只需要一张表（cms_dealer）
```sql
CREATE TABLE `cms_dealer` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '经销商ID',
  `name` varchar(200) NOT NULL COMMENT '经销商名称',
  `address` varchar(500) NOT NULL COMMENT '详细地址',
  `postcode` varchar(20) DEFAULT NULL COMMENT '邮政编码',
  `phone` varchar(50) DEFAULT NULL COMMENT '联系电话',
  `email` varchar(100) DEFAULT NULL COMMENT '电子邮箱',
  `website` varchar(200) DEFAULT NULL COMMENT '网站地址',
  `latitude` decimal(10,7) NOT NULL COMMENT '纬度',
  `longitude` decimal(10,7) NOT NULL COMMENT '经度',
  `region` varchar(100) NOT NULL COMMENT '地区',
  `services` varchar(500) DEFAULT NULL COMMENT '服务类型，逗号分隔',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_region` (`region`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CMS经销商表';
```

## 3. API接口设计

```java
// 1. 查询经销商列表
GET /api/cms/dealer/page
参数：pageNo, pageSize, name, region, status

// 2. 获取经销商详情
GET /api/cms/dealer/{id}

// 3. 创建经销商（直接生效）
POST /api/cms/dealer/create
Body: {name, address, postcode, phone, email, latitude, longitude, region, services}

// 4. 更新经销商（直接生效）
PUT /api/cms/dealer/update/{id}
Body: {需要更新的字段}

// 5. 删除经销商（直接生效）
DELETE /api/cms/dealer/delete/{id}

// 6. 查询附近经销商
GET /api/cms/dealer/nearby?lat=xxx&lng=xxx&radius=50

// 7. 导出所有经销商数据
GET /api/cms/dealer/export

// 8. 导入经销商数据
POST /api/cms/dealer/import
```

### 3.2 APP端接口

```java
// 1. 综合查询经销商列表
POST /api/app/dealer/list
Body: {
    name: "经销商名称关键词",        // 可选
    region: "地区",                 // 可选
    services: ["Sales", "Service"], // 服务标签数组，可选
    minLat: 51.0,                  // 纬度范围最小值，可选
    maxLat: 52.0,                  // 纬度范围最大值，可选
    minLng: -1.0,                  // 经度范围最小值，可选
    maxLng: 1.0,                   // 经度范围最大值，可选
    centerLat: 51.5,               // 中心点纬度（用于距离排序），可选
    centerLng: 0.0,                // 中心点经度（用于距离排序），可选
    pageNo: 1,
    pageSize: 20
}
Response: {
    code: 0,
    data: {
        list: [{
            id: 1,
            name: "Allen Motor Romford",
            address: "17 London Rd, Romford RM7 9QB",
            phone: "01708 123456",
            latitude: 51.57583109117121,
            longitude: 0.17708755450642785,
            region: "London",
            services: ["Sales", "Service", "Parts"],
            distance: 5.2  // 距离中心点的距离（公里），如果提供了中心点坐标
        }],
        total: 100
    }
}

// 2. 获取经销商详情
GET /api/app/dealer/{id}
Response: {
    code: 0,
    data: {
        id: 1,
        name: "Allen Motor Romford",
        address: "17 London Rd, Romford RM7 9QB Greater London",
        postcode: "RM7 9QB",
        phone: "01708 123456",
        email: "<EMAIL>",
        website: "https://www.allenmotorgroup.co.uk",
        latitude: 51.57583109117121,
        longitude: 0.17708755450642785,
        region: "London",
        services: ["Sales", "Service", "Parts", "Test Drive", "Finance"],
        status: 1
    }
}
```

## 4. Java代码实现

### 4.1 实体类
```java
@Data
@TableName("cms_dealer")
public class DealerDO extends BaseDO {
    private Long id;
    private String name;
    private String address;
    private String postcode;
    private String phone;
    private String email;
    private String website;
    private BigDecimal latitude;
    private BigDecimal longitude;
    private String region;
    private String services; // 逗号分隔的服务列表
    private Integer status;
}
```

### 4.2 Service层
```java
@Service
public class DealerService {
    
    @Resource
    private DealerMapper dealerMapper;
    
    // 创建经销商
    public Long createDealer(DealerCreateReqVO reqVO) {
        DealerDO dealer = BeanUtils.toBean(reqVO, DealerDO.class);
        dealerMapper.insert(dealer);
        return dealer.getId();
    }
    
    // 更新经销商
    public void updateDealer(DealerUpdateReqVO reqVO) {
        DealerDO dealer = BeanUtils.toBean(reqVO, DealerDO.class);
        dealerMapper.updateById(dealer);
    }
    
    // 删除经销商
    public void deleteDealer(Long id) {
        dealerMapper.deleteById(id);
    }
    
    // 查询经销商分页
    public PageResult<DealerDO> getDealerPage(DealerPageReqVO reqVO) {
        return dealerMapper.selectPage(reqVO);
    }
    
    public List<DealerDO> getNearbyDealers(Double lat, Double lng, Integer radius) {
        // 使用Haversine公式计算距离
        String sql = "SELECT *, " +
            "(6371 * acos(cos(radians(?)) * cos(radians(latitude)) * " +
            "cos(radians(longitude) - radians(?)) + sin(radians(?)) * " +
            "sin(radians(latitude)))) AS distance " +
            "FROM cms_dealer " +
            "WHERE deleted = 0 AND status = 1 " +
            "HAVING distance < ? " +
            "ORDER BY distance";
            
        return jdbcTemplate.query(sql, new Object[]{lat, lng, lat, radius}, 
            new BeanPropertyRowMapper<>(DealerDO.class));
    }
}
```

## 5. Controller层

### 5.1 管理端Controller
```java
@RestController
@RequestMapping("/api/cms/dealer")
public class DealerController {
    
    @Resource
    private DealerService dealerService;
    
    @GetMapping("/page")
    @Operation(summary = "获取经销商分页")
    public CommonResult<PageResult<DealerDO>> getDealerPage(DealerPageReqVO reqVO) {
        return success(dealerService.getDealerPage(reqVO));
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取经销商详情")
    public CommonResult<DealerDO> getDealer(@PathVariable("id") Long id) {
        return success(dealerService.getDealer(id));
    }
    
    @PostMapping("/create")
    @Operation(summary = "创建经销商")
    public CommonResult<Long> createDealer(@RequestBody DealerCreateReqVO reqVO) {
        return success(dealerService.createDealer(reqVO));
    }
    
    @PutMapping("/update/{id}")
    @Operation(summary = "更新经销商")
    public CommonResult<Boolean> updateDealer(@PathVariable("id") Long id, 
                                              @RequestBody DealerUpdateReqVO reqVO) {
        reqVO.setId(id);
        dealerService.updateDealer(reqVO);
        return success(true);
    }
    
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除经销商")
    public CommonResult<Boolean> deleteDealer(@PathVariable("id") Long id) {
        dealerService.deleteDealer(id);
        return success(true);
    }
    
    @GetMapping("/nearby")
    @Operation(summary = "查询附近经销商")
    public CommonResult<List<DealerDO>> getNearbyDealers(@RequestParam Double lat,
                                                          @RequestParam Double lng,
                                                          @RequestParam(defaultValue = "50") Integer radius) {
        return success(dealerService.getNearbyDealers(lat, lng, radius));
    }
}
```

### 5.2 APP端Controller
```java
@RestController
@RequestMapping("/api/app/dealer")
public class AppDealerController {
    
    @Resource
    private AppDealerService appDealerService;
    
    @PostMapping("/list")
    @Operation(summary = "APP端综合查询经销商列表")
    public CommonResult<PageResult<AppDealerRespVO>> searchDealers(@RequestBody AppDealerSearchReqVO reqVO) {
        return success(appDealerService.searchDealers(reqVO));
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "APP端获取经销商详情")
    public CommonResult<AppDealerDetailRespVO> getDealerDetail(@PathVariable("id") Long id) {
        return success(appDealerService.getDealerDetail(id));
    }
}
```

### 5.3 APP端请求和响应VO
```java
// 查询请求VO
@Data
public class AppDealerSearchReqVO extends PageParam {
    private String name;           // 名称关键词
    private String region;         // 地区
    private List<String> services; // 服务标签数组
    private Double minLat;         // 纬度范围最小值
    private Double maxLat;         // 纬度范围最大值
    private Double minLng;         // 经度范围最小值
    private Double maxLng;         // 经度范围最大值
    private Double centerLat;      // 中心点纬度（用于距离排序）
    private Double centerLng;      // 中心点经度（用于距离排序）
}

// 列表响应VO
@Data
public class AppDealerRespVO {
    private Long id;
    private String name;
    private String address;
    private String phone;
    private Double latitude;
    private Double longitude;
    private String region;
    private List<String> services;
    private Double distance;       // 距离（公里）
}

// 详情响应VO
@Data
public class AppDealerDetailRespVO {
    private Long id;
    private String name;
    private String address;
    private String postcode;
    private String phone;
    private String email;
    private String website;
    private Double latitude;
    private Double longitude;
    private String region;
    private List<String> services;
    private Integer status;
}
```

### 5.4 APP端Service实现
```java
@Service
public class AppDealerService {
    
    @Resource
    private DealerMapper dealerMapper;
    
    public PageResult<AppDealerRespVO> searchDealers(AppDealerSearchReqVO reqVO) {
        // 构建查询条件
        LambdaQueryWrapper<DealerDO> queryWrapper = new LambdaQueryWrapper<>();
        
        // 名称模糊查询
        if (StrUtil.isNotBlank(reqVO.getName())) {
            queryWrapper.like(DealerDO::getName, reqVO.getName());
        }
        
        // 地区筛选
        if (StrUtil.isNotBlank(reqVO.getRegion())) {
            queryWrapper.eq(DealerDO::getRegion, reqVO.getRegion());
        }
        
        // 服务标签筛选（包含任一服务）
        if (CollUtil.isNotEmpty(reqVO.getServices())) {
            queryWrapper.and(wrapper -> {
                for (String service : reqVO.getServices()) {
                    wrapper.or().like(DealerDO::getServices, service);
                }
            });
        }
        
        // 经纬度范围筛选
        if (reqVO.getMinLat() != null && reqVO.getMaxLat() != null) {
            queryWrapper.between(DealerDO::getLatitude, reqVO.getMinLat(), reqVO.getMaxLat());
        }
        if (reqVO.getMinLng() != null && reqVO.getMaxLng() != null) {
            queryWrapper.between(DealerDO::getLongitude, reqVO.getMinLng(), reqVO.getMaxLng());
        }
        
        // 只查询启用状态的经销商
        queryWrapper.eq(DealerDO::getStatus, 1);
        
        // 执行查询
        Page<DealerDO> page = dealerMapper.selectPage(reqVO, queryWrapper);
        
        // 转换为VO并计算距离
        List<AppDealerRespVO> list = convertToAppVO(page.getRecords(), 
            reqVO.getCenterLat(), reqVO.getCenterLng());
        
        // 如果提供了中心点，按距离排序
        if (reqVO.getCenterLat() != null && reqVO.getCenterLng() != null) {
            list.sort(Comparator.comparing(AppDealerRespVO::getDistance));
        }
        
        return new PageResult<>(list, page.getTotal());
    }
    
    private List<AppDealerRespVO> convertToAppVO(List<DealerDO> dealers, 
                                                  Double centerLat, Double centerLng) {
        return dealers.stream().map(dealer -> {
            AppDealerRespVO vo = new AppDealerRespVO();
            vo.setId(dealer.getId());
            vo.setName(dealer.getName());
            vo.setAddress(dealer.getAddress());
            vo.setPhone(dealer.getPhone());
            vo.setLatitude(dealer.getLatitude().doubleValue());
            vo.setLongitude(dealer.getLongitude().doubleValue());
            vo.setRegion(dealer.getRegion());
            vo.setServices(Arrays.asList(dealer.getServices().split(",")));
            
            // 计算距离
            if (centerLat != null && centerLng != null) {
                vo.setDistance(calculateDistance(centerLat, centerLng, 
                    dealer.getLatitude().doubleValue(), 
                    dealer.getLongitude().doubleValue()));
            }
            
            return vo;
        }).collect(Collectors.toList());
    }
    
    // 计算两点之间的距离（Haversine公式）
    private Double calculateDistance(double lat1, double lng1, double lat2, double lng2) {
        double R = 6371; // 地球半径（公里）
        double dLat = Math.toRadians(lat2 - lat1);
        double dLng = Math.toRadians(lng2 - lng1);
        double a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                   Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) *
                   Math.sin(dLng/2) * Math.sin(dLng/2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }
    
    public AppDealerDetailRespVO getDealerDetail(Long id) {
        DealerDO dealer = dealerMapper.selectById(id);
        if (dealer == null) {
            throw new ServiceException("经销商不存在");
        }
        
        AppDealerDetailRespVO vo = new AppDealerDetailRespVO();
        BeanUtils.copyProperties(dealer, vo);
        vo.setLatitude(dealer.getLatitude().doubleValue());
        vo.setLongitude(dealer.getLongitude().doubleValue());
        vo.setServices(Arrays.asList(dealer.getServices().split(",")));
        
        return vo;
    }
}
```

## 6. 实施步骤

1. **创建数据库表**（10分钟）
2. **开发后端API**（1天）
3. **导入现有数据**（30分钟）
4. **测试**（30分钟）

总计：1.5天

## 7. 数据迁移脚本

```sql
-- 从配置文件导入初始数据
INSERT INTO cms_dealer (name, address, postcode, phone, email, latitude, longitude, region, services, status)
VALUES 
('Allen Motor Romford', '17 London Rd, Romford RM7 9QB Greater London', 'RM7 9QB', 
 '01708 123456', '<EMAIL>', 51.57583109117121, 0.17708755450642785, 
 'London', 'Sales,Service,Parts,Test Drive,Finance', 1),
('Allen Motor Kettering', 'Rothwell Road, Kettering NN16 8UP', 'NN16 8UP',
 '01536 123456', '<EMAIL>', 52.40323399538081, -0.740468458545124,
 'East Midlands', 'Sales,Service,Parts,Test Drive,Finance', 1);
-- 继续添加其他经销商...
```

## 总结

最简化设计：
- **只需1张表**
- **无审批流程**
- **基础CRUD + 地理查询**
- **1.5天完成开发**

---

*文档版本：v1.1（最简版）*  
*更新日期：2024-01-08*