package cn.iocoder.yudao.module.cms.enums.diy;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjUtil;
import cn.iocoder.yudao.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * CMS DIY页面状态枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum CmsDiyPageStatusEnum implements ArrayValuable<Integer> {

    DRAFT(0, "草稿"),
    PUBLISHED(1, "已发布"),
    OFFLINE(2, "已下线");

    public static final Integer[] ARRAYS = Arrays.stream(values()).map(CmsDiyPageStatusEnum::getStatus).toArray(Integer[]::new);


    /**
     * 状态值
     */
    private final Integer status;

    /**
     * 状态名
     */
    private final String name;

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

    public static CmsDiyPageStatusEnum getByStatus(Integer status) {
        return ArrayUtil.firstMatch(statusEnum -> statusEnum.getStatus().equals(status),
                values());
    }
}