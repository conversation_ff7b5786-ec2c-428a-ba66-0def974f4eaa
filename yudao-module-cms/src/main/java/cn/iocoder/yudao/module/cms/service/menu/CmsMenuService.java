package cn.iocoder.yudao.module.cms.service.menu;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.menu.vo.CmsMenuListReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.menu.vo.CmsMenuRespVO;
import cn.iocoder.yudao.module.cms.controller.admin.menu.vo.CmsMenuSaveReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.menu.CmsMenuDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * CMS菜单 Service 接口
 *
 * <AUTHOR>
 */
public interface CmsMenuService {

    // ========== 基础CRUD操作 ==========

    /**
     * 创建菜单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMenu(@Valid CmsMenuSaveReqVO createReqVO);

    /**
     * 更新菜单
     *
     * @param updateReqVO 更新信息
     */
    void updateMenu(@Valid CmsMenuSaveReqVO updateReqVO);

    /**
     * 删除菜单
     *
     * @param id 编号
     */
    void deleteMenu(Long id);

    /**
     * 获得菜单
     *
     * @param id 编号
     * @return 菜单
     */
    CmsMenuDO getMenu(Long id);

    /**
     * 获得菜单列表
     *
     * @param listReqVO 查询条件
     * @return 菜单列表
     */
    List<CmsMenuDO> getMenuList(CmsMenuListReqVO listReqVO);

    /**
     * 获得菜单分页
     *
     * @param pageReqVO 分页查询
     * @return 菜单分页
     */
    PageResult<CmsMenuDO> getMenuPage(CmsMenuListReqVO pageReqVO);

    // ========== 树形结构操作 ==========

    /**
     * 获得菜单树
     *
     * @param listReqVO 查询条件
     * @return 菜单树
     */
    List<CmsMenuRespVO> getMenuTree(CmsMenuListReqVO listReqVO);

    /**
     * 构建菜单路径
     * 从指定菜单ID开始，向上递归构建完整路径
     *
     * @param menuId 菜单ID
     * @return 完整路径，如：/parent/child
     */
    String buildMenuPath(Long menuId);

    /**
     * 获得菜单的所有子菜单ID（递归）
     *
     * @param parentId 父菜单ID
     * @return 子菜单ID列表
     */
    List<Long> getChildrenMenuIds(Long parentId);

    // ========== App端接口 ==========

    /**
     * 获得启用状态的菜单树（App端使用）
     *
     * @return 启用的菜单树
     */
    List<CmsMenuRespVO> getEnabledMenuTree();

    /**
     * 根据路径获得菜单
     *
     * @param path 菜单路径
     * @return 菜单
     */
    CmsMenuDO getMenuByPath(String path);

    // ========== 缓存管理 ==========

    /**
     * 清除菜单缓存
     */
    void clearMenuCache();

    /**
     * 预热菜单缓存
     */
    void warmupMenuCache();

    // ========== 验证方法 ==========

    /**
     * 校验菜单是否存在
     *
     * @param id 菜单编号
     * @return 菜单
     */
    CmsMenuDO validateMenuExists(Long id);

    /**
     * 校验菜单路径唯一性
     *
     * @param id   菜单编号（更新时传入，新增时为null）
     * @param path 菜单路径
     */
    void validatePathUnique(Long id, String path);

    /**
     * 校验父菜单是否存在
     *
     * @param parentId 父菜单ID
     */
    void validateParentMenu(Long parentId);

    /**
     * 校验是否可以删除菜单
     * 检查是否存在子菜单或关联页面
     *
     * @param id 菜单ID
     */
    void validateCanDelete(Long id);

}