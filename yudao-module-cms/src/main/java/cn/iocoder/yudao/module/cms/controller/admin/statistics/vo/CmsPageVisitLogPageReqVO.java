package cn.iocoder.yudao.module.cms.controller.admin.statistics.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * CMS页面访问日志分页 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - CMS页面访问日志分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CmsPageVisitLogPageReqVO extends PageParam {

    @Schema(description = "页面ID", example = "1024")
    private Long pageId;

    @Schema(description = "页面UUID", example = "550e8400-e29b-41d4-a716-************")
    private String pageUuid;

    @Schema(description = "用户ID", example = "2048")
    private Long userId;

    @Schema(description = "访问IP", example = "***********")
    private String ip;

    @Schema(description = "访问时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] visitTime;
}