package cn.iocoder.yudao.module.cms.dal.dataobject.car;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.type.JsonMapTypeHandler;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Map;

/**
 * 配置选项类型 DO
 *
 * <AUTHOR>
 */
@TableName(value = "cms_car_option_types", autoResultMap = true)
@KeySequence("cms_car_option_types_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CarOptionTypeDO extends BaseDO {

    /**
     * 选项类型ID
     */
    @TableId
    private Long id;

    /**
     * 选项类型代码
     *
     * 如：colors、interiors、wheels、audio等
     */
    private String typeCode;

    /**
     * 选项类型名称
     */
    private String name;

    /**
     * 英文名称
     */
    private String nameEn;

    /**
     * 类型描述
     */
    private String description;

    /**
     * 配置字段定义
     *
     * JSON Schema格式，定义该类型特有的字段结构
     */
    @TableField(typeHandler = JsonMapTypeHandler.class)
    private Map<String, Object> configSchema;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 状态
     *
     * 枚举 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer status;

}