package cn.iocoder.yudao.module.cms.controller.admin.car.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 配置包分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CarPackagePageReqVO extends PageParam {

    @Schema(description = "配置包编码", example = "COMFORT_PACKAGE")
    private String packageCode;

    @Schema(description = "配置包名称", example = "舒适性配置包")
    private String name;

    @Schema(description = "最小价格", example = "10000.00")
    private BigDecimal minPrice;

    @Schema(description = "最大价格", example = "50000.00")
    private BigDecimal maxPrice;

//    @Schema(description = "是否热门", example = "true")
//    private Boolean isHot;

    @Schema(description = "状态：0-禁用，1-启用", example = "1")
    private Integer status;

}