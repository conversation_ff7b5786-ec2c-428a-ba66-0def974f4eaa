package cn.iocoder.yudao.module.cms.framework.validation.annotation;

import cn.iocoder.yudao.module.cms.framework.validation.validator.PathValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * 路径格式验证注解
 *
 * <AUTHOR>
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = PathValidator.class)
public @interface ValidPath {

    String message() default "路径格式不正确，只允许字母、数字、中划线和下划线";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    /**
     * 是否允许为空
     */
    boolean allowEmpty() default false;

    /**
     * 最大路径深度
     */
    int maxDepth() default 10;
}