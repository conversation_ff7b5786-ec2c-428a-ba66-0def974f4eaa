package cn.iocoder.yudao.module.cms.dal.dataobject.car;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.type.JsonStringListTypeHandler;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 车型 DO
 *
 * <AUTHOR>
 */
@TableName(value = "cms_car_models", autoResultMap = true)
@KeySequence("cms_car_models_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CarModelDO extends BaseDO {

    /**
     * 车型ID
     */
    @TableId
    private Long id;

    /**
     * 车系ID
     */
    private Long seriesId;

    /**
     * 车型代码
     */
    private String code;

    /**
     * 车型名称
     */
    private String name;

    /**
     * 英文名称
     */
    private String nameEn;

    /**
     * 车型描述
     */
    private String description;

    /**
     * 车型分类
     */
    private String category;

    /**
     * 基础价格
     */
    private BigDecimal basePrice;

    /**
     * 主图片URL
     */
    private String imageUrl;

    /**
     * EV图标URL
     */
    private String evIconUrl;

    /**
     * 配置图URL
     */
    private String posterUrl;

    /**
     * 车型特性列表
     */
    @TableField(typeHandler = JsonStringListTypeHandler.class)
    private List<String> features;

    /**
     * 标识
     *
     * 如：Value、Luxury
     */
    private String badge;

    /**
     * 优惠截止日期
     */
    private LocalDate endDate;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 状态
     *
     * 枚举 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer status;

}