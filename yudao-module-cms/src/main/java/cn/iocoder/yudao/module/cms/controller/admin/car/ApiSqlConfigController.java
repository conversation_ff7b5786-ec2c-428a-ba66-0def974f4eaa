package cn.iocoder.yudao.module.cms.controller.admin.car;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.api.ApiSqlConfigCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.api.ApiSqlConfigPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.api.ApiSqlConfigRespVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.api.ApiSqlConfigUpdateReqVO;
import cn.iocoder.yudao.module.cms.convert.car.ApiSqlConfigConvert;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.api.ApiSqlConfigDO;
import cn.iocoder.yudao.module.cms.service.car.api.ApiSqlConfigService;
import cn.iocoder.yudao.module.cms.service.car.api.DatabaseSqlApiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - API SQL配置
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - API SQL配置")
@RestController
@RequestMapping("/cms/api-config")
@Validated
@Slf4j
public class ApiSqlConfigController {

    @Resource
    private ApiSqlConfigService apiSqlConfigService;
    
    @Resource
    private DatabaseSqlApiService databaseSqlApiService;

    @PostMapping("/create")
    @Operation(summary = "创建API SQL配置")
    @PreAuthorize("@ss.hasPermission('cms:api-sql-config:create')")
    public CommonResult<Long> createApiSqlConfig(@Valid @RequestBody ApiSqlConfigCreateReqVO createReqVO) {
        return success(apiSqlConfigService.createApiSqlConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新API SQL配置")
    @PreAuthorize("@ss.hasPermission('cms:api-sql-config:update')")
    public CommonResult<Boolean> updateApiSqlConfig(@Valid @RequestBody ApiSqlConfigUpdateReqVO updateReqVO) {
        apiSqlConfigService.updateApiSqlConfig(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除API SQL配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('cms:api-sql-config:delete')")
    public CommonResult<Boolean> deleteApiSqlConfig(@RequestParam("id") Long id) {
        apiSqlConfigService.deleteApiSqlConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得API SQL配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('cms:api-sql-config:query')")
    public CommonResult<ApiSqlConfigRespVO> getApiSqlConfig(@RequestParam("id") Long id) {
        ApiSqlConfigDO apiSqlConfig = apiSqlConfigService.getApiSqlConfig(id);
        return success(ApiSqlConfigConvert.INSTANCE.convert(apiSqlConfig));
    }

    @GetMapping("/page")
    @Operation(summary = "获得API SQL配置分页")
    @PreAuthorize("@ss.hasPermission('cms:api-sql-config:query')")
    public CommonResult<PageResult<ApiSqlConfigRespVO>> getApiSqlConfigPage(@Valid ApiSqlConfigPageReqVO pageReqVO) {
        PageResult<ApiSqlConfigDO> pageResult = apiSqlConfigService.getApiSqlConfigPage(pageReqVO);
        return success(ApiSqlConfigConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出API SQL配置 Excel")
    @PreAuthorize("@ss.hasPermission('cms:api-sql-config:export')")
    public void exportApiSqlConfigExcel(ApiSqlConfigPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ApiSqlConfigDO> list = apiSqlConfigService.getApiSqlConfigPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "API SQL配置.xls", "数据", ApiSqlConfigRespVO.class,
                        ApiSqlConfigConvert.INSTANCE.convertList(list));
    }

    @PostMapping("/test/{id}")
    @Operation(summary = "测试API SQL配置")
    @PreAuthorize("@ss.hasPermission('cms:api-sql-config:test')")
    public CommonResult<Map<String, Object>> testApiSqlConfig(
            @Parameter(description = "配置ID", required = true) @PathVariable Long id,
            @RequestBody Map<String, Object> testParams) {
        
        log.info("测试API SQL配置: configId={}, params={}", id, testParams);
        Map<String, Object> result = databaseSqlApiService.testApiSqlConfig(id, testParams);
        return success(result);
    }

    @PostMapping("/set-default/{id}")
    @Operation(summary = "设置为默认版本")
    @PreAuthorize("@ss.hasPermission('cms:api-sql-config:update')")
    public CommonResult<Boolean> setDefaultVersion(@PathVariable Long id) {
        apiSqlConfigService.setAsDefaultVersion(id);
        return success(true);
    }

    @PostMapping("/validate")
    @Operation(summary = "验证API SQL配置")
    @PreAuthorize("@ss.hasPermission('cms:api-sql-config:validate')")
    public CommonResult<Boolean> validateApiConfig(@RequestBody Map<String, String> request) {
        String apiCode = request.get("apiCode");
        String sqlContent = request.get("sqlContent");
        
        boolean isValid = databaseSqlApiService.validateApiConfig(apiCode, sqlContent);
        return success(isValid);
    }

    @GetMapping("/parameters/{apiCode}")
    @Operation(summary = "获取API参数列表")
    @PreAuthorize("@ss.hasPermission('cms:api-sql-config:query')")
    public CommonResult<List<String>> getApiParameters(
            @PathVariable String apiCode,
            @RequestParam(required = false) String version) {
        
        List<String> parameters = databaseSqlApiService.getApiParameters(apiCode, version);
        return success(parameters);
    }

    @GetMapping("/api-codes")
    @Operation(summary = "获取所有API代码列表")
    @PreAuthorize("@ss.hasPermission('cms:api-sql-config:query')")
    public CommonResult<List<String>> getApiCodes() {
        List<String> apiCodes = apiSqlConfigService.getAllApiCodes();
        return success(apiCodes);
    }

    @GetMapping("/versions/{apiCode}")
    @Operation(summary = "获取API所有版本")
    @PreAuthorize("@ss.hasPermission('cms:api-sql-config:query')")
    public CommonResult<List<ApiSqlConfigRespVO>> getApiVersions(@PathVariable String apiCode) {
        List<ApiSqlConfigDO> configs = apiSqlConfigService.getApiSqlConfigListByCode(apiCode);
        return success(ApiSqlConfigConvert.INSTANCE.convertList(configs));
    }

    @PostMapping("/copy/{id}")
    @Operation(summary = "复制API配置为新版本")
    @PreAuthorize("@ss.hasPermission('cms:api-sql-config:create')")
    public CommonResult<Long> copyApiConfig(
            @PathVariable Long id,
            @RequestParam String newVersion) {
        
        Long newConfigId = apiSqlConfigService.copyApiConfig(id, newVersion);
        return success(newConfigId);
    }

    @PostMapping("/refresh-cache/{apiCode}")
    @Operation(summary = "刷新API缓存")
    @PreAuthorize("@ss.hasPermission('cms:api-sql-config:update')")
    public CommonResult<Boolean> refreshApiCache(@PathVariable String apiCode) {
        databaseSqlApiService.refreshApiCache(apiCode);
        return success(true);
    }

    @GetMapping("/stats")
    @Operation(summary = "获取API统计信息")
    @PreAuthorize("@ss.hasPermission('cms:api-sql-config:query')")
    public CommonResult<Map<String, Object>> getApiStats() {
        Map<String, Object> stats = databaseSqlApiService.getApiStats();
        return success(stats);
    }

    @GetMapping("/list")
    @Operation(summary = "获得API SQL配置列表")
    @PreAuthorize("@ss.hasPermission('cms:api-sql-config:query')")
    public CommonResult<List<ApiSqlConfigRespVO>> getApiSqlConfigList(
            @Parameter(description = "状态", example = "0") @RequestParam(required = false) Integer status) {
        List<ApiSqlConfigDO> list = apiSqlConfigService.getApiSqlConfigList(status);
        return success(ApiSqlConfigConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/get-by-code-version")
    @Operation(summary = "根据API代码和版本获取配置")
    @PreAuthorize("@ss.hasPermission('cms:api-sql-config:query')")
    public CommonResult<ApiSqlConfigRespVO> getApiSqlConfigByCodeAndVersion(
            @Parameter(description = "API代码", required = true) @RequestParam String apiCode,
            @Parameter(description = "版本", required = true) @RequestParam String version) {
        ApiSqlConfigDO config = apiSqlConfigService.getApiSqlConfigByCodeAndVersion(apiCode, version);
        return success(ApiSqlConfigConvert.INSTANCE.convert(config));
    }

    @GetMapping("/get-default/{apiCode}")
    @Operation(summary = "获取默认API配置")
    @PreAuthorize("@ss.hasPermission('cms:api-sql-config:query')")
    public CommonResult<ApiSqlConfigRespVO> getDefaultApiSqlConfig(@PathVariable String apiCode) {
        ApiSqlConfigDO config = apiSqlConfigService.getDefaultApiSqlConfig(apiCode);
        return success(ApiSqlConfigConvert.INSTANCE.convert(config));
    }

    @PostMapping("/validate-sql")
    @Operation(summary = "验证SQL格式")
    @PreAuthorize("@ss.hasPermission('cms:api-sql-config:validate')")
    public CommonResult<Boolean> validateSqlFormat(@RequestBody Map<String, String> request) {
        String sqlContent = request.get("sqlContent");
        boolean isValid = apiSqlConfigService.validateSqlFormat(sqlContent);
        return success(isValid);
    }

    @PostMapping("/test-sql")
    @Operation(summary = "测试SQL执行")
    @PreAuthorize("@ss.hasPermission('cms:api-sql-config:test')")
    public CommonResult<List<Map<String, Object>>> testSqlExecution(@RequestBody Map<String, Object> request) {
        String sqlContent = (String) request.get("sqlContent");
        @SuppressWarnings("unchecked")
        Map<String, Object> params = (Map<String, Object>) request.get("parameters");
        if (params == null) {
            params = new HashMap<>();
        }
        List<Map<String, Object>> result = apiSqlConfigService.testSqlExecution(sqlContent, params);
        return success(result);
    }

    @PostMapping("/explain-sql")
    @Operation(summary = "解释SQL执行计划")
    @PreAuthorize("@ss.hasPermission('cms:api-sql-config:query')")
    public CommonResult<List<Map<String, Object>>> explainSql(@RequestBody Map<String, Object> request) {
        String sqlContent = (String) request.get("sqlContent");
        @SuppressWarnings("unchecked")
        Map<String, Object> params = (Map<String, Object>) request.get("parameters");
        if (params == null) {
            params = new HashMap<>();
        }
        List<Map<String, Object>> result = apiSqlConfigService.explainSql(sqlContent, params);
        return success(result);
    }
}