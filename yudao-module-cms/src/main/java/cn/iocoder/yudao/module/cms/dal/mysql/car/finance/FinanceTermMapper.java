package cn.iocoder.yudao.module.cms.dal.mysql.car.finance;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.finance.FinanceTermDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 融资期限 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface FinanceTermMapper extends BaseMapperX<FinanceTermDO> {

    default PageResult<FinanceTermDO> selectPage(Long optionId, String name, Integer status,
                                                  Integer pageNo, Integer pageSize) {
        return selectPage(new PageParam().setPageNo(pageNo).setPageSize(pageSize),new LambdaQueryWrapperX<FinanceTermDO>()
                .eqIfPresent(FinanceTermDO::getOptionId, optionId)
                .likeIfPresent(FinanceTermDO::getName, name)
                .eqIfPresent(FinanceTermDO::getStatus, status)
                .orderByAsc(FinanceTermDO::getSortOrder)
                .orderByDesc(FinanceTermDO::getId));
    }

    default FinanceTermDO selectByCode(String termCode) {
        return selectOne(FinanceTermDO::getTermCode, termCode);
    }

    default List<FinanceTermDO> selectListByOptionId(Long optionId) {
        return selectList(new LambdaQueryWrapperX<FinanceTermDO>()
                .eq(FinanceTermDO::getOptionId, optionId)
                .eq(FinanceTermDO::getStatus, 0)
                .orderByAsc(FinanceTermDO::getSortOrder));
    }

    default List<FinanceTermDO> selectListByStatus(Integer status) {
        return selectList(new LambdaQueryWrapperX<FinanceTermDO>()
                .eqIfPresent(FinanceTermDO::getStatus, status)
                .orderByAsc(FinanceTermDO::getSortOrder));
    }

}