package cn.iocoder.yudao.module.cms.convert.diy;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.diy.vo.page.*;
import cn.iocoder.yudao.module.cms.controller.app.diy.vo.AppCmsDiyPageDetailRespVO;
import cn.iocoder.yudao.module.cms.controller.app.diy.vo.AppCmsDiyPageSearchRespVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * CMS DIY 页面 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface CmsDiyPageConvert {

    CmsDiyPageConvert INSTANCE = Mappers.getMapper(CmsDiyPageConvert.class);

    CmsDiyPageDO convert(CmsDiyPageCreateReqVO bean);

    CmsDiyPageDO convert(CmsDiyPageUpdateReqVO bean);

    CmsDiyPageRespVO convert(CmsDiyPageDO bean);

    List<CmsDiyPageRespVO> convertList(List<CmsDiyPageDO> list);

    PageResult<CmsDiyPageRespVO> convertPage(PageResult<CmsDiyPageDO> page);

    @Mapping(target = "publishedContent", source = "content")
    AppCmsDiyPageDetailRespVO convertAppDetail(CmsDiyPageDO bean);

    List<AppCmsDiyPageSearchRespVO> convertAppSearchList(List<CmsDiyPageDO> list);
}