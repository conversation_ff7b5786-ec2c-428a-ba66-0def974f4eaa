package cn.iocoder.yudao.module.cms.convert.menu;

import cn.iocoder.yudao.module.cms.controller.admin.menu.vo.CmsMenuRespVO;
import cn.iocoder.yudao.module.cms.controller.admin.menu.vo.CmsMenuSaveReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.menu.CmsMenuDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * CMS菜单 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface CmsMenuConvert {

    CmsMenuConvert INSTANCE = Mappers.getMapper(CmsMenuConvert.class);

    CmsMenuDO convert(CmsMenuSaveReqVO bean);

    CmsMenuRespVO convert(CmsMenuDO bean);

    List<CmsMenuRespVO> convertList(List<CmsMenuDO> list);

}