package cn.iocoder.yudao.module.cms.controller.app.diy;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.module.cms.controller.app.diy.vo.AppCmsDiyPageRespVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageDO;
import cn.iocoder.yudao.module.cms.service.diy.CmsDiyPageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * App端 - CMS DIY页面 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "App端 - CMS DIY页面")
@RestController
@RequestMapping("/app/cms/diy-page")
@Validated
@Slf4j
public class AppCmsDiyPageController {

    @Resource
    private CmsDiyPageService diyPageService;

    @GetMapping("/get")
    @Operation(summary = "获得已发布页面")
    @Parameter(name = "id", description = "页面ID", required = true, example = "1024")
    @PermitAll
    public CommonResult<AppCmsDiyPageRespVO> getPublishedPage(@RequestParam("id") Long id) {
        recordPageVisit(id);
        
        CmsDiyPageDO page = diyPageService.getPublishedPage(id);
        return success(BeanUtils.toBean(page, AppCmsDiyPageRespVO.class));
    }

    @GetMapping("/by-uuid")
    @Operation(summary = "根据UUID获得已发布页面")
    @Parameter(name = "uuid", description = "页面UUID", required = true, example = "550e8400-e29b-41d4-a716-************")
    @PermitAll
    public CommonResult<AppCmsDiyPageRespVO> getPublishedPageByUuid(@RequestParam("uuid") String uuid) {
        CmsDiyPageDO page = diyPageService.getPublishedPageByUuid(uuid);
        if (page != null) {
            recordPageVisit(page.getId());
        }
        return success(BeanUtils.toBean(page, AppCmsDiyPageRespVO.class));
    }

    @GetMapping("/by-path")
    @Operation(summary = "根据路径获得已发布页面")
    @Parameter(name = "path", description = "页面路径", required = true, example = "/home/<USER>")
    @PermitAll
    public CommonResult<AppCmsDiyPageRespVO> getPublishedPageByPath(@RequestParam("path") String path) {
        CmsDiyPageDO page = diyPageService.getPublishedPageByPath(path);
        if (page != null) {
            recordPageVisit(page.getId());
        }
        return success(BeanUtils.toBean(page, AppCmsDiyPageRespVO.class));
    }

    @GetMapping("/search")
    @Operation(summary = "搜索已发布页面")
    @Parameter(name = "keyword", description = "搜索关键词", required = true, example = "首页")
    @PermitAll
    public CommonResult<List<AppCmsDiyPageRespVO>> searchPublishedPages(@RequestParam("keyword") String keyword) {
        List<CmsDiyPageDO> pages = diyPageService.searchPublishedPages(keyword);
        List<AppCmsDiyPageRespVO> result = pages.stream()
                .map(page -> BeanUtils.toBean(page, AppCmsDiyPageRespVO.class))
                .collect(Collectors.toList());
        return success(result);
    }

    @PostMapping("/visit")
    @Operation(summary = "记录页面访问")
    @Parameter(name = "pageId", description = "页面ID", required = true)
    @Parameter(name = "visitorUuid", description = "访客UUID", required = false)
    @PermitAll
    public CommonResult<Boolean> recordVisit(@RequestParam("pageId") Long pageId,
                                             @RequestParam(value = "visitorUuid", required = false) String visitorUuid) {
        recordPageVisit(pageId, visitorUuid);
        return success(true);
    }

    private void recordPageVisit(Long pageId) {
        recordPageVisit(pageId, null);
    }

    private void recordPageVisit(Long pageId, String visitorUuid) {
        try {
            HttpServletRequest request = ServletUtils.getRequest();
            if (request != null && pageId != null) {
                // 生成访客UUID，如果没有提供的话
                if (StrUtil.isBlank(visitorUuid)) {
                    // 可以基于IP、User-Agent等生成访客标识
                    String ip = ServletUtils.getClientIP(request);
                    String userAgent = request.getHeader("User-Agent");
                    visitorUuid = StrUtil.isNotBlank(ip) ? ip : "unknown";
                }
                
                diyPageService.recordPageVisit(pageId, visitorUuid);
            }
        } catch (Exception e) {
            log.error("[recordPageVisit][记录页面访问失败，页面ID：{}]", pageId, e);
        }
    }
}