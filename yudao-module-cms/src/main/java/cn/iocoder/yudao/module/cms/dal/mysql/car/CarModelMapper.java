package cn.iocoder.yudao.module.cms.dal.mysql.car;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelPageReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarModelDO;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.List;

/**
 * 车型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CarModelMapper extends BaseMapperX<CarModelDO> {

    default PageResult<CarModelDO> selectPage(CarModelPageReqVO pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<CarModelDO>()
                .likeIfPresent(CarModelDO::getCode, pageReqVO.getCode())
                .likeIfPresent(CarModelDO::getName, pageReqVO.getName())
                .eqIfPresent(CarModelDO::getSeriesId, pageReqVO.getSeriesId())
                .geIfPresent(CarModelDO::getBasePrice, pageReqVO.getMinGuidePrice())
                .leIfPresent(CarModelDO::getBasePrice, pageReqVO.getMaxGuidePrice())
                .eqIfPresent(CarModelDO::getStatus, pageReqVO.getStatus())
                .orderByAsc(CarModelDO::getSortOrder)
                .orderByDesc(CarModelDO::getId));
    }

    default CarModelDO selectByCode(String code) {
        return selectOne(CarModelDO::getCode, code);
    }

    default List<CarModelDO> selectListBySeriesIdAndStatus(Long seriesId, Integer status) {
        return selectList(new LambdaQueryWrapperX<CarModelDO>()
                .eqIfPresent(CarModelDO::getSeriesId, seriesId)
                .eqIfPresent(CarModelDO::getStatus, status)
                .orderByAsc(CarModelDO::getSortOrder)
                .orderByDesc(CarModelDO::getId));
    }

    default List<CarModelDO> selectListByPriceRange(BigDecimal minPrice, BigDecimal maxPrice, Integer status) {
        return selectList(new LambdaQueryWrapperX<CarModelDO>()
                .geIfPresent(CarModelDO::getBasePrice, minPrice)
                .leIfPresent(CarModelDO::getBasePrice, maxPrice)
                .eqIfPresent(CarModelDO::getStatus, status)
                .orderByAsc(CarModelDO::getBasePrice)
                .orderByAsc(CarModelDO::getSortOrder));
    }

    default List<CarModelDO> selectHotCarModelList(Integer limit) {
        return selectList(new LambdaQueryWrapperX<CarModelDO>()
                .eq(CarModelDO::getStatus, 1)
                .orderByAsc(CarModelDO::getSortOrder)
                .orderByDesc(CarModelDO::getId)
                .last("LIMIT " + (limit != null ? limit : 10)));
    }

}