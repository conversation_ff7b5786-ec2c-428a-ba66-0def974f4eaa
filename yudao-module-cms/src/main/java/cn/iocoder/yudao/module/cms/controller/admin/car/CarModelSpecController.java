package cn.iocoder.yudao.module.cms.controller.admin.car;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelSpecCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelSpecPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelSpecRespVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelSpecUpdateReqVO;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarModelSpecDO;
import cn.iocoder.yudao.module.cms.service.car.CarModelSpecService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 车型规格管理")
@RestController
@RequestMapping("/cms/car/model-spec")
@Validated
public class CarModelSpecController {

    @Resource
    private CarModelSpecService carModelSpecService;

    @PostMapping("/create")
    @Operation(summary = "创建车型规格")
    @PreAuthorize("@ss.hasPermission('cms:car-model-spec:create')")
    public CommonResult<Long> createCarModelSpec(@Valid @RequestBody CarModelSpecCreateReqVO createReqVO) {
        return success(carModelSpecService.createCarModelSpec(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新车型规格")
    @PreAuthorize("@ss.hasPermission('cms:car-model-spec:update')")
    public CommonResult<Boolean> updateCarModelSpec(@Valid @RequestBody CarModelSpecUpdateReqVO updateReqVO) {
        carModelSpecService.updateCarModelSpec(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除车型规格")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('cms:car-model-spec:delete')")
    public CommonResult<Boolean> deleteCarModelSpec(@RequestParam("id") Long id) {
        carModelSpecService.deleteCarModelSpec(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得车型规格详情")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('cms:car-model-spec:query')")
    public CommonResult<CarModelSpecRespVO> getCarModelSpec(@RequestParam("id") Long id) {
        CarModelSpecDO carModelSpec = carModelSpecService.getCarModelSpec(id);
        return success(BeanUtils.toBean(carModelSpec, CarModelSpecRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得车型规格分页")
    @PreAuthorize("@ss.hasPermission('cms:car-model-spec:query')")
    public CommonResult<PageResult<CarModelSpecRespVO>> getCarModelSpecPage(@Valid CarModelSpecPageReqVO pageReqVO) {
        PageResult<CarModelSpecDO> pageResult = carModelSpecService.getCarModelSpecPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CarModelSpecRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得车型规格列表")
    @Parameter(name = "modelId", description = "车型ID", required = true, example = "1024")
    @Parameter(name = "category", description = "规格分类", example = "ENGINE")
    @Parameter(name = "status", description = "状态", example = "1")
    @PreAuthorize("@ss.hasPermission('cms:car-model-spec:query')")
    public CommonResult<List<CarModelSpecRespVO>> getCarModelSpecList(@RequestParam("modelId") Long modelId,
                                                                     @RequestParam(value = "category", required = false) String category,
                                                                     @RequestParam(value = "status", required = false) Integer status) {
        List<CarModelSpecDO> list = carModelSpecService.getCarModelSpecList(modelId, category, status);
        return success(BeanUtils.toBean(list, CarModelSpecRespVO.class));
    }

    @GetMapping("/list-by-group")
    @Operation(summary = "根据分组获取车型规格列表")
    @Parameter(name = "modelId", description = "车型ID", required = true, example = "1024")
    @Parameter(name = "specGroup", description = "规格分组", example = "动力系统")
    @PreAuthorize("@ss.hasPermission('cms:car-model-spec:query')")
    public CommonResult<List<CarModelSpecRespVO>> getCarModelSpecListByGroup(@RequestParam("modelId") Long modelId,
                                                                            @RequestParam(value = "specGroup", required = false) String specGroup) {
        List<CarModelSpecDO> list = carModelSpecService.getCarModelSpecListByGroup(modelId, specGroup);
        return success(BeanUtils.toBean(list, CarModelSpecRespVO.class));
    }

    @PostMapping("/batch-create")
    @Operation(summary = "批量创建车型规格")
    @Parameter(name = "modelId", description = "车型ID", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('cms:car-model-spec:create')")
    public CommonResult<Boolean> batchCreateCarModelSpec(@RequestParam("modelId") Long modelId,
                                                        @Valid @RequestBody List<CarModelSpecCreateReqVO> createReqVOList) {
        carModelSpecService.batchCreateCarModelSpec(modelId, createReqVOList);
        return success(true);
    }

}