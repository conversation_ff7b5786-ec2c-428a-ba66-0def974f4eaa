package cn.iocoder.yudao.module.cms.framework.cache.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.NotNull;
import java.time.Duration;

/**
 * CMS 缓存配置属性
 *
 * <AUTHOR>
 */
@ConfigurationProperties("yudao.cms.cache")
@Data
@Validated
public class CmsCacheProperties {

    /**
     * 是否启用缓存
     */
    @NotNull(message = "缓存启用标识不能为空")
    private Boolean enabled = true;

    /**
     * L1 缓存配置（Caffeine）
     */
    @NotNull(message = "L1缓存配置不能为空")
    private L1Cache l1 = new L1Cache();

    /**
     * L2 缓存配置（Redis）
     */
    @NotNull(message = "L2缓存配置不能为空")
    private L2Cache l2 = new L2Cache();

    /**
     * 缓存预热配置
     */
    @NotNull(message = "缓存预热配置不能为空")
    private Warmup warmup = new Warmup();

    @Data
    public static class L1Cache {
        /**
         * 最大缓存条目数
         */
        private Long maximumSize = 10000L;

        /**
         * 写入后过期时间
         */
        private Duration expireAfterWrite = Duration.ofMinutes(30);

        /**
         * 访问后过期时间
         */
        private Duration expireAfterAccess = Duration.ofMinutes(10);

        /**
         * 初始容量
         */
        private Integer initialCapacity = 100;
    }

    @Data
    public static class L2Cache {
        /**
         * 菜单缓存TTL
         */
        private Duration menuTtl = Duration.ofMinutes(60);

        /**
         * 页面内容缓存TTL
         */
        private Duration pageTtl = Duration.ofMinutes(30);

        /**
         * 页面路径映射缓存TTL
         */
        private Duration pathTtl = Duration.ofMinutes(60);

        /**
         * 统计数据缓存TTL
         */
        private Duration statsTtl = Duration.ofMinutes(15);

        /**
         * 租户缓存键前缀
         */
        private String tenantKeyPrefix = "cms:tenant:";
    }

    @Data
    public static class Warmup {
        /**
         * 是否启用缓存预热
         */
        private Boolean enabled = true;

        /**
         * 预热菜单树
         */
        private Boolean menuTree = true;

        /**
         * 预热热门页面数量
         */
        private Integer hotPagesCount = 100;

        /**
         * 预热延迟时间（应用启动后）
         */
        private Duration delay = Duration.ofSeconds(30);
    }
}