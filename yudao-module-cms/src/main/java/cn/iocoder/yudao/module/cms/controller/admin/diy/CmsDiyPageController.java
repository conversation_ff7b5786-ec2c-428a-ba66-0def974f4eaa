package cn.iocoder.yudao.module.cms.controller.admin.diy;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.controller.admin.diy.vo.page.*;
import cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageDO;
import cn.iocoder.yudao.module.cms.dal.dataobject.menu.CmsMenuDO;
import cn.iocoder.yudao.module.cms.enums.diy.CmsDiyPageStatusEnum;
import cn.iocoder.yudao.module.cms.service.diy.CmsDiyPageService;
import cn.iocoder.yudao.module.cms.service.menu.CmsMenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * CMS DIY页面管理 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - CMS DIY页面管理")
@RestController
@RequestMapping("/cms/diy-page")
@Validated
@Slf4j
public class CmsDiyPageController {

    @Resource
    private CmsDiyPageService diyPageService;

    @Resource
    private CmsMenuService menuService;

    @PostMapping("/create")
    @Operation(summary = "创建DIY页面")
    @PreAuthorize("@ss.hasPermission('cms:diy-page:create')")
    public CommonResult<Long> createDiyPage(@Valid @RequestBody CmsDiyPageCreateReqVO createReqVO) {
        return success(diyPageService.createDiyPage(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新DIY页面")
    @PreAuthorize("@ss.hasPermission('cms:diy-page:update')")
    public CommonResult<Boolean> updateDiyPage(@Valid @RequestBody CmsDiyPageUpdateReqVO updateReqVO) {
        diyPageService.updateDiyPage(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-content")
    @Operation(summary = "更新DIY页面内容")
    @PreAuthorize("@ss.hasPermission('cms:diy-page:update')")
    public CommonResult<Boolean> updateDiyPageContent(@RequestParam("id") Long id,
                                                      @RequestParam("content") String content,
                                                      @RequestParam("version") Integer version) {
        diyPageService.updateDiyPageContent(id, content, version);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除DIY页面")
    @Parameter(name = "id", description = "页面编号", required = true)
    @Parameter(name = "version", description = "当前版本号", required = true)
    @PreAuthorize("@ss.hasPermission('cms:diy-page:delete')")
    public CommonResult<Boolean> deleteDiyPage(@RequestParam("id") Long id,
                                               @RequestParam("version") Integer version) {
        diyPageService.deleteDiyPage(id, version);
        return success(true);
    }

    @PostMapping("/publish")
    @Operation(summary = "发布DIY页面")
    @PreAuthorize("@ss.hasPermission('cms:diy-page:publish')")
    public CommonResult<Boolean> publishDiyPage(@Valid @RequestBody CmsDiyPagePublishReqVO publishReqVO) {
        diyPageService.publishDiyPage(publishReqVO);
        return success(true);
    }

    @PostMapping("/offline")
    @Operation(summary = "下线DIY页面")
    @Parameter(name = "id", description = "页面编号", required = true)
    @Parameter(name = "version", description = "当前版本号", required = true)
    @PreAuthorize("@ss.hasPermission('cms:diy-page:publish')")
    public CommonResult<Boolean> offlineDiyPage(@RequestParam("id") Long id,
                                                @RequestParam("version") Integer version) {
        diyPageService.offlineDiyPage(id, version);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得DIY页面")
    @Parameter(name = "id", description = "页面编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('cms:diy-page:query')")
    public CommonResult<CmsDiyPageRespVO> getDiyPage(@RequestParam("id") Long id) {
        CmsDiyPageDO page = diyPageService.getDiyPage(id);
        return success(convertToRespVO(page));
    }

    @GetMapping("/get-by-uuid")
    @Operation(summary = "根据UUID获得DIY页面")
    @Parameter(name = "uuid", description = "页面UUID", required = true)
    @PreAuthorize("@ss.hasPermission('cms:diy-page:query')")
    public CommonResult<CmsDiyPageRespVO> getDiyPageByUuid(@RequestParam("uuid") String uuid) {
        CmsDiyPageDO page = diyPageService.getDiyPageByUuid(uuid);
        return success(convertToRespVO(page));
    }

    @GetMapping("/get-by-path")
    @Operation(summary = "根据路径获得DIY页面")
    @Parameter(name = "path", description = "页面路径", required = true)
    @PreAuthorize("@ss.hasPermission('cms:diy-page:query')")
    public CommonResult<CmsDiyPageRespVO> getDiyPageByPath(@RequestParam("path") String path) {
        CmsDiyPageDO page = diyPageService.getDiyPageByPath(path);
        return success(convertToRespVO(page));
    }

    @GetMapping("/page")
    @Operation(summary = "获得DIY页面分页")
    @PreAuthorize("@ss.hasPermission('cms:diy-page:query')")
    public CommonResult<PageResult<CmsDiyPageRespVO>> getDiyPagePage(@Valid CmsDiyPagePageReqVO pageVO) {
        PageResult<CmsDiyPageDO> pageResult = diyPageService.getDiyPagePage(pageVO);
        
        List<CmsDiyPageRespVO> list = pageResult.getList().stream()
                .map(this::convertToRespVO)
                .collect(Collectors.toList());
        
        return success(new PageResult<>(list, pageResult.getTotal()));
    }

    @GetMapping("/list-by-menu")
    @Operation(summary = "根据菜单ID获得DIY页面列表")
    @Parameter(name = "menuId", description = "菜单ID", required = true)
    @PreAuthorize("@ss.hasPermission('cms:diy-page:query')")
    public CommonResult<List<CmsDiyPageRespVO>> getDiyPageListByMenuId(@RequestParam("menuId") Long menuId) {
        List<CmsDiyPageDO> list = diyPageService.getDiyPageListByMenuId(menuId);
        return success(list.stream().map(this::convertToRespVO).collect(Collectors.toList()));
    }

    @GetMapping("/list-by-parent")
    @Operation(summary = "根据父页面ID获得子页面列表")
    @Parameter(name = "parentId", description = "父页面ID", required = true)
    @PreAuthorize("@ss.hasPermission('cms:diy-page:query')")
    public CommonResult<List<CmsDiyPageRespVO>> getDiyPageListByParentId(@RequestParam("parentId") Long parentId) {
        List<CmsDiyPageDO> list = diyPageService.getDiyPageListByParentId(parentId);
        return success(list.stream().map(this::convertToRespVO).collect(Collectors.toList()));
    }

    @GetMapping("/handle-version-conflict")
    @Operation(summary = "处理版本冲突")
    @Parameter(name = "id", description = "页面ID", required = true)
    @PreAuthorize("@ss.hasPermission('cms:diy-page:query')")
    public CommonResult<CmsDiyPageRespVO> handleVersionConflict(@RequestParam("id") Long id) {
        CmsDiyPageDO page = diyPageService.handleVersionConflict(id);
        return success(convertToRespVO(page));
    }

    @PostMapping("/refresh-cache")
    @Operation(summary = "刷新页面缓存")
    @Parameter(name = "id", description = "页面ID（为空则预热所有热门页面）", required = false)
    @PreAuthorize("@ss.hasPermission('cms:diy-page:update')")
    public CommonResult<Boolean> refreshPageCache(@RequestParam(value = "id", required = false) Long id) {
        if (id != null) {
            diyPageService.clearPageCache(id);
        } else {
            diyPageService.warmupPageCache();
        }
        return success(true);
    }

    @GetMapping("/validate-path")
    @Operation(summary = "校验页面路径唯一性")
    @Parameter(name = "path", description = "页面路径", required = true)
    @Parameter(name = "id", description = "页面编号（更新时传入）", required = false)
    @PreAuthorize("@ss.hasPermission('cms:diy-page:query')")
    public CommonResult<Boolean> validatePathUnique(@RequestParam("path") String path,
                                                     @RequestParam(value = "id", required = false) Long id) {
        diyPageService.validatePathUnique(id, path);
        return success(true);
    }

    @GetMapping("/build-full-path")
    @Operation(summary = "构建页面完整路径")
    @Parameter(name = "menuId", description = "菜单ID", required = false)
    @Parameter(name = "parentId", description = "父页面ID", required = false)
    @Parameter(name = "pagePath", description = "页面路径", required = true)
    @PreAuthorize("@ss.hasPermission('cms:diy-page:query')")
    public CommonResult<String> buildPageFullPath(@RequestParam(value = "menuId", required = false) Long menuId,
                                                  @RequestParam(value = "parentId", required = false) Long parentId,
                                                  @RequestParam("pagePath") String pagePath) {
        String fullPath = diyPageService.buildPageFullPath(menuId, parentId, pagePath);
        return success(fullPath);
    }

    private CmsDiyPageRespVO convertToRespVO(CmsDiyPageDO page) {
        if (page == null) {
            return null;
        }
        
        CmsDiyPageRespVO vo = BeanUtils.toBean(page, CmsDiyPageRespVO.class);
        
        if (page.getStatus() != null) {
            CmsDiyPageStatusEnum statusEnum = CmsDiyPageStatusEnum.getByStatus(page.getStatus());
            if (statusEnum != null) {
                vo.setStatusName(statusEnum.getName());
            }
        }
        
        if (page.getMenuId() != null) {
            CmsMenuDO menu = menuService.getMenu(page.getMenuId());
            if (menu != null) {
                vo.setMenuName(menu.getName());
            }
        }
        
        if (page.getParentId() != null) {
            CmsDiyPageDO parentPage = diyPageService.getDiyPage(page.getParentId());
            if (parentPage != null) {
                vo.setParentName(parentPage.getName());
            }
        }
        
        return vo;
    }
}