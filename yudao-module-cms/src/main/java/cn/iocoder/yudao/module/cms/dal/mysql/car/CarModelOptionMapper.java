package cn.iocoder.yudao.module.cms.dal.mysql.car;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarModelOptionDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 车型配置选项 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CarModelOptionMapper extends BaseMapperX<CarModelOptionDO> {

    default PageResult<CarModelOptionDO> selectPage(Long modelId, Long optionTypeId, 
                                                     String name, Integer status,
                                                     Integer pageNo, Integer pageSize) {
        return selectPage(new PageParam().setPageNo(pageNo).setPageSize(pageSize),new LambdaQueryWrapperX<CarModelOptionDO>()
                .eqIfPresent(CarModelOptionDO::getModelId, modelId)
                .eqIfPresent(CarModelOptionDO::getOptionTypeId, optionTypeId)
                .likeIfPresent(CarModelOptionDO::getName, name)
                .eqIfPresent(CarModelOptionDO::getStatus, status)
                .orderByAsc(CarModelOptionDO::getSortOrder)
                .orderByDesc(CarModelOptionDO::getId));
    }

    default List<CarModelOptionDO> selectListByModelId(Long modelId) {
        return selectList(new LambdaQueryWrapperX<CarModelOptionDO>()
                .eq(CarModelOptionDO::getModelId, modelId)
                .orderByAsc(CarModelOptionDO::getSortOrder));
    }

    default List<CarModelOptionDO> selectListByModelIdAndTypeId(Long modelId, Long optionTypeId) {
        return selectList(new LambdaQueryWrapperX<CarModelOptionDO>()
                .eq(CarModelOptionDO::getModelId, modelId)
                .eq(CarModelOptionDO::getOptionTypeId, optionTypeId)
                .orderByAsc(CarModelOptionDO::getSortOrder));
    }

    /**
     * 查询车型的所有配置选项（关联选项类型信息）
     */
    @Select("SELECT o.*, t.name as type_name, t.type_code, t.config_schema " +
            "FROM cms_car_model_options o " +
            "INNER JOIN cms_car_option_types t ON o.option_type_id = t.id " +
            "WHERE o.model_id = #{modelId} AND o.deleted = 0 AND o.status = 0 " +
            "ORDER BY t.sort_order, o.sort_order")
    List<CarModelOptionDO> selectOptionsWithType(@Param("modelId") Long modelId);

}