package cn.iocoder.yudao.module.cms.service.car.finance;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.DownPaymentOptionCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.DownPaymentOptionPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.DownPaymentOptionUpdateReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.finance.DownPaymentOptionDO;
import cn.iocoder.yudao.module.cms.dal.mysql.car.finance.DownPaymentOptionMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.cms.enums.ErrorCodeConstants.*;

/**
 * 首付选项 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DownPaymentOptionServiceImpl implements DownPaymentOptionService {

    @Resource
    private DownPaymentOptionMapper downPaymentOptionMapper;

    @Resource
    private FinanceOptionService financeOptionService;

    @Override
    public Long createDownPaymentOption(DownPaymentOptionCreateReqVO createReqVO) {
        // 验证融资选项存在
        validateFinanceOptionExists(createReqVO.getOptionId());
        
        // 校验代码唯一性
        validatePaymentCodeUnique(null, createReqVO.getPaymentCode());

        // 插入
        DownPaymentOptionDO downPaymentOption = BeanUtils.toBean(createReqVO, DownPaymentOptionDO.class);
        downPaymentOptionMapper.insert(downPaymentOption);
        // 返回
        return downPaymentOption.getId();
    }

    @Override
    public void updateDownPaymentOption(DownPaymentOptionUpdateReqVO updateReqVO) {
        // 校验存在
        validateDownPaymentOptionExists(updateReqVO.getId());
        
        // 验证融资选项存在
        validateFinanceOptionExists(updateReqVO.getOptionId());
        
        // 校验代码唯一性
        validatePaymentCodeUnique(updateReqVO.getId(), updateReqVO.getPaymentCode());

        // 更新
        DownPaymentOptionDO updateObj = BeanUtils.toBean(updateReqVO, DownPaymentOptionDO.class);
        downPaymentOptionMapper.updateById(updateObj);
    }

    @Override
    public void deleteDownPaymentOption(Long id) {
        // 校验存在
        validateDownPaymentOptionExists(id);
        // 删除
        downPaymentOptionMapper.deleteById(id);
    }

    @Override
    public DownPaymentOptionDO getDownPaymentOption(Long id) {
        return downPaymentOptionMapper.selectById(id);
    }

    @Override
    public PageResult<DownPaymentOptionDO> getDownPaymentOptionPage(DownPaymentOptionPageReqVO pageReqVO) {
        return downPaymentOptionMapper.selectPage(pageReqVO.getOptionId(), pageReqVO.getName(), pageReqVO.getStatus(),
                pageReqVO.getPageNo(), pageReqVO.getPageSize());
    }

    @Override
    public List<DownPaymentOptionDO> getDownPaymentOptionList(Integer status) {
        return downPaymentOptionMapper.selectListByStatus(status);
    }

    @Override
    public List<DownPaymentOptionDO> getDownPaymentOptionListByOptionId(Long optionId) {
        return downPaymentOptionMapper.selectListByOptionId(optionId);
    }

    @Override
    public DownPaymentOptionDO getDownPaymentOptionByCode(String paymentCode) {
        return downPaymentOptionMapper.selectByCode(paymentCode);
    }

    private void validateDownPaymentOptionExists(Long id) {
        if (downPaymentOptionMapper.selectById(id) == null) {
            throw exception(DOWN_PAYMENT_OPTION_NOT_EXISTS);
        }
    }

    private void validateFinanceOptionExists(Long optionId) {
        if (financeOptionService.getFinanceOption(optionId) == null) {
            throw exception(FINANCE_OPTION_NOT_EXISTS);
        }
    }

    private void validatePaymentCodeUnique(Long id, String paymentCode) {
        DownPaymentOptionDO downPaymentOption = downPaymentOptionMapper.selectByCode(paymentCode);
        if (downPaymentOption == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的首付选项
        if (id == null) {
            throw exception(DOWN_PAYMENT_OPTION_CODE_DUPLICATE);
        }
        if (!downPaymentOption.getId().equals(id)) {
            throw exception(DOWN_PAYMENT_OPTION_CODE_DUPLICATE);
        }
    }

}