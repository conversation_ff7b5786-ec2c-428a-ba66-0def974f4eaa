package cn.iocoder.yudao.module.cms.service.statistics;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.statistics.vo.CmsPageVisitLogPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.statistics.vo.CmsPageVisitStatsPageReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.statistics.CmsPageVisitLogDO;
import cn.iocoder.yudao.module.cms.dal.dataobject.statistics.CmsPageVisitStatsDO;
import cn.iocoder.yudao.module.cms.dal.mysql.statistics.CmsPageVisitLogMapper;
import cn.iocoder.yudao.module.cms.dal.mysql.statistics.CmsPageVisitStatsMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * CMS页面访问统计 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class CmsPageVisitServiceImpl implements CmsPageVisitService {

    @Resource
    private CmsPageVisitLogMapper visitLogMapper;

    @Resource
    private CmsPageVisitStatsMapper visitStatsMapper;

    private static final Integer DEFAULT_KEEP_DAYS = 30;
    private static final Integer DEFAULT_TREND_DAYS = 7;

    @Override
    @Async
    public void recordPageVisit(Long pageId, String pageUuid, Long userId, String sessionId,
                               String ip, String userAgent, String referer) {
        try {
            CmsPageVisitLogDO visitLog = new CmsPageVisitLogDO();
            visitLog.setPageId(pageId);
            visitLog.setPageUuid(pageUuid);
            visitLog.setUserId(userId);
            visitLog.setSessionId(sessionId);
            visitLog.setIp(ip);
            visitLog.setUserAgent(userAgent);
            visitLog.setReferer(referer);
            visitLog.setVisitTime(LocalDateTime.now());
            
            visitLogMapper.insert(visitLog);
            
            log.debug("[recordPageVisit][记录页面访问成功，页面ID：{}，IP：{}]", pageId, ip);
        } catch (Exception e) {
            log.error("[recordPageVisit][记录页面访问失败，页面ID：{}]", pageId, e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertVisitLogs(List<CmsPageVisitLogDO> visitLogs) {
        if (CollUtil.isEmpty(visitLogs)) {
            return;
        }
        
        try {
            visitLogMapper.insertBatch(visitLogs);
            log.info("[batchInsertVisitLogs][批量插入访问日志成功，数量：{}]", visitLogs.size());
        } catch (Exception e) {
            log.error("[batchInsertVisitLogs][批量插入访问日志失败，数量：{}]", visitLogs.size(), e);
            throw e;
        }
    }

    @Override
    public PageResult<CmsPageVisitLogDO> getVisitLogPage(CmsPageVisitLogPageReqVO pageReqVO) {
        return visitLogMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<CmsPageVisitStatsDO> getVisitStatsPage(CmsPageVisitStatsPageReqVO pageReqVO) {
        return visitStatsMapper.selectPage(pageReqVO);
    }

    @Override
    public List<CmsPageVisitStatsDO> getPageVisitStats(Long pageId, LocalDate startDate, LocalDate endDate) {
        if (pageId == null) {
            return Collections.emptyList();
        }
        return visitStatsMapper.selectListByPageIdAndDateRange(pageId, startDate, endDate);
    }

    @Override
    public List<CmsPageVisitStatsDO> getHotPages(Integer days, Integer limit) {
        if (days == null || days <= 0) {
            days = DEFAULT_TREND_DAYS;
        }
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(days - 1);
        
        return visitStatsMapper.selectHotPages(startDate, endDate, limit);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer aggregateDailyStats(LocalDate statDate) {
        if (statDate == null) {
            statDate = LocalDate.now().minusDays(1);
        }
        
        try {
            // 删除已存在的统计数据
            visitStatsMapper.deleteByStatDate(statDate);
            
            // 聚合当天的访问数据
            List<CmsPageVisitStatsDO> dailyStats = visitLogMapper.selectDailyStats(statDate);
            if (CollUtil.isEmpty(dailyStats)) {
                log.info("[aggregateDailyStats][没有找到需要聚合的数据，日期：{}]", statDate);
                return 0;
            }

            // 设置统计日期
            LocalDate finalStatDate = statDate;
            dailyStats.forEach(stats -> stats.setStatDate(finalStatDate));
            
            // 批量插入统计数据
            visitStatsMapper.insertBatch(dailyStats);
            
            log.info("[aggregateDailyStats][聚合日访问统计成功，日期：{}，聚合记录数：{}]", statDate, dailyStats.size());
            return dailyStats.size();
            
        } catch (Exception e) {
            log.error("[aggregateDailyStats][聚合日访问统计失败，日期：{}]", statDate, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchAggregateStats(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null || startDate.isAfter(endDate)) {
            throw new IllegalArgumentException("日期参数错误");
        }
        
        int totalAggregated = 0;
        LocalDate currentDate = startDate;
        
        while (!currentDate.isAfter(endDate)) {
            try {
                Integer count = aggregateDailyStats(currentDate);
                totalAggregated += count;
                currentDate = currentDate.plusDays(1);
            } catch (Exception e) {
                log.error("[batchAggregateStats][聚合统计失败，日期：{}]", currentDate, e);
                currentDate = currentDate.plusDays(1);
            }
        }
        
        log.info("[batchAggregateStats][批量聚合统计完成，时间范围：{} 到 {}，总聚合记录数：{}]", 
                startDate, endDate, totalAggregated);
        return totalAggregated;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer cleanupOldVisitLogs(Integer keepDays) {
        if (keepDays == null || keepDays <= 0) {
            keepDays = DEFAULT_KEEP_DAYS;
        }
        
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(keepDays);
        
        try {
            Integer deleted = visitLogMapper.deleteByVisitTimeBefore(cutoffTime);
            log.info("[cleanupOldVisitLogs][清理旧访问日志完成，保留天数：{}，删除记录数：{}]", keepDays, deleted);
            return deleted;
        } catch (Exception e) {
            log.error("[cleanupOldVisitLogs][清理旧访问日志失败，保留天数：{}]", keepDays, e);
            throw e;
        }
    }

    @Override
    public Integer getTodayVisitCount(Long pageId) {
        if (pageId == null) {
            return 0;
        }
        
        LocalDate today = LocalDate.now();
        CmsPageVisitStatsDO stats = visitStatsMapper.selectByPageIdAndDate(pageId, today);
        
        if (stats != null) {
            return stats.getTotalVisits();
        }
        
        // 如果统计表中没有数据，从日志表实时查询
        return visitLogMapper.selectTodayVisitCount(pageId);
    }

    @Override
    public Long getTotalVisitCount(Long pageId) {
        if (pageId == null) {
            return 0L;
        }
        return visitStatsMapper.selectTotalVisitCount(pageId);
    }

    @Override
    public List<Object[]> getPageVisitTrend(Long pageId, Integer days) {
        if (pageId == null) {
            return Collections.emptyList();
        }
        
        if (days == null || days <= 0) {
            days = DEFAULT_TREND_DAYS;
        }
        
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(days - 1);
        
        List<CmsPageVisitStatsDO> statsList = visitStatsMapper.selectListByPageIdAndDateRange(pageId, startDate, endDate);
        
        // 构建趋势数据，确保所有日期都有数据（没有访问的日期补0）
        List<Object[]> trendData = new ArrayList<>();
        LocalDate currentDate = startDate;
        
        Map<LocalDate, Integer> statsMap = statsList.stream()
                .collect(Collectors.toMap(
                        CmsPageVisitStatsDO::getStatDate,
                        CmsPageVisitStatsDO::getTotalVisits,
                        (existing, replacement) -> existing
                ));
        
        while (!currentDate.isAfter(endDate)) {
            String dateStr = DateUtil.format(DateUtil.date(currentDate.atStartOfDay()), "yyyy-MM-dd");
            Integer visitCount = statsMap.getOrDefault(currentDate, 0);
            trendData.add(new Object[]{dateStr, visitCount});
            currentDate = currentDate.plusDays(1);
        }
        
        return trendData;
    }

    @Override
    public Object getRealTimeStats() {
        Map<String, Object> realTimeStats = new HashMap<>();
        
        try {
            // 获取今日总访问量
            Integer todayTotalVisits = visitLogMapper.selectTodayTotalVisits();
            realTimeStats.put("todayTotalVisits", todayTotalVisits != null ? todayTotalVisits : 0);
            
            // 获取今日独立访客数
            Integer todayUniqueVisitors = visitLogMapper.selectTodayUniqueVisitors();
            realTimeStats.put("todayUniqueVisitors", todayUniqueVisitors != null ? todayUniqueVisitors : 0);
            
            // 获取当前小时访问量
            Integer currentHourVisits = visitLogMapper.selectCurrentHourVisits();
            realTimeStats.put("currentHourVisits", currentHourVisits != null ? currentHourVisits : 0);
            
            // 获取实时在线用户数（最近5分钟有访问记录的）
            Integer onlineUsers = visitLogMapper.selectOnlineUsers();
            realTimeStats.put("onlineUsers", onlineUsers != null ? onlineUsers : 0);
            
        } catch (Exception e) {
            log.error("[getRealTimeStats][获取实时统计数据失败]", e);
        }
        
        return realTimeStats;
    }
}