package cn.iocoder.yudao.module.cms.controller.admin.car;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.CarModelFinancePlanCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.CarModelFinancePlanFormOptionsVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.CarModelFinancePlanPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.CarModelFinancePlanRespVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.CarModelFinancePlanUpdateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.DownPaymentOptionRespVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.FinanceOptionRespVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.FinanceTermRespVO;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.finance.CarModelFinancePlanDO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.finance.DownPaymentOptionDO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.finance.FinanceOptionDO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.finance.FinanceTermDO;
import cn.iocoder.yudao.module.cms.service.car.finance.CarModelFinancePlanService;
import cn.iocoder.yudao.module.cms.service.car.finance.CarModelFinancePlanService.FinancePlanDetails;
import cn.iocoder.yudao.module.cms.service.car.finance.DownPaymentOptionService;
import cn.iocoder.yudao.module.cms.service.car.finance.FinanceOptionService;
import cn.iocoder.yudao.module.cms.service.car.finance.FinanceTermService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 车型融资方案")
@RestController
@RequestMapping("/cms/finance-plan")
@Validated
public class CarModelFinancePlanController {

    @Resource
    private CarModelFinancePlanService carModelFinancePlanService;

    @Resource
    private FinanceOptionService financeOptionService;

    @Resource
    private FinanceTermService financeTermService;

    @Resource
    private DownPaymentOptionService downPaymentOptionService;

    @PostMapping("/create")
    @Operation(summary = "创建车型融资方案")
    @PreAuthorize("@ss.hasPermission('cms:finance-plan:create')")
    public CommonResult<Long> createCarModelFinancePlan(@Valid @RequestBody CarModelFinancePlanCreateReqVO createReqVO) {
        return success(carModelFinancePlanService.createCarModelFinancePlan(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新车型融资方案")
    @PreAuthorize("@ss.hasPermission('cms:finance-plan:update')")
    public CommonResult<Boolean> updateCarModelFinancePlan(@Valid @RequestBody CarModelFinancePlanUpdateReqVO updateReqVO) {
        carModelFinancePlanService.updateCarModelFinancePlan(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除车型融资方案")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('cms:finance-plan:delete')")
    public CommonResult<Boolean> deleteCarModelFinancePlan(@RequestParam("id") Long id) {
        carModelFinancePlanService.deleteCarModelFinancePlan(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得车型融资方案")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('cms:finance-plan:query')")
    public CommonResult<CarModelFinancePlanRespVO> getCarModelFinancePlan(@RequestParam("id") Long id) {
        CarModelFinancePlanDO financePlan = carModelFinancePlanService.getCarModelFinancePlan(id);
        CarModelFinancePlanRespVO respVO = BeanUtils.toBean(financePlan, CarModelFinancePlanRespVO.class);
        
        // 获取可选的融资选项列表
        List<FinanceOptionDO> financeOptionsList = financeOptionService.getFinanceOptionList(1);
        List<FinanceOptionRespVO> financeOptionsRespList = BeanUtils.toBean(financeOptionsList, FinanceOptionRespVO.class);
        
        // 设置融资选项列表 - financeOptions字段已添加到CarModelFinancePlanRespVO中
        respVO.setFinanceOptions(financeOptionsRespList);
        
        return success(respVO);
    }

    @GetMapping("/page")
    @Operation(summary = "获得车型融资方案分页")
    @PreAuthorize("@ss.hasPermission('cms:finance-plan:query')")
    public CommonResult<PageResult<CarModelFinancePlanRespVO>> getCarModelFinancePlanPage(@Valid CarModelFinancePlanPageReqVO pageReqVO) {
        PageResult<CarModelFinancePlanDO> pageResult = carModelFinancePlanService.getCarModelFinancePlanPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CarModelFinancePlanRespVO.class));
    }

    @GetMapping("/list-by-model")
    @Operation(summary = "根据车型ID获得融资方案列表")
    @Parameter(name = "modelId", description = "车型ID", required = true)
    @PreAuthorize("@ss.hasPermission('cms:finance-plan:query')")
    public CommonResult<List<CarModelFinancePlanRespVO>> getCarModelFinancePlanListByModelId(@RequestParam("modelId") Long modelId) {
        List<CarModelFinancePlanDO> list = carModelFinancePlanService.getCarModelFinancePlanListByModelId(modelId);
        return success(BeanUtils.toBean(list, CarModelFinancePlanRespVO.class));
    }

    @GetMapping("/get-default")
    @Operation(summary = "获得车型的默认融资方案")
    @Parameter(name = "modelId", description = "车型ID", required = true)
    @PreAuthorize("@ss.hasPermission('cms:finance-plan:query')")
    public CommonResult<CarModelFinancePlanRespVO> getDefaultFinancePlan(@RequestParam("modelId") Long modelId) {
        CarModelFinancePlanDO financePlan = carModelFinancePlanService.getDefaultFinancePlan(modelId);
        return success(BeanUtils.toBean(financePlan, CarModelFinancePlanRespVO.class));
    }

    @PutMapping("/set-default")
    @Operation(summary = "设置为默认融资方案")
    @Parameter(name = "id", description = "方案ID", required = true)
    @PreAuthorize("@ss.hasPermission('cms:finance-plan:update')")
    public CommonResult<Boolean> setAsDefaultPlan(@RequestParam("id") Long id) {
        carModelFinancePlanService.setAsDefaultPlan(id);
        return success(true);
    }

    @PostMapping("/batch-create")
    @Operation(summary = "批量创建融资方案")
    @PreAuthorize("@ss.hasPermission('cms:finance-plan:create')")
    public CommonResult<List<Long>> batchCreateCarModelFinancePlans(@Valid @RequestBody List<CarModelFinancePlanCreateReqVO> createReqVOList) {
        return success(carModelFinancePlanService.batchCreateCarModelFinancePlans(createReqVOList));
    }

    @DeleteMapping("/delete-by-model")
    @Operation(summary = "根据车型ID删除所有融资方案")
    @Parameter(name = "modelId", description = "车型ID", required = true)
    @PreAuthorize("@ss.hasPermission('cms:finance-plan:delete')")
    public CommonResult<Boolean> deleteCarModelFinancePlansByModelId(@RequestParam("modelId") Long modelId) {
        carModelFinancePlanService.deleteCarModelFinancePlansByModelId(modelId);
        return success(true);
    }

    @GetMapping("/form-options")
    @Operation(summary = "获取表单选项数据")
    @PreAuthorize("@ss.hasPermission('cms:finance-plan:query')")
    public CommonResult<CarModelFinancePlanFormOptionsVO> getFormOptions() {
        // 获取可选的融资选项列表
        List<FinanceOptionDO> financeOptionsList = financeOptionService.getFinanceOptionList(1);
        List<FinanceOptionRespVO> financeOptionsRespList = BeanUtils.toBean(financeOptionsList, FinanceOptionRespVO.class);
        
        // 获取可选的融资期限列表
        List<FinanceTermDO> financeTermsList = financeTermService.getFinanceTermList(1);
        List<FinanceTermRespVO> financeTermsRespList = BeanUtils.toBean(financeTermsList, FinanceTermRespVO.class);
        
        // 获取可选的首付选项列表
        List<DownPaymentOptionDO> downPaymentOptionsList = downPaymentOptionService.getDownPaymentOptionList(1);
        List<DownPaymentOptionRespVO> downPaymentOptionsRespList = BeanUtils.toBean(downPaymentOptionsList, DownPaymentOptionRespVO.class);
        
        CarModelFinancePlanFormOptionsVO formOptions = new CarModelFinancePlanFormOptionsVO();
        formOptions.setFinanceOptions(financeOptionsRespList);
        formOptions.setFinanceTerms(financeTermsRespList);
        formOptions.setDownPaymentOptions(downPaymentOptionsRespList);
        
        return success(formOptions);
    }

    @GetMapping("/calculate")
    @Operation(summary = "计算融资方案详情")
    @Parameter(name = "carPrice", description = "车辆价格", required = true)
    @Parameter(name = "downPaymentRatio", description = "首付比例", required = true)
    @Parameter(name = "interestRate", description = "利率", required = true)
    @Parameter(name = "termMonths", description = "期限月数", required = true)
    @Parameter(name = "gfv", description = "保证未来价值")
    @PreAuthorize("@ss.hasPermission('cms:finance-plan:query')")
    public CommonResult<FinancePlanDetails> calculateFinancePlan(
            @RequestParam("carPrice") BigDecimal carPrice,
            @RequestParam("downPaymentRatio") BigDecimal downPaymentRatio,
            @RequestParam("interestRate") BigDecimal interestRate,
            @RequestParam("termMonths") Integer termMonths,
            @RequestParam(value = "gfv", required = false) BigDecimal gfv) {
        FinancePlanDetails details = carModelFinancePlanService.calculateFinancePlan(
                carPrice, downPaymentRatio, interestRate, termMonths, gfv);
        return success(details);
    }
}