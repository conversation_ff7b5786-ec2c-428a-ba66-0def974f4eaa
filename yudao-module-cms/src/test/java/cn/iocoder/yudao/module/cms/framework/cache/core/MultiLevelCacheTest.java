package cn.iocoder.yudao.module.cms.framework.cache.core;

import cn.iocoder.yudao.module.cms.framework.cache.config.CmsCacheProperties;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.cache.Cache;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.cache.support.SimpleValueWrapper;

import java.time.Duration;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 多级缓存测试
 *
 * <AUTHOR>
 */
class MultiLevelCacheTest {

    private MultiLevelCache multiLevelCache;
    private Cache l1Cache;
    private Cache l2Cache;

    @BeforeEach
    void setUp() {
        // 创建 L1 缓存（Caffeine）
        com.github.benmanes.caffeine.cache.Cache<Object, Object> caffeineCache = Caffeine.newBuilder()
                .maximumSize(100)
                .expireAfterWrite(Duration.ofMinutes(10))
                .build();
        l1Cache = new CaffeineCache("test-l1", caffeineCache);

        // 模拟 L2 缓存（Redis）
        l2Cache = mock(Cache.class);
        when(l2Cache.getName()).thenReturn("test-l2");

        multiLevelCache = new MultiLevelCache("test-cache", l1Cache, l2Cache);
    }

    @Test
    void testL1CacheHit() {
        // Given
        String key = "test-key";
        String value = "test-value";
        l1Cache.put(key, value);

        // When
        Cache.ValueWrapper result = multiLevelCache.get(key);

        // Then
        assertNotNull(result);
        assertEquals(value, result.get());
        
        // L2 缓存不应该被调用
        verify(l2Cache, never()).get(key);
    }

    @Test
    void testL2CacheHit() {
        // Given
        String key = "test-key";
        String value = "test-value";
        when(l2Cache.get(key)).thenReturn(new SimpleValueWrapper(value));

        // When
        Cache.ValueWrapper result = multiLevelCache.get(key);

        // Then
        assertNotNull(result);
        assertEquals(value, result.get());
        
        // L2 缓存应该被调用
        verify(l2Cache).get(key);
        
        // 验证 L1 缓存被异步回填（需要等待一下）
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        Cache.ValueWrapper l1Result = l1Cache.get(key);
        assertNotNull(l1Result);
        assertEquals(value, l1Result.get());
    }

    @Test
    void testCacheMiss() {
        // Given
        String key = "non-existent-key";
        when(l2Cache.get(key)).thenReturn(null);

        // When
        Cache.ValueWrapper result = multiLevelCache.get(key);

        // Then
        assertNull(result);
        verify(l2Cache).get(key);
    }

    @Test
    void testPut() {
        // Given
        String key = "test-key";
        String value = "test-value";

        // When
        multiLevelCache.put(key, value);

        // Then
        // 验证 L1 缓存
        Cache.ValueWrapper l1Result = l1Cache.get(key);
        assertNotNull(l1Result);
        assertEquals(value, l1Result.get());
        
        // 验证 L2 缓存
        verify(l2Cache).put(key, value);
    }

    @Test
    void testEvict() {
        // Given
        String key = "test-key";
        String value = "test-value";
        l1Cache.put(key, value);

        // When
        multiLevelCache.evict(key);

        // Then
        // 验证 L1 缓存被清除
        assertNull(l1Cache.get(key));
        
        // 验证 L2 缓存被清除
        verify(l2Cache).evict(key);
    }

    @Test
    void testClear() {
        // Given
        l1Cache.put("key1", "value1");
        l1Cache.put("key2", "value2");

        // When
        multiLevelCache.clear();

        // Then
        // 验证 L1 缓存被清除
        assertNull(l1Cache.get("key1"));
        assertNull(l1Cache.get("key2"));
        
        // 验证 L2 缓存被清除
        verify(l2Cache).clear();
    }

    @Test
    void testGetWithType() {
        // Given
        String key = "test-key";
        String value = "test-value";
        l1Cache.put(key, value);

        // When
        String result = multiLevelCache.get(key, String.class);

        // Then
        assertEquals(value, result);
    }

    @Test
    void testGetWithCallable() {
        // Given
        String key = "test-key";
        String value = "test-value";
        when(l2Cache.get(key)).thenReturn(null);

        // When
        String result = multiLevelCache.get(key, () -> value);

        // Then
        assertEquals(value, result);
        
        // 验证值被缓存
        Cache.ValueWrapper cachedResult = l1Cache.get(key);
        assertNotNull(cachedResult);
        assertEquals(value, cachedResult.get());
    }

    @Test
    void testPutIfAbsent() {
        // Given
        String key = "test-key";
        String value1 = "value1";
        String value2 = "value2";
        
        // 先放入一个值
        multiLevelCache.put(key, value1);

        // When - 尝试放入另一个值
        Cache.ValueWrapper result = multiLevelCache.putIfAbsent(key, value2);

        // Then - 应该返回已存在的值
        assertNotNull(result);
        assertEquals(value1, result.get());
        
        // 验证缓存中的值没有改变
        assertEquals(value1, multiLevelCache.get(key, String.class));
    }
}