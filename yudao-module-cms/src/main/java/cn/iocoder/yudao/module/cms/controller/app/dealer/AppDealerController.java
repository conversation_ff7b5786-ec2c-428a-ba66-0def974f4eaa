package cn.iocoder.yudao.module.cms.controller.app.dealer;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.app.dealer.vo.AppDealerDetailRespVO;
import cn.iocoder.yudao.module.cms.controller.app.dealer.vo.AppDealerRespVO;
import cn.iocoder.yudao.module.cms.controller.app.dealer.vo.AppDealerSearchReqVO;
import cn.iocoder.yudao.module.cms.service.dealer.AppDealerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 APP - 经销商")
@RestController
@RequestMapping("/app/dealer")
@Validated
public class AppDealerController {

    @Resource
    private AppDealerService appDealerService;

    @PostMapping("/list")
    @Operation(summary = "APP端综合查询经销商列表")
    @PermitAll
    public CommonResult<PageResult<AppDealerRespVO>> searchDealers(@Valid @RequestBody AppDealerSearchReqVO reqVO) {
        PageResult<AppDealerRespVO> result = appDealerService.searchDealers(reqVO);
        return success(result);
    }

    @GetMapping("/{id}")
    @Operation(summary = "APP端获取经销商详情")
    @Parameter(name = "id", description = "经销商ID", required = true, example = "1")
    @PermitAll
    public CommonResult<AppDealerDetailRespVO> getDealerDetail(@PathVariable("id") Long id) {
        AppDealerDetailRespVO result = appDealerService.getDealerDetail(id);
        return success(result);
    }

}