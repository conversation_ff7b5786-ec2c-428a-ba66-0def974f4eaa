package cn.iocoder.yudao.module.cms.controller.admin.dealer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.util.List;

/**
 * CMS经销商 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class DealerBaseVO {

    @Schema(description = "经销商名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "Allen Motor Romford")
    @NotNull(message = "经销商名称不能为空")
    @Length(max = 200, message = "经销商名称长度不能超过200个字符")
    private String name;

    @Schema(description = "详细地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "17 London Rd, Romford RM7 9QB Greater London")
    @NotNull(message = "详细地址不能为空")
    @Length(max = 500, message = "详细地址长度不能超过500个字符")
    private String address;

    @Schema(description = "邮政编码", example = "RM7 9QB")
    @Length(max = 20, message = "邮政编码长度不能超过20个字符")
    private String postcode;

    @Schema(description = "联系电话", example = "01708 123456")
    @Length(max = 50, message = "联系电话长度不能超过50个字符")
    private String phone;

    @Schema(description = "电子邮箱", example = "<EMAIL>")
    @Length(max = 100, message = "电子邮箱长度不能超过100个字符")
    private String email;

    @Schema(description = "网站地址", example = "https://www.allenmotorgroup.co.uk")
    @Length(max = 200, message = "网站地址长度不能超过200个字符")
    private String website;

    @Schema(description = "纬度", requiredMode = Schema.RequiredMode.REQUIRED, example = "51.5758311")
    @NotNull(message = "纬度不能为空")
    @DecimalMin(value = "-90.0000000", message = "纬度必须在-90到90之间")
    @DecimalMax(value = "90.0000000", message = "纬度必须在-90到90之间")
    private BigDecimal latitude;

    @Schema(description = "经度", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.1770876")
    @NotNull(message = "经度不能为空")
    @DecimalMin(value = "-180.0000000", message = "经度必须在-180到180之间")
    @DecimalMax(value = "180.0000000", message = "经度必须在-180到180之间")
    private BigDecimal longitude;

    @Schema(description = "地区", requiredMode = Schema.RequiredMode.REQUIRED, example = "London")
    @NotNull(message = "地区不能为空")
    @Length(max = 100, message = "地区长度不能超过100个字符")
    private String region;

    @Schema(description = "服务类型", example = "[\"Sales\",\"Service\",\"Parts\",\"Test Drive\",\"Finance\"]")
    private List<String> services;

    @Schema(description = "状态：0-禁用，1-启用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

}