-- 经销商管理菜单SQL
-- 所属目录：CheryCar (ID: 5013)

-- 经销商管理（目录菜单）
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES ('Dealer Management', '', 1, 2, 5013, 'dealer', 'ep:office-building', '', '', 0, b'1', b'1', b'1', '1', NOW(), '1', NOW(), b'0');

-- 获取上一条记录的ID，用作经销商管理目录的ID
SET @dealer_menu_id = LAST_INSERT_ID();

-- 经销商列表（菜单页面）
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES ('Dealer List', 'cms:dealer:query', 2, 1, @dealer_menu_id, 'dealer', 'ep:list', 'chery/dealer/index', 'CheryDealer', 0, b'1', b'1', b'1', '1', NOW(), '1', NOW(), b'0');

-- 获取经销商列表菜单的ID，用作按钮权限的父ID
SET @dealer_list_id = LAST_INSERT_ID();

-- 经销商新增（按钮权限）
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES ('Dealer Create', 'cms:dealer:create', 3, 1, @dealer_list_id, '', '', '', '', 0, b'1', b'1', b'1', '1', NOW(), '1', NOW(), b'0');

-- 经销商编辑（按钮权限）
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES ('Dealer Update', 'cms:dealer:update', 3, 2, @dealer_list_id, '', '', '', '', 0, b'1', b'1', b'1', '1', NOW(), '1', NOW(), b'0');

-- 经销商删除（按钮权限）
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES ('Dealer Delete', 'cms:dealer:delete', 3, 3, @dealer_list_id, '', '', '', '', 0, b'1', b'1', b'1', '1', NOW(), '1', NOW(), b'0');

-- 经销商导出（按钮权限）
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES ('Dealer Export', 'cms:dealer:export', 3, 4, @dealer_list_id, '', '', '', '', 0, b'1', b'1', b'1', '1', NOW(), '1', NOW(), b'0');

-- 查询插入的菜单记录，用于验证
SELECT id, name, permission, type, sort, parent_id, path, icon, component, component_name 
FROM system_menu 
WHERE name LIKE '%Dealer%' AND deleted = b'0' 
ORDER BY id;