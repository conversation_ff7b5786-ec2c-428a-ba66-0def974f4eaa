package cn.iocoder.yudao.module.cms.framework.cache.core;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.cms.framework.cache.config.CmsCacheProperties;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cache.Cache;

import java.util.Arrays;
import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * CMS 缓存服务测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class CmsCacheServiceTest {

    @Mock
    private MultiLevelCacheManager cacheManager;

    @Mock
    private CacheSyncService cacheSyncService;

    @Mock
    private Cache cache;

    private CmsCacheProperties cacheProperties;
    private CmsCacheService cacheService;

    @BeforeEach
    void setUp() {
        cacheProperties = new CmsCacheProperties();
        cacheProperties.setEnabled(true);
        
        cacheService = new CmsCacheService(cacheManager, cacheSyncService, cacheProperties);
        
        // 设置租户上下文
        TenantContextHolder.setTenantId(1L);
        
        when(cacheManager.getCache(any())).thenReturn(cache);
    }

    @AfterEach
    void tearDown() {
        TenantContextHolder.clear();
    }

    @Test
    void testGetWithCacheHit() {
        // Given
        String cacheName = "test-cache";
        String key = "test-key";
        String expectedValue = "cached-value";
        String tenantKey = "cms:tenant:1:" + key;
        
        when(cache.get(tenantKey, String.class)).thenReturn(expectedValue);

        // When
        String result = cacheService.get(cacheName, key, String.class, () -> "fallback-value");

        // Then
        assertEquals(expectedValue, result);
        verify(cache).get(tenantKey, String.class);
        verify(cacheSyncService, never()).publishCacheUpdate(any(), any(), any());
    }

    @Test
    void testGetWithCacheMiss() {
        // Given
        String cacheName = "test-cache";
        String key = "test-key";
        String fallbackValue = "fallback-value";
        String tenantKey = "cms:tenant:1:" + key;
        
        when(cache.get(tenantKey, String.class)).thenReturn(null);

        // When
        String result = cacheService.get(cacheName, key, String.class, () -> fallbackValue);

        // Then
        assertEquals(fallbackValue, result);
        verify(cache).get(tenantKey, String.class);
        verify(cache).put(tenantKey, fallbackValue);
        verify(cacheSyncService).publishCacheUpdate(cacheName, tenantKey, fallbackValue);
    }

    @Test
    void testGetWithCacheDisabled() {
        // Given
        cacheProperties.setEnabled(false);
        String fallbackValue = "fallback-value";
        Supplier<String> supplier = () -> fallbackValue;

        // When
        String result = cacheService.get("test-cache", "test-key", String.class, supplier);

        // Then
        assertEquals(fallbackValue, result);
        verify(cacheManager, never()).getCache(any());
    }

    @Test
    void testPut() {
        // Given
        String cacheName = "test-cache";
        String key = "test-key";
        String value = "test-value";
        String tenantKey = "cms:tenant:1:" + key;

        // When
        cacheService.put(cacheName, key, value);

        // Then
        verify(cache).put(tenantKey, value);
        verify(cacheSyncService).publishCacheUpdate(cacheName, tenantKey, value);
    }

    @Test
    void testPutWithNullValue() {
        // Given
        String cacheName = "test-cache";
        String key = "test-key";

        // When
        cacheService.put(cacheName, key, null);

        // Then
        verify(cache, never()).put(any(), any());
        verify(cacheSyncService, never()).publishCacheUpdate(any(), any(), any());
    }

    @Test
    void testEvict() {
        // Given
        String cacheName = "test-cache";
        String key = "test-key";
        String tenantKey = "cms:tenant:1:" + key;

        // When
        cacheService.evict(cacheName, key);

        // Then
        verify(cache).evict(tenantKey);
        verify(cacheSyncService).publishCacheEvict(cacheName, tenantKey);
    }

    @Test
    void testClear() {
        // Given
        String cacheName = "test-cache";

        // When
        cacheService.clear(cacheName);

        // Then
        verify(cache).clear();
        verify(cacheSyncService).publishCacheClear(cacheName);
    }

    @Test
    void testEvictBatch() {
        // Given
        String cacheName = "test-cache";
        String key1 = "key1";
        String key2 = "key2";
        String tenantKey1 = "cms:tenant:1:" + key1;
        String tenantKey2 = "cms:tenant:1:" + key2;

        // When
        cacheService.evictBatch(cacheName, Arrays.asList(key1, key2));

        // Then
        verify(cache).evict(tenantKey1);
        verify(cache).evict(tenantKey2);
        verify(cacheSyncService).publishCacheEvict(cacheName, tenantKey1);
        verify(cacheSyncService).publishCacheEvict(cacheName, tenantKey2);
    }

    @Test
    void testExists() {
        // Given
        String cacheName = "test-cache";
        String key = "test-key";
        String tenantKey = "cms:tenant:1:" + key;
        
        when(cache.get(tenantKey)).thenReturn(mock(Cache.ValueWrapper.class));

        // When
        boolean exists = cacheService.exists(cacheName, key);

        // Then
        assertTrue(exists);
        verify(cache).get(tenantKey);
    }

    @Test
    void testExistsWithNullValue() {
        // Given
        String cacheName = "test-cache";
        String key = "test-key";
        String tenantKey = "cms:tenant:1:" + key;
        
        when(cache.get(tenantKey)).thenReturn(null);

        // When
        boolean exists = cacheService.exists(cacheName, key);

        // Then
        assertFalse(exists);
        verify(cache).get(tenantKey);
    }

    @Test
    void testGetMenuTree() {
        // Given
        String key = "enabled";
        String expectedValue = "menu-tree";
        String tenantKey = "cms:tenant:1:tree:" + key;
        
        when(cache.get(tenantKey, String.class)).thenReturn(expectedValue);

        // When
        String result = cacheService.getMenuTree(key, String.class, () -> "fallback");

        // Then
        assertEquals(expectedValue, result);
        verify(cacheManager).getCache(CmsCacheService.CACHE_MENU);
    }

    @Test
    void testPutMenuTree() {
        // Given
        String key = "enabled";
        String value = "menu-tree";
        String tenantKey = "cms:tenant:1:tree:" + key;

        // When
        cacheService.putMenuTree(key, value);

        // Then
        verify(cache).put(tenantKey, value);
        verify(cacheSyncService).publishCacheUpdate(CmsCacheService.CACHE_MENU, tenantKey, value);
    }

    @Test
    void testGetPageContent() {
        // Given
        Long pageId = 123L;
        String expectedValue = "page-content";
        String tenantKey = "cms:tenant:1:content:" + pageId;
        
        when(cache.get(tenantKey, String.class)).thenReturn(expectedValue);

        // When
        String result = cacheService.getPageContent(pageId, String.class, () -> "fallback");

        // Then
        assertEquals(expectedValue, result);
        verify(cacheManager).getCache(CmsCacheService.CACHE_PAGE);
    }

    @Test
    void testEvictPageCache() {
        // Given
        Long pageId = 123L;
        String tenantKey = "cms:tenant:1:content:" + pageId;
        
        Cache pathCache = mock(Cache.class);
        when(cacheManager.getCache(CmsCacheService.CACHE_PAGE_PATH)).thenReturn(pathCache);

        // When
        cacheService.evictPageCache(pageId);

        // Then
        verify(cache).evict(tenantKey);
        verify(cacheSyncService).publishCacheEvict(CmsCacheService.CACHE_PAGE, tenantKey);
        verify(pathCache).clear();
        verify(cacheSyncService).publishCacheClear(CmsCacheService.CACHE_PAGE_PATH);
    }

    @Test
    void testTenantKeyBuilding() {
        // Given - 无租户上下文
        TenantContextHolder.clear();
        String key = "test-key";

        // When
        cacheService.put("test-cache", key, "value");

        // Then - 应该使用原始键
        verify(cache).put(key, "value");
    }

    @Test
    void testGetCacheStats() {
        // Given
        String cacheName = "test-cache";
        CacheStats expectedStats = new CacheStats();
        when(cacheManager.getCacheStats(cacheName)).thenReturn(expectedStats);

        // When
        CacheStats result = cacheService.getCacheStats(cacheName);

        // Then
        assertEquals(expectedStats, result);
        verify(cacheManager).getCacheStats(cacheName);
    }
}