# 车型配置管理系统 - 可扩展设计方案 v2.0

## 1. 设计理念

### 1.1 核心问题
当前系统中配置包、颜色、内饰、轮毂等都是独立的表结构，如果未来需要添加新的配置类型（如：音响系统、座椅材质、天窗类型、智能配置等），需要：
1. 创建新的数据表
2. 修改API接口
3. 更新前端代码
4. 维护成本高，扩展性差

### 1.2 解决方案
采用**通用配置项管理系统**，将所有配置类型抽象为统一的数据结构，通过配置类型来区分不同的配置项。

## 2. 可扩展的数据库设计

### 2.1 核心表结构

```sql
-- 1. 配置类型定义表（config_types）
CREATE TABLE config_types (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  code VARCHAR(50) UNIQUE NOT NULL COMMENT '配置类型代码：package/color/interior/wheel/audio/sunroof等',
  name VARCHAR(100) NOT NULL COMMENT '配置类型名称',
  description TEXT COMMENT '配置类型描述',
  schema_config JSON COMMENT '配置项的字段定义schema',
  ui_config JSON COMMENT 'UI展示配置：展示方式、图标、排序等',
  validation_rules JSON COMMENT '验证规则',
  is_multiple BOOLEAN DEFAULT FALSE COMMENT '是否支持多选',
  is_required BOOLEAN DEFAULT FALSE COMMENT '是否必选',
  display_order INT DEFAULT 0 COMMENT '显示顺序',
  status TINYINT DEFAULT 1 COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_code (code),
  INDEX idx_status (status)
) COMMENT='配置类型定义表';

-- 2. 通用配置项表（config_items）
CREATE TABLE config_items (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  type_id BIGINT NOT NULL COMMENT '配置类型ID',
  code VARCHAR(100) NOT NULL COMMENT '配置项代码',
  name VARCHAR(200) NOT NULL COMMENT '配置项名称',
  description TEXT COMMENT '配置项描述',
  base_price DECIMAL(10,2) DEFAULT 0 COMMENT '基础价格/加价',
  
  -- 通用属性字段（根据不同类型有不同含义）
  attribute_1 VARCHAR(500) COMMENT '属性1：颜色代码/尺寸/功率等',
  attribute_2 VARCHAR(500) COMMENT '属性2：第二色代码/材质/品牌等',
  attribute_3 VARCHAR(500) COMMENT '属性3：扩展属性',
  attribute_4 VARCHAR(500) COMMENT '属性4：扩展属性',
  attribute_5 VARCHAR(500) COMMENT '属性5：扩展属性',
  
  -- JSON扩展字段，存储不确定的属性
  extra_attributes JSON COMMENT '额外属性，JSON格式存储',
  
  -- 媒体资源
  primary_image VARCHAR(500) COMMENT '主图片URL',
  secondary_image VARCHAR(500) COMMENT '副图片URL',
  thumbnail_image VARCHAR(500) COMMENT '缩略图URL',
  video_url VARCHAR(500) COMMENT '视频URL',
  media_gallery JSON COMMENT '图片/视频集合',
  
  -- 关联和约束
  required_items JSON COMMENT '依赖的其他配置项ID列表',
  excluded_items JSON COMMENT '互斥的其他配置项ID列表',
  
  -- 标签和分类
  tags JSON COMMENT '标签列表',
  category VARCHAR(100) COMMENT '分类',
  
  display_order INT DEFAULT 0 COMMENT '显示顺序',
  status TINYINT DEFAULT 1 COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (type_id) REFERENCES config_types(id),
  UNIQUE KEY uk_type_code (type_id, code),
  INDEX idx_type (type_id),
  INDEX idx_code (code),
  INDEX idx_status (status)
) COMMENT='通用配置项表';

-- 3. 车型配置关联表（model_config_relations）
CREATE TABLE model_config_relations (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  model_id BIGINT NOT NULL COMMENT '车型ID',
  config_item_id BIGINT NOT NULL COMMENT '配置项ID',
  config_type_id BIGINT NOT NULL COMMENT '配置类型ID',
  
  -- 关联属性
  is_standard BOOLEAN DEFAULT FALSE COMMENT '是否标配',
  is_optional BOOLEAN DEFAULT TRUE COMMENT '是否可选',
  is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认选中',
  
  -- 价格调整
  price_adjustment DECIMAL(10,2) DEFAULT 0 COMMENT '针对该车型的价格调整',
  
  -- 可用条件
  availability_rules JSON COMMENT '可用规则：如需要特定配置包',
  
  -- 库存信息（可选）
  stock_status VARCHAR(50) COMMENT '库存状态：in_stock/out_of_stock/limited',
  delivery_time VARCHAR(100) COMMENT '交付时间',
  
  display_order INT DEFAULT 0 COMMENT '显示顺序',
  status TINYINT DEFAULT 1 COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (model_id) REFERENCES car_models(id),
  FOREIGN KEY (config_item_id) REFERENCES config_items(id),
  FOREIGN KEY (config_type_id) REFERENCES config_types(id),
  UNIQUE KEY uk_model_item (model_id, config_item_id),
  INDEX idx_model (model_id),
  INDEX idx_item (config_item_id),
  INDEX idx_type (config_type_id)
) COMMENT='车型配置关联表';

-- 4. 配置项特性表（config_item_features）
CREATE TABLE config_item_features (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  config_item_id BIGINT NOT NULL COMMENT '配置项ID',
  feature_name VARCHAR(200) NOT NULL COMMENT '特性名称',
  feature_value TEXT COMMENT '特性值',
  feature_type VARCHAR(50) COMMENT '特性类型：text/boolean/number/list',
  display_order INT DEFAULT 0 COMMENT '显示顺序',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (config_item_id) REFERENCES config_items(id),
  INDEX idx_item (config_item_id)
) COMMENT='配置项特性表';

-- 5. 配置组合规则表（config_combination_rules）
CREATE TABLE config_combination_rules (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  model_id BIGINT COMMENT '车型ID，NULL表示全局规则',
  rule_type VARCHAR(50) NOT NULL COMMENT '规则类型：required/excluded/recommended',
  source_config_ids JSON NOT NULL COMMENT '源配置项ID列表',
  target_config_ids JSON NOT NULL COMMENT '目标配置项ID列表',
  rule_description TEXT COMMENT '规则描述',
  priority INT DEFAULT 0 COMMENT '优先级',
  status TINYINT DEFAULT 1 COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_model (model_id),
  INDEX idx_type (rule_type)
) COMMENT='配置组合规则表';
```

### 2.2 预定义配置类型示例

```sql
-- 插入预定义的配置类型
INSERT INTO config_types (code, name, description, schema_config, ui_config, is_multiple) VALUES
-- 现有配置类型
('package', '配置包', '车型配置包选择', 
  '{"fields": [{"key": "features", "type": "array", "label": "包含特性"}]}',
  '{"display": "card", "icon": "package"}', 
  FALSE),

('color', '外观颜色', '车身颜色选择', 
  '{"fields": [{"key": "color_code", "type": "color", "label": "颜色代码"}, {"key": "is_two_tone", "type": "boolean", "label": "双色"}]}',
  '{"display": "color_picker", "icon": "palette"}', 
  FALSE),

('interior', '内饰', '内饰材质和颜色', 
  '{"fields": [{"key": "material", "type": "string", "label": "材质"}, {"key": "color", "type": "color", "label": "颜色"}]}',
  '{"display": "card", "icon": "chair"}', 
  FALSE),

('wheel', '轮毂', '轮毂样式和尺寸', 
  '{"fields": [{"key": "size", "type": "string", "label": "尺寸"}, {"key": "style", "type": "string", "label": "样式"}]}',
  '{"display": "image_grid", "icon": "wheel"}', 
  FALSE),

-- 未来可扩展的配置类型
('audio', '音响系统', '音响品牌和配置', 
  '{"fields": [{"key": "brand", "type": "string", "label": "品牌"}, {"key": "speakers", "type": "number", "label": "扬声器数量"}]}',
  '{"display": "card", "icon": "speaker"}', 
  FALSE),

('sunroof', '天窗', '天窗类型和尺寸', 
  '{"fields": [{"key": "type", "type": "select", "label": "类型", "options": ["全景", "普通", "分段式"]}, {"key": "size", "type": "string", "label": "尺寸"}]}',
  '{"display": "card", "icon": "sunroof"}', 
  FALSE),

('seat', '座椅配置', '座椅材质和功能', 
  '{"fields": [{"key": "material", "type": "select", "label": "材质", "options": ["真皮", "仿皮", "织物", "Alcantara"]}, {"key": "functions", "type": "array", "label": "功能"}]}',
  '{"display": "card", "icon": "seat"}', 
  FALSE),

('intelligent', '智能配置', '智能驾驶和互联功能', 
  '{"fields": [{"key": "adas_level", "type": "string", "label": "辅助驾驶级别"}, {"key": "features", "type": "array", "label": "智能功能"}]}',
  '{"display": "list", "icon": "chip"}', 
  TRUE),

('battery', '电池配置', '新能源车型电池配置', 
  '{"fields": [{"key": "capacity", "type": "number", "label": "容量(kWh)"}, {"key": "range", "type": "number", "label": "续航(km)"}, {"key": "charging_time", "type": "string", "label": "充电时间"}]}',
  '{"display": "card", "icon": "battery"}', 
  FALSE);
```

## 3. API设计

### 3.1 配置类型管理API

```typescript
// 获取所有配置类型
GET /api/config-types
Response: {
  types: Array<{
    id: string
    code: string
    name: string
    description: string
    schemaConfig: object
    uiConfig: object
    isMultiple: boolean
    isRequired: boolean
  }>
}

// 创建配置类型（管理端）
POST /admin/api/config-types
Body: {
  code: string
  name: string
  description?: string
  schemaConfig?: object
  uiConfig?: object
  isMultiple?: boolean
  isRequired?: boolean
}

// 更新配置类型（管理端）
PUT /admin/api/config-types/{id}

// 删除配置类型（管理端）
DELETE /admin/api/config-types/{id}
```

### 3.2 配置项管理API

```typescript
// 获取配置项列表
GET /api/config-items
Query: {
  typeCode?: string  // 配置类型代码
  modelId?: string   // 车型ID
  page?: number
  pageSize?: number
}
Response: {
  total: number
  items: Array<{
    id: string
    typeCode: string
    typeName: string
    code: string
    name: string
    description: string
    price: number
    attributes: object  // 动态属性
    images: {
      primary: string
      secondary?: string
      thumbnail?: string
      gallery?: string[]
    }
    isStandard?: boolean
    isOptional?: boolean
    isDefault?: boolean
  }>
}

// 创建配置项（管理端）
POST /admin/api/config-items
Body: {
  typeId: string
  code: string
  name: string
  description?: string
  basePrice?: number
  attributes?: object  // 根据配置类型的schema动态传入
  images?: object
  requiredItems?: string[]
  excludedItems?: string[]
  tags?: string[]
}

// 批量导入配置项（管理端）
POST /admin/api/config-items/import
Body: {
  typeCode: string
  items: Array<ConfigItem>
}

// 更新配置项（管理端）
PUT /admin/api/config-items/{id}

// 删除配置项（管理端）
DELETE /admin/api/config-items/{id}
```

### 3.3 车型配置关联API

```typescript
// 获取车型的所有配置
GET /api/models/{modelId}/configs
Response: {
  model: {
    id: string
    name: string
  }
  configGroups: Array<{
    typeCode: string
    typeName: string
    isMultiple: boolean
    isRequired: boolean
    items: Array<{
      id: string
      code: string
      name: string
      price: number
      attributes: object
      images: object
      isStandard: boolean
      isOptional: boolean
      isDefault: boolean
      availabilityRules?: object
    }>
  }>
}

// 关联配置项到车型（管理端）
POST /admin/api/models/{modelId}/configs
Body: {
  configItemId: string
  isStandard?: boolean
  isOptional?: boolean
  isDefault?: boolean
  priceAdjustment?: number
  availabilityRules?: object
}

// 批量关联配置项（管理端）
POST /admin/api/models/{modelId}/configs/batch
Body: {
  configs: Array<{
    configItemId: string
    isStandard?: boolean
    isOptional?: boolean
    isDefault?: boolean
  }>
}

// 更新车型配置关联（管理端）
PUT /admin/api/models/{modelId}/configs/{configId}

// 移除车型配置关联（管理端）
DELETE /admin/api/models/{modelId}/configs/{configId}
```

### 3.4 配置规则管理API

```typescript
// 获取配置组合规则
GET /api/config-rules
Query: {
  modelId?: string
  ruleType?: string
}

// 创建配置规则（管理端）
POST /admin/api/config-rules
Body: {
  modelId?: string
  ruleType: 'required' | 'excluded' | 'recommended'
  sourceConfigIds: string[]
  targetConfigIds: string[]
  ruleDescription?: string
  priority?: number
}

// 验证配置组合
POST /api/config-rules/validate
Body: {
  modelId: string
  selectedConfigs: string[]
}
Response: {
  isValid: boolean
  errors?: Array<{
    type: string
    message: string
    relatedConfigs: string[]
  }>
  warnings?: Array<{
    type: string
    message: string
    relatedConfigs: string[]
  }>
  recommendations?: Array<{
    configId: string
    reason: string
  }>
}
```

## 4. 数据迁移方案

### 4.1 现有数据迁移步骤

```javascript
// 迁移脚本示例
async function migrateExistingData() {
  // 1. 创建配置类型
  const packageType = await createConfigType({
    code: 'package',
    name: '配置包',
    schemaConfig: {
      fields: [
        { key: 'features', type: 'array', label: '包含特性' }
      ]
    }
  });
  
  const colorType = await createConfigType({
    code: 'color',
    name: '外观颜色',
    schemaConfig: {
      fields: [
        { key: 'color_code', type: 'color', label: '颜色代码' },
        { key: 'is_two_tone', type: 'boolean', label: '双色' }
      ]
    }
  });
  
  // 2. 迁移现有配置数据
  const { carModels } = require('./src/config/carModels');
  
  for (const [modelCode, model] of Object.entries(carModels)) {
    // 迁移配置包
    for (const pkg of model.packages) {
      const configItem = await createConfigItem({
        typeId: packageType.id,
        code: pkg.id,
        name: pkg.name,
        basePrice: pkg.price,
        extraAttributes: {
          features: pkg.features,
          featuresTitle: pkg.featuresTitle
        }
      });
      
      // 关联到车型
      await associateConfigToModel({
        modelId: model.id,
        configItemId: configItem.id,
        isOptional: true,
        isDefault: pkg.price === 0
      });
    }
    
    // 迁移颜色配置
    for (const color of model.colors) {
      const configItem = await createConfigItem({
        typeId: colorType.id,
        code: color.id,
        name: color.name,
        basePrice: color.price,
        primaryImage: color.image,
        attribute_1: JSON.stringify(color.colorCode),
        attribute_2: JSON.stringify(color.secondaryColorCode),
        extraAttributes: {
          isTwoTone: color.isTwoTone,
          isPremium: color.isPremium
        }
      });
      
      await associateConfigToModel({
        modelId: model.id,
        configItemId: configItem.id,
        isOptional: true
      });
    }
    
    // 类似地迁移内饰、轮毂等配置
  }
}
```

## 5. 扩展新配置类型示例

### 5.1 添加"智能驾驶配置"

```typescript
// 1. 创建配置类型
POST /admin/api/config-types
{
  "code": "adas",
  "name": "智能驾驶配置",
  "description": "高级驾驶辅助系统配置",
  "schemaConfig": {
    "fields": [
      {
        "key": "level",
        "type": "select",
        "label": "自动驾驶级别",
        "options": ["L0", "L1", "L2", "L2+", "L3"]
      },
      {
        "key": "sensors",
        "type": "object",
        "label": "传感器配置",
        "fields": [
          { "key": "cameras", "type": "number", "label": "摄像头数量" },
          { "key": "radars", "type": "number", "label": "毫米波雷达数量" },
          { "key": "lidars", "type": "number", "label": "激光雷达数量" },
          { "key": "ultrasonic", "type": "number", "label": "超声波雷达数量" }
        ]
      },
      {
        "key": "functions",
        "type": "array",
        "label": "功能列表",
        "items": {
          "type": "object",
          "fields": [
            { "key": "name", "type": "string", "label": "功能名称" },
            { "key": "description", "type": "string", "label": "功能描述" },
            { "key": "enabled", "type": "boolean", "label": "是否启用" }
          ]
        }
      }
    ]
  },
  "uiConfig": {
    "display": "advanced_card",
    "icon": "smart_car",
    "showComparisonTable": true
  },
  "isMultiple": false,
  "isRequired": false
}

// 2. 创建具体配置项
POST /admin/api/config-items
{
  "typeId": "adas_type_id",
  "code": "adas_l2_plus",
  "name": "L2+ 智能驾驶套装",
  "description": "包含高速领航、自动泊车等功能",
  "basePrice": 15000,
  "extraAttributes": {
    "level": "L2+",
    "sensors": {
      "cameras": 11,
      "radars": 5,
      "lidars": 0,
      "ultrasonic": 12
    },
    "functions": [
      {
        "name": "高速领航辅助",
        "description": "高速公路自动驾驶辅助",
        "enabled": true
      },
      {
        "name": "自动泊车",
        "description": "支持平行、垂直、斜向泊车",
        "enabled": true
      },
      {
        "name": "遥控泊车",
        "description": "手机APP遥控泊车",
        "enabled": true
      }
    ]
  },
  "primaryImage": "/images/adas/l2_plus.jpg",
  "mediaGallery": [
    "/images/adas/demo_1.jpg",
    "/images/adas/demo_2.jpg"
  ],
  "tags": ["智能", "安全", "高科技"]
}

// 3. 关联到车型
POST /admin/api/models/{modelId}/configs
{
  "configItemId": "adas_l2_plus_id",
  "isOptional": true,
  "isDefault": false,
  "availabilityRules": {
    "requiredPackage": "luxury",
    "minimumYear": 2024
  }
}
```

### 5.2 添加"定制化选装包"

```typescript
// 创建定制化配置类型
POST /admin/api/config-types
{
  "code": "customization",
  "name": "个性化定制",
  "description": "车辆个性化定制选项",
  "schemaConfig": {
    "fields": [
      {
        "key": "category",
        "type": "select",
        "label": "定制类别",
        "options": ["外观", "内饰", "性能", "舒适", "科技"]
      },
      {
        "key": "customOptions",
        "type": "dynamic",
        "label": "定制选项",
        "allowUserInput": true
      }
    ]
  },
  "isMultiple": true  // 支持多选
}
```

## 6. 前端集成方案

### 6.1 动态配置组件

```typescript
// 通用配置选择组件
interface ConfigSelectorProps {
  modelId: string;
  configType: string;
  onSelect: (config: ConfigItem) => void;
}

const ConfigSelector: React.FC<ConfigSelectorProps> = ({ 
  modelId, 
  configType, 
  onSelect 
}) => {
  const [configs, setConfigs] = useState<ConfigItem[]>([]);
  const [typeInfo, setTypeInfo] = useState<ConfigType>();
  
  useEffect(() => {
    // 获取配置类型信息
    fetchConfigType(configType).then(setTypeInfo);
    // 获取该类型的所有配置项
    fetchConfigItems(modelId, configType).then(setConfigs);
  }, [modelId, configType]);
  
  // 根据配置类型的UI配置动态渲染
  const renderConfig = () => {
    switch (typeInfo?.uiConfig?.display) {
      case 'color_picker':
        return <ColorPicker configs={configs} onSelect={onSelect} />;
      case 'image_grid':
        return <ImageGrid configs={configs} onSelect={onSelect} />;
      case 'card':
        return <CardList configs={configs} onSelect={onSelect} />;
      case 'list':
        return <SimpleList configs={configs} onSelect={onSelect} />;
      default:
        return <DefaultConfigView configs={configs} onSelect={onSelect} />;
    }
  };
  
  return (
    <div className="config-selector">
      <h3>{typeInfo?.name}</h3>
      {typeInfo?.description && <p>{typeInfo.description}</p>}
      {renderConfig()}
    </div>
  );
};

// 车型配置器主组件
const VehicleConfigurator: React.FC = ({ modelId }) => {
  const [configTypes, setConfigTypes] = useState<ConfigType[]>([]);
  const [selectedConfigs, setSelectedConfigs] = useState<Map<string, ConfigItem>>();
  
  useEffect(() => {
    // 动态获取所有可用的配置类型
    fetchAvailableConfigTypes(modelId).then(setConfigTypes);
  }, [modelId]);
  
  return (
    <div className="configurator">
      {configTypes.map(type => (
        <ConfigSelector
          key={type.code}
          modelId={modelId}
          configType={type.code}
          onSelect={(config) => {
            setSelectedConfigs(prev => 
              new Map(prev).set(type.code, config)
            );
          }}
        />
      ))}
    </div>
  );
};
```

### 6.2 管理端动态表单

```typescript
// 根据schema动态生成表单
const DynamicConfigForm: React.FC<{
  configType: ConfigType;
  onSubmit: (data: any) => void;
}> = ({ configType, onSubmit }) => {
  const schema = configType.schemaConfig;
  
  return (
    <Form onSubmit={onSubmit}>
      {schema.fields.map(field => (
        <DynamicField
          key={field.key}
          field={field}
          onChange={(value) => {/* handle change */}}
        />
      ))}
    </Form>
  );
};

// 动态字段组件
const DynamicField: React.FC<{ field: FieldSchema }> = ({ field }) => {
  switch (field.type) {
    case 'string':
      return <Input name={field.key} label={field.label} />;
    case 'number':
      return <NumberInput name={field.key} label={field.label} />;
    case 'boolean':
      return <Checkbox name={field.key} label={field.label} />;
    case 'select':
      return (
        <Select name={field.key} label={field.label}>
          {field.options?.map(opt => (
            <Option key={opt} value={opt}>{opt}</Option>
          ))}
        </Select>
      );
    case 'array':
      return <ArrayField field={field} />;
    case 'object':
      return <ObjectField field={field} />;
    case 'color':
      return <ColorPicker name={field.key} label={field.label} />;
    case 'dynamic':
      return <DynamicInput name={field.key} label={field.label} />;
    default:
      return <Input name={field.key} label={field.label} />;
  }
};
```

## 7. 优势总结

### 7.1 高度可扩展
1. **无需修改数据库结构**：新增配置类型只需在config_types表中添加记录
2. **无需修改核心API**：API自动支持新的配置类型
3. **前端自动适配**：根据配置类型的schema和UI配置自动渲染

### 7.2 灵活配置
1. **动态字段**：通过JSON存储不确定的属性
2. **自定义验证**：每种配置类型可定义自己的验证规则
3. **复杂关联**：支持配置项之间的依赖和互斥关系

### 7.3 统一管理
1. **一致的API接口**：所有配置类型使用相同的API模式
2. **统一的管理界面**：管理端可复用相同的组件
3. **标准化的数据结构**：便于维护和扩展

### 7.4 业务适应性
1. **支持标配/选配**：灵活定义每个配置项的属性
2. **价格策略**：支持基础价格和车型特定价格调整
3. **库存管理**：可扩展支持库存和交付时间
4. **国际化**：易于添加多语言支持

## 8. 实施建议

### 8.1 分阶段实施
1. **第一阶段**：迁移现有的4种配置类型（配置包、颜色、内饰、轮毂）
2. **第二阶段**：添加常用扩展配置（音响、天窗、座椅等）
3. **第三阶段**：支持完全自定义的配置类型

### 8.2 向后兼容
1. 保留原有API一段时间，逐步迁移
2. 提供数据迁移工具
3. 前端可同时支持新旧两种方式

### 8.3 性能优化
1. 配置数据缓存策略
2. 批量查询优化
3. 前端懒加载配置项

这个方案的核心优势是**"一次设计，无限扩展"**，真正实现了配置系统的灵活性和可维护性。