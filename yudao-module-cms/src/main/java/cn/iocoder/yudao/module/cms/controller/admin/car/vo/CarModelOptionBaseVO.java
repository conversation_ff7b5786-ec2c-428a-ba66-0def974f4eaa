package cn.iocoder.yudao.module.cms.controller.admin.car.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 车型配置选项 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class CarModelOptionBaseVO {

    @Schema(description = "车型ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "车型ID不能为空")
    private Long modelId;

    @Schema(description = "选项类型ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "选项类型ID不能为空")
    private Long optionTypeId;

    @Schema(description = "选项代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "red_color")
    @NotNull(message = "选项代码不能为空")
    @Length(max = 50, message = "选项代码长度不能超过50个字符")
    private String optionCode;

    @Schema(description = "选项名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "红色")
    @NotNull(message = "选项名称不能为空")
    @Length(max = 100, message = "选项名称长度不能超过100个字符")
    private String name;

    @Schema(description = "选项价格", example = "2000.00")
    private BigDecimal price;

    @Schema(description = "选项图片URL", example = "https://example.com/image.jpg")
    @Length(max = 500, message = "选项图片URL长度不能超过500个字符")
    private String imageUrl;

    @Schema(description = "缩略图URL数组", example = "[\"https://example.com/thumb1.jpg\",\"https://example.com/thumb2.jpg\"]")
    private List<String> thumbnailUrls;

    @Schema(description = "需要的配置包", example = "package_001")
    @Length(max = 100, message = "需要的配置包长度不能超过100个字符")
    private String requiredPackage;

    @Schema(description = "选项配置数据", example = "{\"colorCode\":\"#FF0000\",\"material\":\"metallic\"}")
    private Map<String, Object> configData;

    @Schema(description = "排序", example = "1")
    private Integer sortOrder;

    @Schema(description = "状态：0-禁用，1-启用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

}