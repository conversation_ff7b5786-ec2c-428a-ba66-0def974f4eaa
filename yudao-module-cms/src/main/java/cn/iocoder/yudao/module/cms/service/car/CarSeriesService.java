package cn.iocoder.yudao.module.cms.service.car;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarSeriesCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarSeriesPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarSeriesUpdateReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarSeriesDO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 车系管理 Service 接口
 *
 * <AUTHOR>
 */
public interface CarSeriesService {

    /**
     * 创建车系
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCarSeries(@Valid CarSeriesCreateReqVO createReqVO);

    /**
     * 更新车系
     *
     * @param updateReqVO 更新信息
     */
    void updateCarSeries(@Valid CarSeriesUpdateReqVO updateReqVO);

    /**
     * 删除车系
     *
     * @param id 编号
     */
    void deleteCarSeries(Long id);

    /**
     * 获得车系
     *
     * @param id 编号
     * @return 车系
     */
    CarSeriesDO getCarSeries(Long id);

    /**
     * 获得车系分页
     *
     * @param pageReqVO 分页查询
     * @return 车系分页
     */
    PageResult<CarSeriesDO> getCarSeriesPage(CarSeriesPageReqVO pageReqVO);

    /**
     * 获得车系列表
     *
     * @param brand 品牌标识
     * @param status 状态
     * @return 车系列表
     */
    List<CarSeriesDO> getCarSeriesList(String brand, Integer status);

    /**
     * 根据编码获取车系
     *
     * @param code 车系编码
     * @return 车系信息
     */
    CarSeriesDO getCarSeriesByCode(String code);

}