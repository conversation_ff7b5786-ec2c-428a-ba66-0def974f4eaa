package cn.iocoder.yudao.module.cms.framework.security;

import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * CMS 审计日志切面
 * 记录敏感操作：发布、删除、回滚等
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class CmsAuditLogAspect {

    /**
     * 记录页面发布操作
     */
    @AfterReturning(pointcut = "@annotation(cn.iocoder.yudao.module.cms.framework.security.annotation.CmsAuditLog)", returning = "result")
    public void auditPublishOperation(JoinPoint joinPoint, Object result) {
        try {
            // 获取方法信息
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            String className = joinPoint.getTarget().getClass().getName();
            String methodName = method.getName();

            // 获取用户信息
            Long userId = SecurityFrameworkUtils.getLoginUserId();
            String userIp = ServletUtils.getClientIP();

            // 获取请求参数
            Object[] args = joinPoint.getArgs();
            String params = JSONUtil.toJsonStr(args);

            // 构建审计日志
            Map<String, Object> auditLog = new HashMap<>();
            auditLog.put("userId", userId);
            auditLog.put("tenantId", TenantContextHolder.getTenantId());
            auditLog.put("userIp", userIp);
            auditLog.put("className", className);
            auditLog.put("methodName", methodName);
            auditLog.put("params", params);
            auditLog.put("result", JSONUtil.toJsonStr(result));
            auditLog.put("operationType", getOperationType(methodName));
            auditLog.put("operationTime", LocalDateTime.now());
            auditLog.put("success", true);

            // 记录审计日志
            log.info("[CMS审计日志] 操作成功: {}", JSONUtil.toJsonStr(auditLog));

        } catch (Exception e) {
            log.error("[CMS审计日志] 记录失败", e);
        }
    }

    /**
     * 记录操作异常
     */
    @AfterThrowing(pointcut = "@annotation(cn.iocoder.yudao.module.cms.framework.security.annotation.CmsAuditLog)", throwing = "exception")
    public void auditException(JoinPoint joinPoint, Exception exception) {
        try {
            // 获取方法信息
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            String className = joinPoint.getTarget().getClass().getName();
            String methodName = method.getName();

            // 获取用户信息
            Long userId = SecurityFrameworkUtils.getLoginUserId();
            String userIp = ServletUtils.getClientIP();

            // 获取请求参数
            Object[] args = joinPoint.getArgs();
            String params = JSONUtil.toJsonStr(args);

            // 构建审计日志
            Map<String, Object> auditLog = new HashMap<>();
            auditLog.put("userId", userId);
            auditLog.put("tenantId", TenantContextHolder.getTenantId());
            auditLog.put("userIp", userIp);
            auditLog.put("className", className);
            auditLog.put("methodName", methodName);
            auditLog.put("params", params);
            auditLog.put("error", exception.getMessage());
            auditLog.put("operationType", getOperationType(methodName));
            auditLog.put("operationTime", LocalDateTime.now());
            auditLog.put("success", false);

            // 记录审计日志
            log.error("[CMS审计日志] 操作失败: {}", JSONUtil.toJsonStr(auditLog));

        } catch (Exception e) {
            log.error("[CMS审计日志] 记录失败", e);
        }
    }

    /**
     * 根据方法名判断操作类型
     */
    private String getOperationType(String methodName) {
        if (methodName.contains("publish") || methodName.contains("Publish")) {
            return "PUBLISH";
        } else if (methodName.contains("delete") || methodName.contains("Delete")) {
            return "DELETE";
        } else if (methodName.contains("rollback") || methodName.contains("Rollback")) {
            return "ROLLBACK";
        } else if (methodName.contains("offline") || methodName.contains("Offline")) {
            return "OFFLINE";
        } else if (methodName.contains("create") || methodName.contains("Create")) {
            return "CREATE";
        } else if (methodName.contains("update") || methodName.contains("Update")) {
            return "UPDATE";
        } else {
            return "OTHER";
        }
    }
}