# 车型配置管理系统配置文件
# 作者：开发团队
# 日期：2025-01-09

cms:
  car:
    # 缓存配置
    cache:
      # L1缓存(Caffeine)配置
      l1:
        # 车系缓存
        series:
          max-size: 1000
          expire-after-write: 30m
          refresh-after-write: 10m
        # 车型缓存  
        models:
          max-size: 5000
          expire-after-write: 1h
          refresh-after-write: 30m
        # 配置选项缓存
        options:
          max-size: 2000
          expire-after-write: 2h
          refresh-after-write: 1h
        # 融资方案缓存
        finance:
          max-size: 500
          expire-after-write: 4h
          refresh-after-write: 2h
      
      # L2缓存(Redis)配置
      l2:
        # 缓存键前缀
        key-prefix: "cms:car:"
        # 默认TTL
        default-ttl: 3600
        # 车系缓存TTL
        series-ttl: 7200
        # 车型缓存TTL
        models-ttl: 3600
        # 配置选项缓存TTL
        options-ttl: 1800
        # 融资方案缓存TTL
        finance-ttl: 14400
        # 空值缓存TTL(防止缓存穿透)
        null-ttl: 300

    # JSON Schema验证配置
    json-schema:
      # 是否启用严格模式
      strict-mode: true
      # 是否允许额外属性
      allow-additional-properties: false
      # 验证错误时是否详细输出
      verbose-errors: true
      # Schema缓存配置
      cache:
        max-size: 100
        expire-after-write: 1h

    # API配置
    api:
      # 版本管理
      version:
        # 默认版本
        default: "default"
        # 支持的版本列表
        supported: ["default", "v1", "v2", "v3"]
        # 版本兼容模式
        compatibility-mode: true
      
      # SQL配置管理
      sql:
        # 是否启用SQL缓存
        cache-enabled: true
        # SQL缓存TTL(秒)
        cache-ttl: 1800
        # 最大并发执行数
        max-concurrent: 50
        # 查询超时时间(秒)
        query-timeout: 30


    # 文件上传配置
    upload:
      # 车型图片上传路径
      car-images-path: "/cms/car/images/"
      # 配置选项图片路径
      option-images-path: "/cms/car/options/"
      # 允许的文件类型
      allowed-types: 
        - "image/jpeg"
        - "image/png" 
        - "image/webp"
      # 最大文件大小(MB)
      max-file-size: 10

    # 数据验证配置
    validation:
      # 是否启用数据一致性检查
      consistency-check: true
      # 批量操作最大数量
      max-batch-size: 100
      # 是否启用乐观锁
      optimistic-lock: true

    # 性能监控配置
    monitoring:
      # 是否启用性能监控
      enabled: true
      # 慢查询阈值(毫秒)
      slow-query-threshold: 500
      # 是否记录SQL执行日志
      log-sql: false
      # 缓存命中率统计间隔(秒)
      cache-stats-interval: 300

# Spring Boot缓存配置扩展
spring:
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=10000,expireAfterWrite=1h
    
# MyBatis Plus配置扩展
mybatis-plus:
  configuration:
    # 启用二级缓存
    cache-enabled: true
  global-config:
    db-config:
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    # CMS车型模块日志级别
    cn.iocoder.yudao.module.cms.service.car: INFO
    cn.iocoder.yudao.module.cms.controller.admin.car: INFO
    cn.iocoder.yudao.module.cms.dal.mysql.car: DEBUG
    # SQL日志
    org.springframework.jdbc.core.JdbcTemplate: DEBUG
  pattern:
    # 控制台日志格式
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"