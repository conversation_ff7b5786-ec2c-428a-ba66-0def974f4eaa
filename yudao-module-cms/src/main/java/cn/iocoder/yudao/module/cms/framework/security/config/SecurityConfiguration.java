package cn.iocoder.yudao.module.cms.framework.security.config;

import cn.iocoder.yudao.framework.security.config.AuthorizeRequestsCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer;

/**
 * CMS 模块的 Security 配置
 */
@Configuration(proxyBeanMethods = false, value = "cmsSecurityConfiguration")
public class SecurityConfiguration {

    @Bean("cmsAuthorizeRequestsCustomizer")
    public AuthorizeRequestsCustomizer authorizeRequestsCustomizer() {
        return new AuthorizeRequestsCustomizer() {

            @Override
            public void customize(AuthorizeHttpRequestsConfigurer<HttpSecurity>.AuthorizationManagerRequestMatcherRegistry registry) {
                // CMS App 端接口无需登录
                registry.requestMatchers("/app/cms/**").permitAll();
                // 兼容旧的 promotion 路径
                registry.requestMatchers("/promotion/banner/**").permitAll();
                registry.requestMatchers("/promotion/article/**").permitAll();
            }

        };
    }

}