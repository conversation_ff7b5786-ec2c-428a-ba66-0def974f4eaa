package cn.iocoder.yudao.module.cms.service.dealer;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.cms.controller.app.dealer.vo.AppDealerDetailRespVO;
import cn.iocoder.yudao.module.cms.controller.app.dealer.vo.AppDealerRespVO;
import cn.iocoder.yudao.module.cms.controller.app.dealer.vo.AppDealerSearchReqVO;

/**
 * App端经销商 Service 接口
 *
 * <AUTHOR>
 */
public interface AppDealerService {

    /**
     * 综合查询经销商列表
     *
     * @param reqVO 查询条件
     * @return 经销商分页列表
     */
    PageResult<AppDealerRespVO> searchDealers(AppDealerSearchReqVO reqVO);

    /**
     * 获得经销商详情
     *
     * @param id 经销商ID
     * @return 经销商详情
     */
    AppDealerDetailRespVO getDealerDetail(Long id);

}