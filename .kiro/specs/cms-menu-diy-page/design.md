# CMS Menu and DIY Page Management Design Document

## Overview

This design implements a comprehensive Content Management System (CMS) within the yudao-vue-pro architecture, providing hierarchical menu management and DIY page creation with advanced features including optimistic locking, version control, multi-level caching, and access analytics.

The system follows a layered architecture with clear separation of concerns:
- **Controller Layer**: Admin and App API endpoints
- **Service Layer**: Business logic and caching
- **Data Access Layer**: MyBatis Plus with optimistic locking
- **Caching Layer**: Caffeine (L1) + Redis (L2) dual-level caching
- **Statistics Layer**: Asynchronous visit tracking and analytics

## Architecture

### System Architecture Diagram

```mermaid
graph TB
    subgraph "Client Layer"
        AC[Admin Console]
        APP[Mobile/Web App]
    end
    
    subgraph "API Gateway"
        AG[API Gateway]
    end
    
    subgraph "Application Layer"
        subgraph "Admin APIs"
            AMC[Menu Controller]
            APC[Page Controller]
            AVC[Version Controller]
        end
        
        subgraph "App APIs"
            APMC[App Menu Controller]
            APPC[App Page Controller]
            ASC[Statistics Controller]
        end
    end
    
    subgraph "Service Layer"
        MS[Menu Service]
        PS[Page Service]
        VS[Version Service]
        SS[Statistics Service]
        CS[Cache Service]
    end
    
    subgraph "Caching Layer"
        L1[L1 Cache - Caffeine]
        L2[L2 Cache - Redis]
        SYNC[Cache Sync Service]
    end
    
    subgraph "Data Layer"
        MM[Menu Mapper]
        PM[Page Mapper]
        VM[Version Mapper]
        SM[Statistics Mapper]
        DB[(MySQL Database)]
    end
    
    subgraph "Message Queue"
        MQ[Redis Pub/Sub]
    end
    
    AC --> AG
    APP --> AG
    AG --> AMC
    AG --> APC
    AG --> AVC
    AG --> APMC
    AG --> APPC
    AG --> ASC
    
    AMC --> MS
    APC --> PS
    AVC --> VS
    APMC --> MS
    APPC --> PS
    ASC --> SS
    
    MS --> CS
    PS --> CS
    VS --> CS
    SS --> CS
    
    CS --> L1
    CS --> L2
    L2 --> SYNC
    SYNC --> MQ
    
    MS --> MM
    PS --> PM
    VS --> VM
    SS --> SM
    
    MM --> DB
    PM --> DB
    VM --> DB
    SM --> DB
```

### Multi-Level Caching Architecture

```mermaid
graph LR
    subgraph "Cache Flow"
        REQ[Request] --> L1C{L1 Cache Hit?}
        L1C -->|Yes| L1R[Return from L1]
        L1C -->|No| L2C{L2 Cache Hit?}
        L2C -->|Yes| L2R[Return from L2]
        L2C -->|No| DB[Query Database]
        L2R --> L1W[Async Write to L1]
        DB --> CW[Write to Both Caches]
        CW --> DBR[Return Result]
    end
    
    subgraph "Cache Sync"
        UPD[Data Update] --> INV[Invalidate Caches]
        INV --> PUB[Publish Sync Message]
        PUB --> SUB[Other Instances Subscribe]
        SUB --> CLEAR[Clear Local Cache]
    end
```

## Components and Interfaces

### Core Domain Models

#### CmsMenuDO
```java
@TableName("cms_menu")
@Data
public class CmsMenuDO extends BaseDO {
    @TableId
    private Long id;
    
    @NotBlank(message = "菜单名称不能为空")
    @Length(max = 50, message = "菜单名称长度不能超过50个字符")
    private String name;
    
    @Length(max = 100, message = "菜单图标长度不能超过100个字符")
    private String icon;
    
    @NotBlank(message = "菜单路径不能为空")
    @Length(max = 200, message = "菜单路径长度不能超过200个字符")
    private String path;
    
    private Long parentId;
    
    @NotNull(message = "显示顺序不能为空")
    private Integer sort;
    
    @NotNull(message = "状态不能为空")
    private Integer status; // 0-启用，1-禁用
}
```

#### CmsDiyPageDO
```java
@TableName("cms_diy_page")
@Data
public class CmsDiyPageDO extends BaseDO {
    @TableId
    private Long id;
    
    @NotBlank(message = "页面UUID不能为空")
    @Length(max = 36, message = "UUID长度固定为36个字符")
    private String uuid;
    
    @NotBlank(message = "页面名称不能为空")
    @Length(max = 100, message = "页面名称长度不能超过100个字符")
    private String name;
    
    private Long parentId;
    
    @NotNull(message = "关联菜单ID不能为空")
    private Long menuId;
    
    @NotBlank(message = "页面路径不能为空")
    @Length(max = 500, message = "页面路径长度不能超过500个字符")
    private String path;
    
    @Length(max = 200, message = "关键词长度不能超过200个字符")
    private String keywords;
    
    private String description;
    
    @NotNull(message = "状态不能为空")
    private Integer status; // 0-草稿，1-已发布，2-已下线
    
    @Version // MyBatis Plus 乐观锁注解
    @NotNull(message = "版本号不能为空")
    private Integer version;
    
    private Integer publishedVersion;
    
    private String content; // JSON格式的页面内容
}
```

#### CmsDiyPageVersionDO
```java
@TableName("cms_diy_page_version")
@Data
public class CmsDiyPageVersionDO extends BaseDO {
    @TableId
    private Long id;
    
    @NotNull(message = "页面ID不能为空")
    private Long pageId;
    
    @NotNull(message = "版本号不能为空")
    private Integer version;
    
    @NotBlank(message = "版本名称不能为空")
    @Length(max = 100, message = "版本名称长度不能超过100个字符")
    private String name;
    
    private String content; // JSON格式的页面内容快照
    
    private LocalDateTime publishTime;
    
    @NotNull(message = "发布状态不能为空")
    private Boolean isPublished;
    
    @Length(max = 500, message = "版本备注长度不能超过500个字符")
    private String remark;
}
```

### Service Layer Interfaces

#### CmsMenuService
```java
public interface CmsMenuService {
    // 基础CRUD操作
    Long createMenu(CmsMenuSaveReqVO createReqVO);
    void updateMenu(CmsMenuSaveReqVO updateReqVO);
    void deleteMenu(Long id);
    CmsMenuDO getMenu(Long id);
    List<CmsMenuDO> getMenuList(CmsMenuListReqVO listReqVO);
    
    // 树形结构操作
    List<CmsMenuRespVO> getMenuTree(CmsMenuListReqVO listReqVO);
    String buildMenuPath(Long menuId);
    
    // App端接口
    List<CmsMenuRespVO> getEnabledMenuTree();
    CmsMenuDO getMenuByPath(String path);
    
    // 缓存管理
    void clearMenuCache(Long tenantId);
    void warmupMenuCache(Long tenantId);
}
```

#### CmsDiyPageService
```java
public interface CmsDiyPageService {
    // 基础CRUD操作（带乐观锁）
    Long createPage(CmsDiyPageSaveReqVO createReqVO);
    void updatePage(CmsDiyPageSaveReqVO updateReqVO); // 乐观锁更新
    void deletePage(Long id);
    CmsDiyPageDO getPage(Long id);
    PageResult<CmsDiyPageDO> getPagePage(CmsDiyPagePageReqVO pageReqVO);
    
    // 发布管理
    void publishPage(Long id, Integer version, String remark);
    void offlinePage(Long id, Integer version);
    
    // 路径管理
    String buildPagePath(Long menuId, Long parentId, String pagePath);
    
    // 版本控制
    void checkVersionConflict(Long id, Integer version);
    
    // App端接口
    CmsDiyPageDO getPublishedPageByPath(String path);
    CmsDiyPageDO getPublishedPageByUuid(String uuid);
    PageResult<CmsDiyPageDO> getPublishedPagesByMenu(Long menuId, Integer pageNo, Integer pageSize);
    PageResult<CmsDiyPageDO> searchPublishedPages(String keyword, Integer pageNo, Integer pageSize);
    List<CmsBreadcrumbRespVO> getPageBreadcrumb(Long pageId);
    
    // 访问统计
    void recordPageVisit(CmsPageVisitReqVO visitReqVO);
    
    // 缓存管理
    void clearPageCache(Long pageId);
    void warmupPageCache(List<Long> pageIds);
    List<CmsDiyPageDO> getHotPages(Integer limit);
}
```

#### CmsDiyPageVersionService
```java
public interface CmsDiyPageVersionService {
    // 版本管理
    Long createVersionOnPublish(Long pageId, String content, String remark);
    PageResult<CmsDiyPageVersionDO> getVersionPage(CmsDiyPageVersionPageReqVO pageReqVO);
    CmsDiyPageVersionDO getVersion(Long pageId, Integer version);
    CmsDiyPageVersionDO getPublishedVersion(Long pageId);
    
    // 版本操作
    void rollbackToVersion(Long pageId, Integer targetVersion, Integer currentVersion);
    Integer getNextVersionNumber(Long pageId);
    void cleanupOldVersions(Long pageId, Integer keepCount);
}
```

### Caching Service Design

#### Multi-Level Cache Manager
```java
@Service
@Slf4j
public class CmsCacheService {
    
    @Autowired
    @Qualifier("multiLevelCacheManager")
    private CacheManager cacheManager;
    
    @Autowired
    private CacheSyncService cacheSyncService;
    
    // 菜单缓存操作
    @Cacheable(value = "cms:menu", key = "'tree:' + #tenantId", 
               cacheManager = "multiLevelCacheManager")
    public List<CmsMenuRespVO> getCachedMenuTree(Long tenantId) {
        // 实际查询逻辑由调用方提供
        return null;
    }
    
    @CacheEvict(value = "cms:menu", key = "'tree:' + #tenantId", 
                cacheManager = "multiLevelCacheManager")
    public void evictMenuCache(Long tenantId) {
        cacheSyncService.publishCacheEvict("cms:menu", "tree:" + tenantId);
    }
    
    // 页面缓存操作
    @Cacheable(value = "cms:page", key = "'content:' + #pageId", 
               cacheManager = "multiLevelCacheManager")
    public CmsDiyPageDO getCachedPage(Long pageId) {
        return null;
    }
    
    @CacheEvict(value = {"cms:page", "cms:page:path"}, 
                key = "'content:' + #pageId", 
                cacheManager = "multiLevelCacheManager")
    public void evictPageCache(Long pageId) {
        cacheSyncService.publishCacheEvict("cms:page", "content:" + pageId);
        cacheSyncService.publishCacheClear("cms:page:path");
    }
    
    // 路径映射缓存
    @Cacheable(value = "cms:page:path", key = "#path", 
               cacheManager = "multiLevelCacheManager")
    public Long getCachedPageIdByPath(String path) {
        return null;
    }
}
```

#### Cache Synchronization Service
```java
@Service
@Slf4j
public class CacheSyncService {
    
    private final RedisTemplate<String, Object> redisTemplate;
    private final String instanceId;
    
    private static final String CACHE_SYNC_CHANNEL = "cms:cache:sync";
    private static final String CACHE_EVICT_CHANNEL = "cms:cache:evict";
    
    public void publishCacheUpdate(String cacheName, Object key, Object value) {
        CacheSyncMessage message = CacheSyncMessage.builder()
                .instanceId(instanceId)
                .cacheName(cacheName)
                .key(key)
                .value(value)
                .operation(CacheOperation.PUT)
                .timestamp(System.currentTimeMillis())
                .build();
        
        redisTemplate.convertAndSend(CACHE_SYNC_CHANNEL, message);
    }
    
    public void publishCacheEvict(String cacheName, Object key) {
        CacheSyncMessage message = CacheSyncMessage.builder()
                .instanceId(instanceId)
                .cacheName(cacheName)
                .key(key)
                .operation(CacheOperation.EVICT)
                .timestamp(System.currentTimeMillis())
                .build();
        
        redisTemplate.convertAndSend(CACHE_EVICT_CHANNEL, message);
    }
    
    public void publishCacheClear(String cacheName) {
        CacheSyncMessage message = CacheSyncMessage.builder()
                .instanceId(instanceId)
                .cacheName(cacheName)
                .operation(CacheOperation.CLEAR)
                .timestamp(System.currentTimeMillis())
                .build();
        
        redisTemplate.convertAndSend(CACHE_EVICT_CHANNEL, message);
    }
}
```

## Data Models

### Database Schema Design

#### Primary Tables
```sql
-- CMS菜单表
CREATE TABLE `cms_menu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `name` varchar(50) NOT NULL COMMENT '菜单名称',
  `icon` varchar(100) DEFAULT NULL COMMENT '菜单图标',
  `path` varchar(200) NOT NULL COMMENT '菜单路径',
  `parent_id` bigint DEFAULT 0 COMMENT '上级菜单ID，0表示根菜单',
  `sort` int DEFAULT 0 COMMENT '显示顺序',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态：0-启用，1-禁用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_path` (`path`, `deleted`, `tenant_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CMS菜单表';

-- CMS DIY页面表
CREATE TABLE `cms_diy_page` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '页面ID',
  `uuid` varchar(36) NOT NULL COMMENT '页面UUID，全局唯一',
  `name` varchar(100) NOT NULL COMMENT '页面名称',
  `parent_id` bigint DEFAULT NULL COMMENT '上级页面ID',
  `menu_id` bigint NOT NULL COMMENT '关联菜单ID',
  `path` varchar(500) NOT NULL COMMENT '页面路径，唯一',
  `keywords` varchar(200) DEFAULT NULL COMMENT '关键词',
  `description` text COMMENT '页面描述',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态：0-草稿，1-已发布，2-已下线',
  `version` int NOT NULL DEFAULT 1 COMMENT '当前版本号，用于乐观锁',
  `published_version` int DEFAULT NULL COMMENT '已发布的版本号',
  `content` longtext COMMENT '页面内容JSON',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_uuid` (`uuid`, `deleted`, `tenant_id`),
  UNIQUE KEY `uk_path` (`path`, `deleted`, `tenant_id`),
  KEY `idx_menu_id` (`menu_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CMS DIY页面表';

-- CMS DIY页面版本表
CREATE TABLE `cms_diy_page_version` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '版本ID',
  `page_id` bigint NOT NULL COMMENT '页面ID',
  `version` int NOT NULL COMMENT '版本号',
  `name` varchar(100) NOT NULL COMMENT '版本名称',
  `content` longtext COMMENT '页面内容JSON',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `is_published` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已发布',
  `remark` varchar(500) DEFAULT NULL COMMENT '版本备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_page_version` (`page_id`, `version`, `deleted`, `tenant_id`),
  KEY `idx_page_id` (`page_id`),
  KEY `idx_publish_time` (`publish_time`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CMS DIY页面版本表';
```

#### Statistics Tables
```sql
-- CMS页面访问统计表
CREATE TABLE `cms_page_visit_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '访问记录ID',
  `page_id` bigint NOT NULL COMMENT '页面ID',
  `page_uuid` varchar(36) NOT NULL COMMENT '页面UUID',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID（登录用户）',
  `session_id` varchar(64) DEFAULT NULL COMMENT '会话ID',
  `ip` varchar(45) DEFAULT NULL COMMENT '访问IP',
  `user_agent` text COMMENT '用户代理',
  `referer` varchar(500) DEFAULT NULL COMMENT '来源页面',
  `visit_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
  `stay_time` int DEFAULT 0 COMMENT '停留时间（秒）',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_page_id` (`page_id`),
  KEY `idx_page_uuid` (`page_uuid`),
  KEY `idx_visit_time` (`visit_time`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CMS页面访问统计表';

-- CMS页面访问统计汇总表
CREATE TABLE `cms_page_visit_stats` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `page_id` bigint NOT NULL COMMENT '页面ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `total_visits` int NOT NULL DEFAULT 0 COMMENT '总访问次数',
  `unique_visitors` int NOT NULL DEFAULT 0 COMMENT '独立访客数',
  `avg_stay_time` int NOT NULL DEFAULT 0 COMMENT '平均停留时间（秒）',
  `bounce_count` int NOT NULL DEFAULT 0 COMMENT '跳出次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_page_date` (`page_id`, `stat_date`, `tenant_id`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CMS页面访问统计汇总表';
```

### Optimistic Locking Implementation

#### MyBatis Mapper with Version Control
```java
@Mapper
public interface CmsDiyPageMapper extends BaseMapperX<CmsDiyPageDO> {
    
    /**
     * 根据ID和版本号更新页面（乐观锁）
     */
    @Update("UPDATE cms_diy_page SET " +
            "name = #{updateObj.name}, " +
            "parent_id = #{updateObj.parentId}, " +
            "menu_id = #{updateObj.menuId}, " +
            "path = #{updateObj.path}, " +
            "keywords = #{updateObj.keywords}, " +
            "description = #{updateObj.description}, " +
            "status = #{updateObj.status}, " +
            "published_version = #{updateObj.publishedVersion}, " +
            "content = #{updateObj.content}, " +
            "version = #{updateObj.version}, " +
            "updater = #{updateObj.updater}, " +
            "update_time = NOW() " +
            "WHERE id = #{updateObj.id} AND version = #{version} AND deleted = 0")
    int updateByIdAndVersion(@Param("updateObj") CmsDiyPageDO updateObj, 
                            @Param("version") Integer version);
    
    /**
     * 根据ID和版本号删除页面（乐观锁）
     */
    @Update("UPDATE cms_diy_page SET " +
            "deleted = 1, " +
            "updater = #{updater}, " +
            "update_time = NOW() " +
            "WHERE id = #{id} AND version = #{version} AND deleted = 0")
    int deleteByIdAndVersion(@Param("id") Long id, 
                            @Param("version") Integer version, 
                            @Param("updater") String updater);
}
```

## Error Handling

### Error Code Definitions
```java
public interface CmsErrorCodeConstants {
    // ========== CMS 菜单 1-013-021-000 ==========
    ErrorCode CMS_MENU_NOT_EXISTS = new ErrorCode(1_013_021_000, "菜单不存在");
    ErrorCode CMS_MENU_PATH_DUPLICATE = new ErrorCode(1_013_021_001, "菜单路径已存在");
    ErrorCode CMS_MENU_DELETE_FAIL_HAVE_CHILDREN = new ErrorCode(1_013_021_002, "菜单删除失败，存在子菜单");
    ErrorCode CMS_MENU_DELETE_FAIL_HAVE_PAGES = new ErrorCode(1_013_021_003, "菜单删除失败，存在关联页面");

    // ========== CMS DIY 页面 1-013-022-000 ==========
    ErrorCode CMS_DIY_PAGE_NOT_EXISTS = new ErrorCode(1_013_022_000, "DIY页面不存在");
    ErrorCode CMS_DIY_PAGE_PATH_DUPLICATE = new ErrorCode(1_013_022_001, "页面路径已存在");
    ErrorCode CMS_DIY_PAGE_UUID_DUPLICATE = new ErrorCode(1_013_022_002, "页面UUID已存在");
    ErrorCode CMS_DIY_PAGE_PUBLISH_FAIL_NO_CONTENT = new ErrorCode(1_013_022_003, "发布失败，页面内容不能为空");
    ErrorCode CMS_DIY_PAGE_DELETE_FAIL_HAVE_CHILDREN = new ErrorCode(1_013_022_004, "页面删除失败，存在子页面");

    // ========== CMS DIY 页面版本 1-013-023-000 ==========
    ErrorCode CMS_DIY_PAGE_VERSION_NOT_EXISTS = new ErrorCode(1_013_023_000, "页面版本不存在");
    ErrorCode CMS_DIY_PAGE_VERSION_CONFLICT = new ErrorCode(1_013_023_001, "页面版本冲突，请刷新后重试");
    ErrorCode CMS_DIY_PAGE_ROLLBACK_FAIL_SAME_VERSION = new ErrorCode(1_013_023_002, "回滚失败，目标版本与当前版本相同");

    // ========== CMS App 端 1-013-024-000 ==========
    ErrorCode CMS_PAGE_NOT_PUBLISHED = new ErrorCode(1_013_024_000, "页面未发布或已下线");
    ErrorCode CMS_PAGE_PATH_NOT_FOUND = new ErrorCode(1_013_024_001, "页面路径不存在");
    ErrorCode CMS_PAGE_UUID_NOT_FOUND = new ErrorCode(1_013_024_002, "页面UUID不存在");
    ErrorCode CMS_MENU_PATH_NOT_FOUND = new ErrorCode(1_013_024_003, "菜单路径不存在");
}
```

### Exception Handling Strategy
```java
@Service
@Slf4j
public class CmsDiyPageServiceImpl implements CmsDiyPageService {
    
    @Override
    public void updatePage(CmsDiyPageSaveReqVO updateReqVO) {
        // 1. 检查页面是否存在
        CmsDiyPageDO existPage = validatePageExists(updateReqVO.getId());
        
        // 2. 乐观锁版本检查
        if (!Objects.equals(existPage.getVersion(), updateReqVO.getVersion())) {
            throw exception(CMS_DIY_PAGE_VERSION_CONFLICT);
        }
        
        // 3. 路径唯一性检查
        validatePathUnique(updateReqVO.getId(), updateReqVO.getPath());
        
        // 4. 构建更新对象
        CmsDiyPageDO updateObj = CmsDiyPageConvert.INSTANCE.convert(updateReqVO);
        updateObj.setVersion(existPage.getVersion() + 1);
        
        // 5. 执行更新（使用乐观锁）
        int updateCount = diyPageMapper.updateByIdAndVersion(updateObj, updateReqVO.getVersion());
        if (updateCount == 0) {
            throw exception(CMS_DIY_PAGE_VERSION_CONFLICT);
        }
        
        // 6. 清除相关缓存
        clearPageCache(updateReqVO.getId());
    }
    
    private CmsDiyPageDO validatePageExists(Long id) {
        CmsDiyPageDO page = diyPageMapper.selectById(id);
        if (page == null) {
            throw exception(CMS_DIY_PAGE_NOT_EXISTS);
        }
        return page;
    }
    
    private void validatePathUnique(Long id, String path) {
        CmsDiyPageDO existingPage = diyPageMapper.selectOne(CmsDiyPageDO::getPath, path);
        if (existingPage != null && !existingPage.getId().equals(id)) {
            throw exception(CMS_DIY_PAGE_PATH_DUPLICATE);
        }
    }
}
```

## Testing Strategy

### Unit Testing Approach

#### Service Layer Testing
```java
@ExtendWith(MockitoExtension.class)
class CmsDiyPageServiceImplTest {
    
    @Mock
    private CmsDiyPageMapper diyPageMapper;
    
    @Mock
    private CmsDiyPageVersionService diyPageVersionService;
    
    @Mock
    private CmsCacheService cacheService;
    
    @InjectMocks
    private CmsDiyPageServiceImpl diyPageService;
    
    @Test
    void testUpdatePage_Success() {
        // Given
        CmsDiyPageSaveReqVO updateReqVO = new CmsDiyPageSaveReqVO();
        updateReqVO.setId(1L);
        updateReqVO.setVersion(1);
        updateReqVO.setName("Updated Page");
        
        CmsDiyPageDO existPage = new CmsDiyPageDO();
        existPage.setId(1L);
        existPage.setVersion(1);
        
        when(diyPageMapper.selectById(1L)).thenReturn(existPage);
        when(diyPageMapper.updateByIdAndVersion(any(), eq(1))).thenReturn(1);
        
        // When
        diyPageService.updatePage(updateReqVO);
        
        // Then
        verify(diyPageMapper).updateByIdAndVersion(any(), eq(1));
        verify(cacheService).evictPageCache(1L);
    }
    
    @Test
    void testUpdatePage_VersionConflict() {
        // Given
        CmsDiyPageSaveReqVO updateReqVO = new CmsDiyPageSaveReqVO();
        updateReqVO.setId(1L);
        updateReqVO.setVersion(1);
        
        CmsDiyPageDO existPage = new CmsDiyPageDO();
        existPage.setId(1L);
        existPage.setVersion(2); // Different version
        
        when(diyPageMapper.selectById(1L)).thenReturn(existPage);
        
        // When & Then
        assertThrows(ServiceException.class, () -> diyPageService.updatePage(updateReqVO));
    }
}
```

#### Cache Testing
```java
@SpringBootTest
@TestPropertySource(properties = {
    "spring.cache.type=caffeine",
    "yudao.cms.cache.enabled=true"
})
class CmsCacheServiceTest {
    
    @Autowired
    private CmsCacheService cacheService;
    
    @Autowired
    private CacheManager cacheManager;
    
    @Test
    void testMenuCacheOperations() {
        Long tenantId = 1L;
        List<CmsMenuRespVO> menuTree = Arrays.asList(new CmsMenuRespVO());
        
        // Test cache put
        cacheService.cacheMenuTree(tenantId, menuTree);
        
        // Test cache hit
        Cache cache = cacheManager.getCache("cms:menu");
        assertNotNull(cache);
        assertNotNull(cache.get("tree:" + tenantId));
        
        // Test cache evict
        cacheService.evictMenuCache(tenantId);
        assertNull(cache.get("tree:" + tenantId));
    }
}
```

### Integration Testing

#### API Integration Tests
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Sql(scripts = "/sql/cms-test-data.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
@Sql(scripts = "/sql/clean.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
class CmsDiyPageControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void testCreateAndPublishPage() {
        // 1. Create page
        CmsPageCreateReqVO createReq = new CmsPageCreateReqVO();
        createReq.setName("Test Page");
        createReq.setMenuId(1L);
        createReq.setPath("test");
        createReq.setContent("{\"components\":[]}");
        
        ResponseEntity<CommonResult<Long>> createResponse = restTemplate.postForEntity(
            "/admin-api/cms/diy-page/create", createReq, 
            new ParameterizedTypeReference<CommonResult<Long>>() {});
        
        assertEquals(HttpStatus.OK, createResponse.getStatusCode());
        Long pageId = createResponse.getBody().getData();
        assertNotNull(pageId);
        
        // 2. Publish page
        CmsPagePublishReqVO publishReq = new CmsPagePublishReqVO();
        publishReq.setId(pageId);
        publishReq.setVersion(1);
        publishReq.setRemark("Initial publish");
        
        ResponseEntity<CommonResult<Boolean>> publishResponse = restTemplate.postForEntity(
            "/admin-api/cms/diy-page/publish", publishReq,
            new ParameterizedTypeReference<CommonResult<Boolean>>() {});
        
        assertEquals(HttpStatus.OK, publishResponse.getStatusCode());
        assertTrue(publishResponse.getBody().getData());
        
        // 3. Verify app-side access
        ResponseEntity<CommonResult<CmsDiyPageRespVO>> appResponse = restTemplate.getForEntity(
            "/app-api/cms/diy-page/get-by-path?path=/menu1/test",
            new ParameterizedTypeReference<CommonResult<CmsDiyPageRespVO>>() {});
        
        assertEquals(HttpStatus.OK, appResponse.getStatusCode());
        assertNotNull(appResponse.getBody().getData());
    }
}
```

### Performance Testing

#### Cache Performance Tests
```java
@SpringBootTest
@TestPropertySource(properties = {
    "yudao.cms.cache.caffeine.maximum-size=10000",
    "yudao.cms.cache.redis.menu-ttl=60"
})
class CachePerformanceTest {
    
    @Autowired
    private CmsMenuService menuService;
    
    @Test
    void testCachePerformance() {
        int iterations = 1000;
        Long tenantId = 1L;
        
        // Warm up cache
        menuService.getEnabledMenuTree();
        
        // Test cached performance
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            menuService.getEnabledMenuTree();
        }
        long cachedTime = System.currentTimeMillis() - startTime;
        
        // Clear cache and test uncached performance
        menuService.clearMenuCache(tenantId);
        
        startTime = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            menuService.clearMenuCache(tenantId); // Force DB query
            menuService.getEnabledMenuTree();
        }
        long uncachedTime = System.currentTimeMillis() - startTime;
        
        // Assert cache provides significant performance improvement
        assertTrue(cachedTime < uncachedTime / 10, 
                  "Cache should provide at least 10x performance improvement");
        
        log.info("Cache performance test - Cached: {}ms, Uncached: {}ms, Improvement: {}x",
                cachedTime, uncachedTime, uncachedTime / cachedTime);
    }
}
```

#### Concurrent Access Tests
```java
@SpringBootTest
class ConcurrentAccessTest {
    
    @Autowired
    private CmsDiyPageService pageService;
    
    @Test
    void testOptimisticLockingUnderConcurrency() throws InterruptedException {
        Long pageId = 1L;
        int threadCount = 10;
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger conflictCount = new AtomicInteger(0);
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    CmsDiyPageSaveReqVO updateReq = new CmsDiyPageSaveReqVO();
                    updateReq.setId(pageId);
                    updateReq.setVersion(1); // All threads use same version
                    updateReq.setName("Updated by thread " + threadIndex);
                    
                    pageService.updatePage(updateReq);
                    successCount.incrementAndGet();
                } catch (ServiceException e) {
                    if (e.getCode() == CMS_DIY_PAGE_VERSION_CONFLICT.getCode()) {
                        conflictCount.incrementAndGet();
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await(30, TimeUnit.SECONDS);
        executor.shutdown();
        
        // Only one thread should succeed, others should get version conflict
        assertEquals(1, successCount.get());
        assertEquals(threadCount - 1, conflictCount.get());
    }
}
```

This comprehensive design document provides the foundation for implementing the CMS Menu and DIY Page Management system with all the advanced features specified in the requirements, including optimistic locking, multi-level caching, version control, and robust error handling.