package cn.iocoder.yudao.module.cms.dal.dataobject.car.finance;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 首付选项 DO
 *
 * <AUTHOR>
 */
@TableName("cms_down_payment_options")
@KeySequence("cms_down_payment_options_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DownPaymentOptionDO extends BaseDO {

    /**
     * 首付选项ID
     */
    @TableId
    private Long id;

    /**
     * 融资选项ID
     */
    private Long optionId;

    /**
     * 首付代码
     */
    private String paymentCode;

    /**
     * 首付名称
     */
    private String name;

    /**
     * 首付比例值
     */
    private BigDecimal value;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 状态
     *
     * 枚举 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer status;

}