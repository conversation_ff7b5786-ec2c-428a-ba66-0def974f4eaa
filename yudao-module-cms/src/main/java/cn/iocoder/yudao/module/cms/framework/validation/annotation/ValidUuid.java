package cn.iocoder.yudao.module.cms.framework.validation.annotation;

import cn.iocoder.yudao.module.cms.framework.validation.validator.UuidValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * UUID 格式验证注解
 *
 * <AUTHOR>
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = UuidValidator.class)
public @interface ValidUuid {

    String message() default "UUID 格式不正确";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    /**
     * 是否允许为空
     */
    boolean allowEmpty() default false;
}