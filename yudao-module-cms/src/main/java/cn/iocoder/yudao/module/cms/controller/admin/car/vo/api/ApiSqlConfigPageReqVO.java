package cn.iocoder.yudao.module.cms.controller.admin.car.vo.api;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - API SQL配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ApiSqlConfigPageReqVO extends PageParam {

    @Schema(description = "API标识码", example = "car_models_list")
    private String apiCode;

    @Schema(description = "API版本", example = "v1.0")
    private String version;

    @Schema(description = "状态：0-启用，1-禁用", example = "0")
    private Integer status;

}