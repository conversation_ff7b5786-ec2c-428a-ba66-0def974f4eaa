package cn.iocoder.yudao.module.cms.framework.cache.config;

import cn.iocoder.yudao.module.cms.framework.cache.core.CacheSyncService;
import cn.iocoder.yudao.module.cms.framework.cache.core.CacheWarmupService;
import cn.iocoder.yudao.module.cms.framework.cache.core.MultiLevelCacheManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * CMS 缓存自动配置类
 *
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties(CmsCacheProperties.class)
@ConditionalOnProperty(prefix = "yudao.cms.cache", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableAsync
@Slf4j
public class CmsCacheAutoConfiguration {

    /**
     * 多级缓存管理器
     */
    @Bean
    public MultiLevelCacheManager multiLevelCacheManager(CmsCacheProperties cacheProperties,
                                                         RedisCacheManager redisCacheManager) {
        log.info("Initializing MultiLevelCacheManager with properties: {}", cacheProperties);
        return new MultiLevelCacheManager(cacheProperties, redisCacheManager);
    }

    /**
     * 缓存同步服务
     */
    @Bean
    @ConditionalOnProperty(prefix = "yudao.cms.cache", name = "enabled", havingValue = "true", matchIfMissing = true)
    public CacheSyncService cacheSyncService(RedisTemplate<String, Object> redisTemplate,
                                           RedisMessageListenerContainer messageListenerContainer,
                                           MultiLevelCacheManager cacheManager) {
        log.info("Initializing CacheSyncService");
        return new CacheSyncService(redisTemplate, messageListenerContainer, cacheManager);
    }

    /**
     * 缓存预热服务
     */
    @Bean
    public CacheWarmupService cacheWarmupService(CmsCacheProperties cacheProperties,
                                               MultiLevelCacheManager cacheManager) {
        log.info("Initializing CacheWarmupService");
        return new CacheWarmupService(cacheProperties, cacheManager);
    }

    /**
     * Redis 消息监听容器
     * 如果不存在则创建一个默认的
     */
    @Bean
    @ConditionalOnProperty(prefix = "yudao.cms.cache", name = "enabled", havingValue = "true", matchIfMissing = true)
    public RedisMessageListenerContainer redisMessageListenerContainer(RedisTemplate<String, Object> redisTemplate) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        if (redisTemplate.getConnectionFactory() != null) {
            container.setConnectionFactory(redisTemplate.getConnectionFactory());
        }
        
        // 设置任务执行器
        container.setTaskExecutor(Runnable::run);
        
        log.info("Initialized RedisMessageListenerContainer for cache sync");
        return container;
    }
}