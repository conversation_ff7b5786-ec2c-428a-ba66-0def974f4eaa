package cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.DecimalMax;
import java.math.BigDecimal;

/**
 * 首付选项 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class DownPaymentOptionBaseVO {

    @Schema(description = "融资选项ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "融资选项ID不能为空")
    private Long optionId;

    @Schema(description = "首付代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "down_20")
    @NotNull(message = "首付代码不能为空")
    @Length(max = 50, message = "首付代码长度不能超过50个字符")
    private String paymentCode;

    @Schema(description = "首付名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "20%首付")
    @NotNull(message = "首付名称不能为空")
    @Length(max = 100, message = "首付名称长度不能超过100个字符")
    private String name;

    @Schema(description = "首付比例值", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.20")
    @NotNull(message = "首付比例值不能为空")
    @DecimalMin(value = "0", message = "首付比例值不能小于0")
    @DecimalMax(value = "1", message = "首付比例值不能大于1")
    private BigDecimal value;

    @Schema(description = "排序", example = "1")
    private Integer sortOrder;

    @Schema(description = "状态：0-禁用，1-启用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

}