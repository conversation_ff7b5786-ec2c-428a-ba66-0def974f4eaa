package cn.iocoder.yudao.module.cms.dal.dataobject.car.finance;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 融资期限 DO
 *
 * <AUTHOR>
 */
@TableName("cms_finance_terms")
@KeySequence("cms_finance_terms_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FinanceTermDO extends BaseDO {

    /**
     * 期限ID
     */
    @TableId
    private Long id;

    /**
     * 融资选项ID
     */
    private Long optionId;

    /**
     * 期限代码
     */
    private String termCode;

    /**
     * 期限名称
     */
    private String name;

    /**
     * 利率
     */
    private BigDecimal rate;

    /**
     * 期限月数
     */
    private Integer months;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 状态
     *
     * 枚举 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer status;

}