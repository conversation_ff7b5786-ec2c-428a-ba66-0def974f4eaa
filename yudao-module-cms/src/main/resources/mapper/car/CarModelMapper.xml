<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.cms.dal.mysql.car.CarModelMapper">

    <!-- 车型详情结果映射 -->
    <resultMap id="CarModelDetailResult" type="cn.iocoder.yudao.module.cms.dal.dataobject.car.CarModelDO">
        <id column="id" property="id"/>
        <result column="series_id" property="seriesId"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="name_en" property="nameEn"/>
        <result column="description" property="description"/>
        <result column="category" property="category"/>
        <result column="base_price" property="basePrice"/>
        <result column="image_url" property="imageUrl"/>
        <result column="ev_icon_url" property="evIconUrl"/>
        <result column="poster_url" property="posterUrl"/>
        <result column="features" property="features" typeHandler="cn.iocoder.yudao.framework.mybatis.core.type.JsonStringListTypeHandler"/>
        <result column="badge" property="badge"/>
        <result column="end_date" property="endDate"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="status" property="status"/>
    </resultMap>

    <!-- 查询车型列表（包含车系信息） -->
    <select id="selectModelListWithSeries" resultMap="CarModelDetailResult">
        SELECT 
            m.*,
            s.name as series_name,
            s.name_en as series_name_en
        FROM cms_car_models m
        INNER JOIN cms_car_series s ON m.series_id = s.id
        WHERE m.deleted = 0 AND s.deleted = 0
        <if test="seriesCode != null and seriesCode != ''">
            AND s.code = #{seriesCode}
        </if>
        <if test="status != null">
            AND m.status = #{status}
        </if>
        ORDER BY m.sort_order, m.id DESC
    </select>

    <!-- 按价格范围查询车型 -->
    <select id="selectModelsByPriceRange" resultMap="CarModelDetailResult">
        SELECT 
            m.*,
            s.name as series_name
        FROM cms_car_models m
        INNER JOIN cms_car_series s ON m.series_id = s.id
        WHERE m.deleted = 0 AND s.deleted = 0
        <if test="minPrice != null">
            AND m.base_price >= #{minPrice}
        </if>
        <if test="maxPrice != null">
            AND m.base_price &lt;= #{maxPrice}
        </if>
        <if test="category != null and category != ''">
            AND m.category = #{category}
        </if>
        ORDER BY m.base_price, m.sort_order
    </select>

    <!-- 查询优惠车型（有截止日期） -->
    <select id="selectPromotionModels" resultMap="CarModelDetailResult">
        SELECT 
            m.*,
            s.name as series_name,
            DATEDIFF(m.end_date, CURDATE()) as days_remaining
        FROM cms_car_models m
        INNER JOIN cms_car_series s ON m.series_id = s.id
        WHERE m.deleted = 0 
            AND s.deleted = 0
            AND m.status = 0
            AND m.end_date IS NOT NULL
            AND m.end_date >= CURDATE()
        ORDER BY m.end_date, m.sort_order
    </select>

</mapper>