-- CMS菜单表
CREATE TABLE `cms_menu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `name` varchar(50) NOT NULL COMMENT '菜单名称',
  `icon` varchar(100) DEFAULT NULL COMMENT '菜单图标',
  `path` varchar(200) NOT NULL COMMENT '菜单路径',
  `parent_id` bigint DEFAULT 0 COMMENT '上级菜单ID，0表示根菜单',
  `sort` int DEFAULT 0 COMMENT '显示顺序',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态：0-启用，1-禁用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_path` (`path`, `deleted`, `tenant_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CMS菜单表';

-- CMS DIY页面表
CREATE TABLE `cms_diy_page` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '页面ID',
  `uuid` varchar(36) NOT NULL COMMENT '页面UUID，全局唯一',
  `name` varchar(100) NOT NULL COMMENT '页面名称',
  `parent_id` bigint DEFAULT NULL COMMENT '上级页面ID',
  `menu_id` bigint NOT NULL COMMENT '关联菜单ID',
  `path` varchar(500) NOT NULL COMMENT '页面路径，唯一',
  `keywords` varchar(200) DEFAULT NULL COMMENT '关键词',
  `description` text COMMENT '页面描述',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态：0-草稿，1-已发布，2-已下线',
  `version` int NOT NULL DEFAULT 1 COMMENT '当前版本号，用于乐观锁',
  `published_version` int DEFAULT NULL COMMENT '已发布的版本号',
  `content` longtext COMMENT '页面内容JSON',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_uuid` (`uuid`, `deleted`, `tenant_id`),
  UNIQUE KEY `uk_path` (`path`, `deleted`, `tenant_id`),
  KEY `idx_menu_id` (`menu_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_status` (`status`),
  KEY `idx_published_version` (`published_version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CMS DIY页面表';

-- CMS DIY页面版本表
CREATE TABLE `cms_diy_page_version` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '版本ID',
  `page_id` bigint NOT NULL COMMENT '页面ID',
  `version` int NOT NULL COMMENT '版本号',
  `name` varchar(100) NOT NULL COMMENT '版本名称',
  `content` longtext COMMENT '页面内容JSON',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `is_published` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已发布',
  `remark` varchar(500) DEFAULT NULL COMMENT '版本备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_page_version` (`page_id`, `version`, `deleted`, `tenant_id`),
  KEY `idx_page_id` (`page_id`),
  KEY `idx_publish_time` (`publish_time`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_is_published` (`is_published`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CMS DIY页面版本表';

-- CMS页面访问统计表
CREATE TABLE `cms_page_visit_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '访问记录ID',
  `page_id` bigint NOT NULL COMMENT '页面ID',
  `page_uuid` varchar(36) NOT NULL COMMENT '页面UUID',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID（登录用户）',
  `session_id` varchar(64) DEFAULT NULL COMMENT '会话ID',
  `ip` varchar(45) DEFAULT NULL COMMENT '访问IP',
  `user_agent` text COMMENT '用户代理',
  `referer` varchar(500) DEFAULT NULL COMMENT '来源页面',
  `visit_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
  `stay_time` int DEFAULT 0 COMMENT '停留时间（秒）',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_page_id` (`page_id`),
  KEY `idx_page_uuid` (`page_uuid`),
  KEY `idx_visit_time` (`visit_time`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_id` (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CMS页面访问统计表';

-- CMS页面访问统计汇总表
CREATE TABLE `cms_page_visit_stats` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `page_id` bigint NOT NULL COMMENT '页面ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `total_visits` int NOT NULL DEFAULT 0 COMMENT '总访问次数',
  `avg_stay_time` int NOT NULL DEFAULT 0 COMMENT '平均停留时间（秒）',
  `bounce_count` int NOT NULL DEFAULT 0 COMMENT '跳出次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_page_date` (`page_id`, `stat_date`, `tenant_id`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_page_id` (`page_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CMS页面访问统计汇总表';