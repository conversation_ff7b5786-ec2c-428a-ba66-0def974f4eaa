package cn.iocoder.yudao.module.cms.controller.admin.car.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 配置选项类型分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CarOptionTypePageReqVO extends PageParam {

    @Schema(description = "选项类型名称", example = "外观颜色")
    private String name;

    @Schema(description = "状态：0-禁用，1-启用", example = "1")
    private Integer status;

}