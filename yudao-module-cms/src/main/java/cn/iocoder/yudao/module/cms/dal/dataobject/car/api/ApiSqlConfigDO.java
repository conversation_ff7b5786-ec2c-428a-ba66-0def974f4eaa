package cn.iocoder.yudao.module.cms.dal.dataobject.car.api;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * API SQL配置 DO
 *
 * <AUTHOR>
 */
@TableName("cms_api_sql_configs")
@KeySequence("cms_api_sql_configs_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiSqlConfigDO extends BaseDO {

    /**
     * 配置ID
     */
    @TableId
    private Long id;

    /**
     * API标识码
     */
    private String apiCode;

    /**
     * API版本，default为默认版本
     */
    private String version;

    /**
     * SQL查询语句
     */
    private String sqlContent;

    /**
     * 响应处理器类名
     */
    private String responseProcessor;

    /**
     * 配置描述
     */
    private String description;

    /**
     * 是否默认版本
     */
    private Boolean isDefault;

    /**
     * 状态
     *
     * 枚举 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer status;

}