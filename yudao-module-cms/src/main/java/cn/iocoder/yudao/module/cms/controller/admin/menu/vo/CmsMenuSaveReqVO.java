package cn.iocoder.yudao.module.cms.controller.admin.menu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;


@Schema(description = "管理后台 - CMS菜单新增/修改 Request VO")
@Data
public class CmsMenuSaveReqVO {

    @Schema(description = "菜单ID", example = "1")
    private Long id;

    @Schema(description = "菜单名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "首页")
    @NotBlank(message = "菜单名称不能为空")
    @Size(max = 50, message = "菜单名称长度不能超过50个字符")
    private String name;

    @Schema(description = "菜单图标", example = "home")
    @Size(max = 100, message = "菜单图标长度不能超过100个字符")
    private String icon;

    @Schema(description = "菜单路径", requiredMode = Schema.RequiredMode.REQUIRED, example = "home")
    @NotBlank(message = "菜单路径不能为空")
    @Size(max = 200, message = "菜单路径长度不能超过200个字符")
    private String path;

    @Schema(description = "上级菜单ID", example = "0")
    private Long parentId;

    @Schema(description = "显示顺序", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "显示顺序不能为空")
    private Integer sort;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "状态不能为空")
    private Integer status;

}