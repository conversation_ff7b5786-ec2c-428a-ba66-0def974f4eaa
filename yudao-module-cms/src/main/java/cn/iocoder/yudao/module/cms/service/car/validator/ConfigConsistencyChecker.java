package cn.iocoder.yudao.module.cms.service.car.validator;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarModelDO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarModelOptionDO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarOptionTypeDO;
import cn.iocoder.yudao.module.cms.service.car.CarModelOptionService;
import cn.iocoder.yudao.module.cms.service.car.CarModelService;
import cn.iocoder.yudao.module.cms.service.car.CarOptionTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 配置一致性检查器
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class ConfigConsistencyChecker {

    @Resource
    private CarModelService carModelService;

    @Resource
    private CarOptionTypeService carOptionTypeService;

    @Resource
    private CarModelOptionService carModelOptionService;

    @Resource
    private DynamicConfigValidator dynamicConfigValidator;

    /**
     * 检查车型配置的一致性
     *
     * @param modelId 车型ID
     * @return 检查结果
     */
    public ConsistencyCheckResult checkModelConfigConsistency(Long modelId) {
        ConsistencyCheckResult result = new ConsistencyCheckResult(modelId);
        
        try {
            // 检查车型是否存在
            CarModelDO carModel = carModelService.getCarModel(modelId);
            if (carModel == null) {
                result.addError("车型不存在: " + modelId);
                return result;
            }

            // 获取车型的所有配置选项
            List<CarModelOptionDO> options = carModelOptionService.getCarModelOptionListByModelId(modelId);
            
            // 检查每个配置选项
            for (CarModelOptionDO option : options) {
                checkOptionConsistency(option, result);
            }

            // 检查配置选项间的依赖关系
            checkOptionDependencies(options, result);

            // 检查配置包的一致性
            checkPackageConsistency(modelId, options, result);

        } catch (Exception e) {
            log.error("检查车型配置一致性时发生异常", e);
            result.addError("检查失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 检查单个配置选项的一致性
     */
    private void checkOptionConsistency(CarModelOptionDO option, ConsistencyCheckResult result) {
        // 检查选项类型是否存在
        CarOptionTypeDO optionType = carOptionTypeService.getCarOptionType(option.getOptionTypeId());
        if (optionType == null) {
            result.addError("选项 " + option.getName() + " 的类型不存在");
            return;
        }

        // 检查配置数据是否符合Schema
        if (option.getConfigData() != null && !option.getConfigData().isEmpty()) {
            DynamicConfigValidator.ValidationResult validationResult = 
                dynamicConfigValidator.validateConfigData(optionType.getTypeCode(), option.getConfigData());
            
            if (!validationResult.isSuccess()) {
                result.addWarning("选项 " + option.getName() + " 的配置数据验证失败: " + validationResult.getMessage());
            }
        }

        // 检查必填字段完整性
        if (option.getConfigData() != null) {
            DynamicConfigValidator.ValidationResult completenessResult = 
                dynamicConfigValidator.validateFieldCompleteness(optionType.getTypeCode(), option.getConfigData());
            
            if (!completenessResult.isSuccess()) {
                result.addWarning("选项 " + option.getName() + " 字段不完整: " + completenessResult.getMessage());
            }
        }

        // 检查数据类型一致性
        if (option.getConfigData() != null) {
            DynamicConfigValidator.ValidationResult typeResult = 
                dynamicConfigValidator.validateDataTypeConsistency(optionType.getTypeCode(), option.getConfigData());
            
            if (!typeResult.isSuccess()) {
                result.addError("选项 " + option.getName() + " 数据类型不一致: " + typeResult.getMessage());
            }
        }
    }

    /**
     * 检查配置选项间的依赖关系
     */
    private void checkOptionDependencies(List<CarModelOptionDO> options, ConsistencyCheckResult result) {
        // 按选项类型分组
        Map<Long, List<CarModelOptionDO>> optionsByType = options.stream()
                .collect(Collectors.groupingBy(CarModelOptionDO::getOptionTypeId));

        // 检查每个类型是否有重复的选项代码
        for (Map.Entry<Long, List<CarModelOptionDO>> entry : optionsByType.entrySet()) {
            List<CarModelOptionDO> typeOptions = entry.getValue();
            Set<String> optionCodes = new HashSet<>();
            
            for (CarModelOptionDO option : typeOptions) {
                if (optionCodes.contains(option.getOptionCode())) {
                    result.addError("选项代码重复: " + option.getOptionCode());
                } else {
                    optionCodes.add(option.getOptionCode());
                }
            }
        }

        // 检查配置包依赖
        checkPackageDependencies(options, result);
    }

    /**
     * 检查配置包依赖
     */
    private void checkPackageDependencies(List<CarModelOptionDO> options, ConsistencyCheckResult result) {
        // 收集所有需要的配置包
        Set<String> requiredPackages = options.stream()
                .filter(option -> option.getRequiredPackage() != null && !option.getRequiredPackage().trim().isEmpty())
                .map(CarModelOptionDO::getRequiredPackage)
                .collect(Collectors.toSet());

        // 这里可以添加更复杂的配置包依赖检查逻辑
        // 例如检查配置包是否存在、是否冲突等
        for (String packageCode : requiredPackages) {
            if (packageCode.trim().isEmpty()) {
                result.addWarning("存在空的配置包依赖");
            }
        }
    }

    /**
     * 检查配置包的一致性
     */
    private void checkPackageConsistency(Long modelId, List<CarModelOptionDO> options, ConsistencyCheckResult result) {
        // 检查配置包的一致性
        // 这里可以添加更多的配置包一致性检查逻辑
        Map<String, List<CarModelOptionDO>> packageOptions = options.stream()
                .filter(option -> option.getRequiredPackage() != null && !option.getRequiredPackage().trim().isEmpty())
                .collect(Collectors.groupingBy(CarModelOptionDO::getRequiredPackage));

        for (Map.Entry<String, List<CarModelOptionDO>> entry : packageOptions.entrySet()) {
            String packageCode = entry.getKey();
            List<CarModelOptionDO> packageOptionList = entry.getValue();
            
            if (packageOptionList.size() < 2) {
                result.addInfo("配置包 " + packageCode + " 只有一个选项，可能不需要配置包");
            }
        }
    }

    /**
     * 批量检查多个车型的配置一致性
     *
     * @param modelIds 车型ID列表
     * @return 检查结果列表
     */
    public List<ConsistencyCheckResult> batchCheckModelConfigConsistency(List<Long> modelIds) {
        List<ConsistencyCheckResult> results = new ArrayList<>();
        
        for (Long modelId : modelIds) {
            ConsistencyCheckResult result = checkModelConfigConsistency(modelId);
            results.add(result);
        }
        
        return results;
    }

    /**
     * 检查所有活跃车型的配置一致性
     *
     * @return 检查结果列表
     */
    public List<ConsistencyCheckResult> checkAllActiveModelsConsistency() {
        List<CarModelDO> activeModels = carModelService.getCarModelList(null, CommonStatusEnum.ENABLE.getStatus()); // 获取所有启用的车型
        List<Long> modelIds = activeModels.stream().map(CarModelDO::getId).collect(Collectors.toList());
        return batchCheckModelConfigConsistency(modelIds);
    }

    /**
     * 一致性检查结果
     */
    public static class ConsistencyCheckResult {
        private Long modelId;
        private List<String> errors;
        private List<String> warnings;
        private List<String> infos;
        private boolean hasErrors;
        private boolean hasWarnings;

        public ConsistencyCheckResult(Long modelId) {
            this.modelId = modelId;
            this.errors = new ArrayList<>();
            this.warnings = new ArrayList<>();
            this.infos = new ArrayList<>();
            this.hasErrors = false;
            this.hasWarnings = false;
        }

        public void addError(String error) {
            errors.add(error);
            hasErrors = true;
        }

        public void addWarning(String warning) {
            warnings.add(warning);
            hasWarnings = true;
        }

        public void addInfo(String info) {
            infos.add(info);
        }

        public Long getModelId() {
            return modelId;
        }

        public List<String> getErrors() {
            return errors;
        }

        public List<String> getWarnings() {
            return warnings;
        }

        public List<String> getInfos() {
            return infos;
        }

        public boolean hasErrors() {
            return hasErrors;
        }

        public boolean hasWarnings() {
            return hasWarnings;
        }

        public boolean isValid() {
            return !hasErrors;
        }

        public String getSummary() {
            if (hasErrors) {
                return "检查失败，存在 " + errors.size() + " 个错误";
            } else if (hasWarnings) {
                return "检查通过，存在 " + warnings.size() + " 个警告";
            } else {
                return "检查通过，配置一致";
            }
        }
    }

}