<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.cms.dal.mysql.statistics.CmsPageVisitLogMapper">

    <!-- 统计指定日期的页面访问数据 -->
    <select id="selectDailyStats" resultType="cn.iocoder.yudao.module.cms.dal.dataobject.statistics.CmsPageVisitStatsDO">
        SELECT
            page_id,
            COUNT(*) as totalVisits,
            COUNT(DISTINCT COALESCE(user_id, session_id, ip)) as uniqueVisitors,
            COALESCE(AVG(stay_time), 0) as avgStayTime,
            COUNT(CASE WHEN stay_time &lt;= 10 THEN 1 END) as bounceCount
        FROM cms_page_visit_log
        WHERE DATE(visit_time) = #{statDate} AND deleted = 0
        GROUP BY page_id
    </select>

    <!-- 统计指定页面和日期的访问数据 -->
    <select id="selectPageDailyStats" resultType="cn.iocoder.yudao.module.cms.dal.dataobject.statistics.CmsPageVisitStatsDO">
        SELECT
            #{pageId} as pageId,
            COUNT(*) as totalVisits,
            COUNT(DISTINCT COALESCE(user_id, session_id, ip)) as uniqueVisitors,
            COALESCE(AVG(stay_time), 0) as avgStayTime,
            COUNT(CASE WHEN stay_time &lt;= 10 THEN 1 END) as bounceCount
        FROM cms_page_visit_log
        WHERE page_id = #{pageId} AND DATE(visit_time) = #{statDate} AND deleted = 0
    </select>

    <!-- 统计热门页面 -->
    <select id="selectHotPages" resultType="java.util.Map">
        SELECT page_id, COUNT(*) as visit_count
        FROM cms_page_visit_log
        WHERE visit_time &gt;= #{startTime} AND deleted = 0
        GROUP BY page_id
        ORDER BY visit_count DESC
        LIMIT #{limit}
    </select>

    <!-- 批量插入访问日志 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO cms_page_visit_log (
            page_id, page_uuid, user_id, session_id, ip, user_agent, referer, visit_time, stay_time,
            creator, create_time, updater, update_time, deleted, tenant_id
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.pageId}, #{item.pageUuid}, #{item.userId}, #{item.sessionId}, #{item.ip},
                #{item.userAgent}, #{item.referer}, #{item.visitTime}, #{item.stayTime},
                #{item.creator}, #{item.createTime}, #{item.updater}, #{item.updateTime},
                #{item.deleted}, #{item.tenantId}
            )
        </foreach>
    </insert>

    <!-- 聚合指定日期的访问统计数据 -->
    <select id="aggregateDailyStats" resultType="java.util.Map">
        SELECT 
            page_id,
            COUNT(*) as total_visits,
            COUNT(DISTINCT COALESCE(user_id, session_id, ip)) as unique_visitors,
            COALESCE(AVG(stay_time), 0) as avg_stay_time,
            COUNT(CASE WHEN stay_time &lt;= 10 THEN 1 END) as bounce_count
        FROM cms_page_visit_log 
        WHERE DATE(visit_time) = #{statDate} AND deleted = 0
        GROUP BY page_id
    </select>

    <!-- 删除指定时间之前的访问日志 -->
    <update id="deleteByVisitTimeBefore">
        UPDATE cms_page_visit_log 
        SET deleted = 1, update_time = NOW()
        WHERE visit_time &lt; #{beforeTime} AND deleted = 0
    </update>

</mapper>