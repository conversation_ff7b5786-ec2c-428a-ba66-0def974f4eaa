package cn.iocoder.yudao.module.cms.dal.dataobject.car.finance;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 车型融资方案 DO
 *
 * <AUTHOR>
 */
@TableName("cms_car_model_finance_plans")
@KeySequence("cms_car_model_finance_plans_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CarModelFinancePlanDO extends BaseDO {

    /**
     * 方案ID
     */
    @TableId
    private Long id;

    /**
     * 车型ID
     */
    private Long modelId;

    /**
     * 融资选项ID
     */
    private Long financeOptionId;

    /**
     * 期限ID
     */
    private Long termId;

    /**
     * 首付选项ID
     */
    private Long downPaymentId;

    /**
     * 每周付款金额
     */
    private BigDecimal weeklyPayment;

    /**
     * 每月付款金额
     */
    private BigDecimal monthlyPayment;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 保证未来价值
     */
    private BigDecimal gfv;

    /**
     * 公里数限制
     */
    private Integer kmAllowance;

    /**
     * 是否默认方案
     */
    private Boolean isDefault;

    /**
     * 是否推荐方案
     */
    private Boolean isFeatured;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 状态
     *
     * 枚举 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer status;

}