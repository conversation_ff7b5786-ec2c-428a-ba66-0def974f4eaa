package cn.iocoder.yudao.module.cms.controller.app.car;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.finance.CarModelFinancePlanRespVO;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.finance.CarModelFinancePlanDO;
import cn.iocoder.yudao.module.cms.service.car.finance.CarModelFinancePlanService;
import cn.iocoder.yudao.module.cms.service.car.finance.CarModelFinancePlanService.FinancePlanDetails;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.PermitAll;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * App端车型融资方案API控制器
 * 
 * <AUTHOR>
 */
@Tag(name = "App API - 车型融资方案")
@RestController
@RequestMapping("/app/cms/finance-plan")
@Slf4j
public class AppCarFinancePlanController {

    @Resource
    private CarModelFinancePlanService carModelFinancePlanService;

    @GetMapping("/model/{modelId}")
    @Operation(summary = "获取车型的所有融资方案", description = "返回指定车型的所有可用融资方案列表")
    @PermitAll
    public CommonResult<List<CarModelFinancePlanRespVO>> getFinancePlansByModelId(
            @Parameter(description = "车型ID", required = true)
            @PathVariable Long modelId) {
        
        log.info("获取车型融资方案列表: modelId={}", modelId);
        
        List<CarModelFinancePlanDO> list = carModelFinancePlanService.getCarModelFinancePlanListByModelId(modelId);
        // 过滤出状态为启用的方案
        List<CarModelFinancePlanDO> activePlans = list.stream()
                .filter(plan -> plan.getStatus() != null && plan.getStatus() == 0)
                .collect(Collectors.toList());
        
        return success(BeanUtils.toBean(activePlans, CarModelFinancePlanRespVO.class));
    }

    @GetMapping("/model/{modelId}/default")
    @Operation(summary = "获取车型的默认融资方案", description = "返回指定车型的默认融资方案")
    @PermitAll
    public CommonResult<CarModelFinancePlanRespVO> getDefaultFinancePlan(
            @Parameter(description = "车型ID", required = true)
            @PathVariable Long modelId) {
        
        log.info("获取车型默认融资方案: modelId={}", modelId);
        
        CarModelFinancePlanDO financePlan = carModelFinancePlanService.getDefaultFinancePlan(modelId);
        return success(BeanUtils.toBean(financePlan, CarModelFinancePlanRespVO.class));
    }

    @GetMapping("/model/{modelId}/featured")
    @Operation(summary = "获取车型的推荐融资方案", description = "返回指定车型的推荐融资方案列表")
    @PermitAll
    public CommonResult<List<CarModelFinancePlanRespVO>> getFeaturedFinancePlans(
            @Parameter(description = "车型ID", required = true)
            @PathVariable Long modelId) {
        
        log.info("获取车型推荐融资方案: modelId={}", modelId);
        
        List<CarModelFinancePlanDO> list = carModelFinancePlanService.getCarModelFinancePlanListByModelId(modelId);
        // 过滤出推荐的方案
        List<CarModelFinancePlanDO> featuredPlans = list.stream()
                .filter(plan -> Boolean.TRUE.equals(plan.getIsFeatured()) && 
                               plan.getStatus() != null && plan.getStatus() == 0)
                .collect(Collectors.toList());
        
        return success(BeanUtils.toBean(featuredPlans, CarModelFinancePlanRespVO.class));
    }

    @GetMapping("/detail/{id}")
    @Operation(summary = "获取融资方案详情", description = "返回指定融资方案的详细信息")
    @PermitAll
    public CommonResult<CarModelFinancePlanRespVO> getFinancePlanDetail(
            @Parameter(description = "融资方案ID", required = true)
            @PathVariable Long id) {
        
        log.info("获取融资方案详情: id={}", id);
        
        CarModelFinancePlanDO financePlan = carModelFinancePlanService.getCarModelFinancePlan(id);
        // 检查状态是否为启用
        if (financePlan == null || financePlan.getStatus() == null || financePlan.getStatus() != 0) {
            return success(null);
        }
        
        return success(BeanUtils.toBean(financePlan, CarModelFinancePlanRespVO.class));
    }

    @GetMapping("/calculate")
    @Operation(summary = "计算融资方案", description = "根据参数动态计算融资方案的月供、总额等信息")
    @PermitAll
    public CommonResult<Map<String, Object>> calculateFinancePlan(
            @Parameter(description = "车辆价格", required = true, example = "200000")
            @RequestParam BigDecimal carPrice,
            @Parameter(description = "首付比例(0-1)", required = true, example = "0.3")
            @RequestParam BigDecimal downPaymentRatio,
            @Parameter(description = "年利率(%)", required = true, example = "4.9")
            @RequestParam BigDecimal interestRate,
            @Parameter(description = "贷款期限(月)", required = true, example = "36")
            @RequestParam Integer termMonths,
            @Parameter(description = "保证未来价值", example = "50000")
            @RequestParam(required = false) BigDecimal gfv) {
        
        log.info("计算融资方案: carPrice={}, downPaymentRatio={}, interestRate={}, termMonths={}, gfv={}", 
                carPrice, downPaymentRatio, interestRate, termMonths, gfv);
        
        // 参数校验
        if (carPrice.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("车辆价格必须大于0");
        }
        if (downPaymentRatio.compareTo(BigDecimal.ZERO) < 0 || downPaymentRatio.compareTo(BigDecimal.ONE) > 0) {
            throw new IllegalArgumentException("首付比例必须在0-1之间");
        }
        if (interestRate.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("利率不能为负数");
        }
        if (termMonths <= 0) {
            throw new IllegalArgumentException("贷款期限必须大于0");
        }
        
        FinancePlanDetails details = carModelFinancePlanService.calculateFinancePlan(
                carPrice, downPaymentRatio, interestRate, termMonths, gfv);
        
        // 构建响应
        Map<String, Object> result = new HashMap<>();
        result.put("carPrice", carPrice);
        result.put("downPaymentRatio", downPaymentRatio);
        result.put("downPaymentAmount", details.getDownPaymentAmount());
        result.put("financeAmount", details.getFinanceAmount());
        result.put("interestRate", interestRate);
        result.put("termMonths", termMonths);
        result.put("monthlyPayment", details.getMonthlyPayment());
        result.put("weeklyPayment", details.getWeeklyPayment());
        result.put("totalAmount", details.getTotalAmount());
        if (gfv != null) {
            result.put("gfv", gfv);
        }
        
        return success(result);
    }

    @GetMapping("/model/{modelId}/summary")
    @Operation(summary = "获取车型融资方案摘要", description = "返回车型的融资方案摘要信息，包括最低月供等")
    @PermitAll
    public CommonResult<Map<String, Object>> getFinancePlanSummary(
            @Parameter(description = "车型ID", required = true)
            @PathVariable Long modelId) {
        
        log.info("获取车型融资方案摘要: modelId={}", modelId);
        
        List<CarModelFinancePlanDO> list = carModelFinancePlanService.getCarModelFinancePlanListByModelId(modelId);
        
        // 过滤出启用的方案
        List<CarModelFinancePlanDO> activePlans = list.stream()
                .filter(plan -> plan.getStatus() != null && plan.getStatus() == 0)
                .collect(Collectors.toList());
        
        Map<String, Object> summary = new HashMap<>();
        summary.put("totalPlans", activePlans.size());
        
        if (!activePlans.isEmpty()) {
            // 找出最低月供
            BigDecimal minMonthlyPayment = activePlans.stream()
                    .map(CarModelFinancePlanDO::getMonthlyPayment)
                    .filter(payment -> payment != null)
                    .min(BigDecimal::compareTo)
                    .orElse(null);
            
            // 找出最低周供
            BigDecimal minWeeklyPayment = activePlans.stream()
                    .map(CarModelFinancePlanDO::getWeeklyPayment)
                    .filter(payment -> payment != null)
                    .min(BigDecimal::compareTo)
                    .orElse(null);
            
            // 找出默认方案
            CarModelFinancePlanDO defaultPlan = activePlans.stream()
                    .filter(plan -> Boolean.TRUE.equals(plan.getIsDefault()))
                    .findFirst()
                    .orElse(null);
            
            // 找出推荐方案数量
            long featuredCount = activePlans.stream()
                    .filter(plan -> Boolean.TRUE.equals(plan.getIsFeatured()))
                    .count();
            
            summary.put("minMonthlyPayment", minMonthlyPayment);
            summary.put("minWeeklyPayment", minWeeklyPayment);
            summary.put("hasDefaultPlan", defaultPlan != null);
            summary.put("featuredPlansCount", featuredCount);
            
            if (defaultPlan != null) {
                summary.put("defaultPlanId", defaultPlan.getId());
                summary.put("defaultMonthlyPayment", defaultPlan.getMonthlyPayment());
                summary.put("defaultWeeklyPayment", defaultPlan.getWeeklyPayment());
            }
        }
        
        return success(summary);
    }
}