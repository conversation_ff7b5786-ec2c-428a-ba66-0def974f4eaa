package cn.iocoder.yudao.module.cms.framework.cache.core;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.cms.framework.cache.config.CmsCacheProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Supplier;

/**
 * CMS 缓存服务
 * 
 * 提供统一的缓存操作接口，封装多级缓存的复杂性
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CmsCacheService {

    private final MultiLevelCacheManager cacheManager;
    private final CacheSyncService cacheSyncService;
    private final CmsCacheProperties cacheProperties;

    // 缓存名称常量
    public static final String CACHE_MENU = "cms:menu";
    public static final String CACHE_PAGE = "cms:page";
    public static final String CACHE_PAGE_PATH = "cms:page:path";
    public static final String CACHE_PAGE_VERSION = "cms:page:version";
    public static final String CACHE_STATS = "cms:stats";

    public CmsCacheService(MultiLevelCacheManager cacheManager,
                          CacheSyncService cacheSyncService,
                          CmsCacheProperties cacheProperties) {
        this.cacheManager = cacheManager;
        this.cacheSyncService = cacheSyncService;
        this.cacheProperties = cacheProperties;
    }

    /**
     * 获取缓存值，如果不存在则通过 supplier 加载
     */
    public <T> T get(String cacheName, Object key, Class<T> type, Supplier<T> supplier) {
        if (!cacheProperties.getEnabled()) {
            return supplier.get();
        }

        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache == null) {
                log.warn("Cache not found: {}", cacheName);
                return supplier.get();
            }

            String tenantKey = buildTenantKey(key);
            T value = cache.get(tenantKey, type);
            
            if (value == null) {
                // 缓存未命中，加载数据
                value = supplier.get();
                if (value != null) {
                    try {
                        put(cacheName, key, value);
                    } catch (Exception putException) {
                        // 如果缓存失败（如序列化问题），记录警告但不影响功能
                        log.warn("Failed to cache value for key: {}, will continue without caching", key, putException);
                    }
                }
            }
            
            return value;
        } catch (Exception e) {
            log.error("Failed to get from cache: cacheName={}, key={}", cacheName, key, e);
            return supplier.get();
        }
    }

    /**
     * 放入缓存
     */
    public void put(String cacheName, Object key, Object value) {
        if (!cacheProperties.getEnabled() || value == null) {
            return;
        }

        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                String tenantKey = buildTenantKey(key);
                cache.put(tenantKey, value);
                
                // 发布同步消息
                cacheSyncService.publishCacheUpdate(cacheName, tenantKey, value);
                
                log.debug("Put to cache: cacheName={}, key={}", cacheName, tenantKey);
            }
        } catch (Exception e) {
            // 区分序列化错误和其他错误
            if (e.getMessage() != null && e.getMessage().contains("Failed to serialize")) {
                log.warn("Serialization failed for cache: cacheName={}, key={}, skipping cache", cacheName, key);
            } else {
                log.error("Failed to put to cache: cacheName={}, key={}", cacheName, key, e);
            }
        }
    }

    /**
     * 驱逐缓存
     */
    public void evict(String cacheName, Object key) {
        if (!cacheProperties.getEnabled()) {
            return;
        }

        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                String tenantKey = buildTenantKey(key);
                cache.evict(tenantKey);
                
                // 发布同步消息
                cacheSyncService.publishCacheEvict(cacheName, tenantKey);
                
                log.debug("Evicted from cache: cacheName={}, key={}", cacheName, tenantKey);
            }
        } catch (Exception e) {
            log.error("Failed to evict from cache: cacheName={}, key={}", cacheName, key, e);
        }
    }

    /**
     * 清除整个缓存
     */
    public void clear(String cacheName) {
        if (!cacheProperties.getEnabled()) {
            return;
        }

        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
                
                // 发布同步消息
                cacheSyncService.publishCacheClear(cacheName);
                
                log.debug("Cleared cache: {}", cacheName);
            }
        } catch (Exception e) {
            log.error("Failed to clear cache: {}", cacheName, e);
        }
    }

    /**
     * 批量驱逐缓存
     */
    public void evictBatch(String cacheName, List<Object> keys) {
        if (!cacheProperties.getEnabled() || keys == null || keys.isEmpty()) {
            return;
        }

        keys.forEach(key -> evict(cacheName, key));
    }

    /**
     * 获取缓存统计信息
     */
    public CacheStats getCacheStats(String cacheName) {
        return cacheManager.getCacheStats(cacheName);
    }

    /**
     * 检查缓存是否存在
     */
    public boolean exists(String cacheName, Object key) {
        if (!cacheProperties.getEnabled()) {
            return false;
        }

        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                String tenantKey = buildTenantKey(key);
                return cache.get(tenantKey) != null;
            }
        } catch (Exception e) {
            log.error("Failed to check cache existence: cacheName={}, key={}", cacheName, key, e);
        }
        
        return false;
    }

    /**
     * 构建租户相关的缓存键
     */
    private String buildTenantKey(Object key) {
        Long tenantId = TenantContextHolder.getTenantId();
        if (tenantId != null && tenantId > 0) {
            return cacheProperties.getL2().getTenantKeyPrefix() + tenantId + ":" + key;
        }
        return String.valueOf(key);
    }

    // ========== 菜单缓存相关方法 ==========

    /**
     * 获取菜单树缓存
     */
    public <T> T getMenuTree(String key, Class<T> type, Supplier<T> supplier) {
        return get(CACHE_MENU, "tree:" + key, type, supplier);
    }

    /**
     * 缓存菜单树
     */
    public void putMenuTree(String key, Object menuTree) {
        put(CACHE_MENU, "tree:" + key, menuTree);
    }

    /**
     * 清除菜单缓存
     */
    public void evictMenuCache() {
        clear(CACHE_MENU);
    }

    // ========== 页面缓存相关方法 ==========

    /**
     * 获取页面内容缓存
     */
    public <T> T getPageContent(Long pageId, Class<T> type, Supplier<T> supplier) {
        return get(CACHE_PAGE, "content:" + pageId, type, supplier);
    }

    /**
     * 缓存页面内容
     */
    public void putPageContent(Long pageId, Object pageContent) {
        put(CACHE_PAGE, "content:" + pageId, pageContent);
    }

    /**
     * 驱逐页面缓存
     */
    public void evictPageCache(Long pageId) {
        evict(CACHE_PAGE, "content:" + pageId);
        // 同时清除路径映射缓存
        clear(CACHE_PAGE_PATH);
    }

    /**
     * 获取页面路径映射缓存
     */
    public <T> T getPageByPath(String path, Class<T> type, Supplier<T> supplier) {
        return get(CACHE_PAGE_PATH, path, type, supplier);
    }

    /**
     * 缓存页面路径映射
     */
    public void putPageByPath(String path, Object page) {
        put(CACHE_PAGE_PATH, path, page);
    }

    // ========== 统计缓存相关方法 ==========

    /**
     * 获取统计数据缓存
     */
    public <T> T getStats(String key, Class<T> type, Supplier<T> supplier) {
        return get(CACHE_STATS, key, type, supplier);
    }

    /**
     * 缓存统计数据
     */
    public void putStats(String key, Object stats) {
        put(CACHE_STATS, key, stats);
    }

    /**
     * 清除统计缓存
     */
    public void evictStatsCache() {
        clear(CACHE_STATS);
    }
}