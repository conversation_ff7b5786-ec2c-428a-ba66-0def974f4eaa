package cn.iocoder.yudao.module.cms.dal.dataobject.diy;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;


/**
 * CMS DIY页面 DO
 *
 * <AUTHOR>
 */
@TableName("cms_diy_page")
@KeySequence("cms_diy_page_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsDiyPageDO extends BaseDO {

    /**
     * 页面ID
     */
    @TableId
    private Long id;

    /**
     * 页面UUID，全局唯一
     */
    private String uuid;

    /**
     * 页面名称
     */
    private String name;

    /**
     * 上级页面ID
     */
    private Long parentId;

    /**
     * 关联菜单ID
     */
    private Long menuId;

    /**
     * 页面路径，唯一
     */
    private String path;

    /**
     * 关键词
     */
    private String keywords;

    /**
     * 页面描述
     */
    private String description;

    /**
     * 状态：0-草稿，1-已发布，2-已下线
     *
     * 枚举 {@link cn.iocoder.yudao.module.cms.enums.diy.CmsDiyPageStatusEnum}
     */
    private Integer status;

    /**
     * 当前版本号，用于乐观锁
     */
    @Version
    private Integer version;

    /**
     * 已发布的版本号
     */
    private Integer publishedVersion;

    /**
     * 页面内容JSON
     */
    private String content;

}
