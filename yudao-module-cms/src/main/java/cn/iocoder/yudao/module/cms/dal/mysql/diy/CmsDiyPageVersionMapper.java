package cn.iocoder.yudao.module.cms.dal.mysql.diy;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.cms.controller.admin.diy.vo.version.CmsDiyPageVersionPageReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.diy.CmsDiyPageVersionDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * CMS DIY页面版本 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CmsDiyPageVersionMapper extends BaseMapperX<CmsDiyPageVersionDO> {

    default PageResult<CmsDiyPageVersionDO> selectPage(CmsDiyPageVersionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CmsDiyPageVersionDO>()
                .eqIfPresent(CmsDiyPageVersionDO::getPageId, reqVO.getPageId())
                .likeIfPresent(CmsDiyPageVersionDO::getName, reqVO.getName())
                .eqIfPresent(CmsDiyPageVersionDO::getIsPublished, reqVO.getIsPublished())
                .betweenIfPresent(CmsDiyPageVersionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(CmsDiyPageVersionDO::getVersion));
    }

    /**
     * 根据页面ID和版本号查询版本
     */
    default CmsDiyPageVersionDO selectByPageIdAndVersion(Long pageId, Integer version) {
        return selectOne(new LambdaQueryWrapperX<CmsDiyPageVersionDO>()
                .eq(CmsDiyPageVersionDO::getPageId, pageId)
                .eq(CmsDiyPageVersionDO::getVersion, version));
    }

    /**
     * 根据页面ID查询已发布的版本
     */
    default CmsDiyPageVersionDO selectPublishedByPageId(Long pageId) {
        return selectOne(new LambdaQueryWrapperX<CmsDiyPageVersionDO>()
                .eq(CmsDiyPageVersionDO::getPageId, pageId)
                .eq(CmsDiyPageVersionDO::getIsPublished, true)
                .orderByDesc(CmsDiyPageVersionDO::getVersion)
                .last("LIMIT 1"));
    }

    /**
     * 根据页面ID查询最新版本号
     */
    Integer selectMaxVersionByPageId(@Param("pageId") Long pageId);

    /**
     * 根据页面ID查询版本列表（按版本号倒序）
     */
    default List<CmsDiyPageVersionDO> selectListByPageId(Long pageId) {
        return selectList(new LambdaQueryWrapperX<CmsDiyPageVersionDO>()
                .eq(CmsDiyPageVersionDO::getPageId, pageId)
                .orderByDesc(CmsDiyPageVersionDO::getVersion));
    }

    /**
     * 根据页面ID查询版本列表（分页，按版本号倒序）
     */
    default PageResult<CmsDiyPageVersionDO> selectPageByPageId(Long pageId, PageParam pageReq) {
        return selectPage(pageReq, new LambdaQueryWrapperX<CmsDiyPageVersionDO>()
                .eq(CmsDiyPageVersionDO::getPageId, pageId)
                .orderByDesc(CmsDiyPageVersionDO::getVersion));
    }

    /**
     * 根据页面ID删除旧版本（保留最新的N个版本）
     */
    int deleteOldVersions(@Param("pageId") Long pageId, @Param("keepCount") Integer keepCount);

    /**
     * 根据页面ID统计版本数量
     */
    default Long selectCountByPageId(Long pageId) {
        return selectCount(CmsDiyPageVersionDO::getPageId, pageId);
    }

    /**
     * 批量更新版本的发布状态
     */
    int updatePublishStatusByPageId(@Param("pageId") Long pageId, @Param("isPublished") Boolean isPublished);

    /**
     * 根据页面ID查询最近的N个版本
     */
    default List<CmsDiyPageVersionDO> selectRecentVersions(Long pageId, Integer limit) {
        return selectList(new LambdaQueryWrapperX<CmsDiyPageVersionDO>()
                .eq(CmsDiyPageVersionDO::getPageId, pageId)
                .orderByDesc(CmsDiyPageVersionDO::getVersion)
                .last("LIMIT " + limit));
    }

    /**
     * 检查版本是否存在
     */
    default boolean existsByPageIdAndVersion(Long pageId, Integer version) {
        return selectCount(new LambdaQueryWrapperX<CmsDiyPageVersionDO>()
                .eq(CmsDiyPageVersionDO::getPageId, pageId)
                .eq(CmsDiyPageVersionDO::getVersion, version)) > 0;
    }

    /**
     * 根据页面ID查询最新版本
     */
    default CmsDiyPageVersionDO selectLatestByPageId(Long pageId) {
        return selectOne(new LambdaQueryWrapperX<CmsDiyPageVersionDO>()
                .eq(CmsDiyPageVersionDO::getPageId, pageId)
                .orderByDesc(CmsDiyPageVersionDO::getVersion)
                .last("LIMIT 1"));
    }

    /**
     * 根据页面ID删除所有版本
     */
    default int deleteByPageId(Long pageId) {
        return delete(new LambdaQueryWrapperX<CmsDiyPageVersionDO>()
                .eq(CmsDiyPageVersionDO::getPageId, pageId));
    }

    /**
     * 将页面的所有版本设置为未发布
     */
    default int updateAllUnpublishedByPageId(Long pageId) {
        CmsDiyPageVersionDO updateObj = new CmsDiyPageVersionDO();
        updateObj.setIsPublished(false);
        return update(updateObj, new LambdaQueryWrapperX<CmsDiyPageVersionDO>()
                .eq(CmsDiyPageVersionDO::getPageId, pageId));
    }

    /**
     * 查询所有不重复的页面ID
     */
    @Select("SELECT DISTINCT page_id FROM cms_diy_page_version WHERE deleted = 0")
    List<Long> selectDistinctPageIds();

}