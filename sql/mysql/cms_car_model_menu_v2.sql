-- 车型管理菜单 SQL
-- 主菜单 ID: 5013 (CheryCar)
-- 子菜单 ID 从 5100 开始自增

-- =============================================
-- 1. 车型基础管理模块
-- =============================================

-- 1.1 车系管理菜单
INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5100, 'Car Series', 'cms:car-series', 2, 1, 5013, 'series', 'car', 'cms/carmodel/series/index', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

-- 车系管理按钮权限
INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5101, 'Query Series', 'cms:car-series:query', 3, 1, 5100, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5102, 'Create Series', 'cms:car-series:create', 3, 2, 5100, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5103, 'Update Series', 'cms:car-series:update', 3, 3, 5100, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5104, 'Delete Series', 'cms:car-series:delete', 3, 4, 5100, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5105, 'Export Series', 'cms:car-series:export', 3, 5, 5100, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

-- 1.2 车型管理菜单
INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5110, 'Car Models', 'cms:car-model', 2, 2, 5013, 'model', 'list', 'cms/carmodel/model/index', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

-- 车型管理按钮权限
INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5111, 'Query Model', 'cms:car-model:query', 3, 1, 5110, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5112, 'Create Model', 'cms:car-model:create', 3, 2, 5110, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5113, 'Update Model', 'cms:car-model:update', 3, 3, 5110, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5114, 'Delete Model', 'cms:car-model:delete', 3, 4, 5110, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5115, 'Export Model', 'cms:car-model:export', 3, 5, 5110, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

-- 1.3 车型规格管理权限 (作为车型管理的子权限)
INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5116, 'Query Specs', 'cms:car-model-spec:query', 3, 11, 5110, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5117, 'Create Specs', 'cms:car-model-spec:create', 3, 12, 5110, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5118, 'Update Specs', 'cms:car-model-spec:update', 3, 13, 5110, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5119, 'Delete Specs', 'cms:car-model-spec:delete', 3, 14, 5110, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

-- 1.4 配置包管理权限 (作为车型管理的子权限)
INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5120, 'Query Package', 'cms:car-model-package:query', 3, 21, 5110, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5121, 'Create Package', 'cms:car-model-package:create', 3, 22, 5110, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5122, 'Update Package', 'cms:car-model-package:update', 3, 23, 5110, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5123, 'Delete Package', 'cms:car-model-package:delete', 3, 24, 5110, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

-- =============================================
-- 2. 配置选项管理模块
-- =============================================

-- 2.1 选项类型管理菜单
INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5130, 'Option Types', 'cms:car-option-type', 2, 3, 5013, 'option-type', 'setting', 'cms/carmodel/option/type/index', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

-- 选项类型管理按钮权限
INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5131, 'Query Type', 'cms:car-option-type:query', 3, 1, 5130, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5132, 'Create Type', 'cms:car-option-type:create', 3, 2, 5130, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5133, 'Update Type', 'cms:car-option-type:update', 3, 3, 5130, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5134, 'Delete Type', 'cms:car-option-type:delete', 3, 4, 5130, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

-- 2.2 车型选项管理菜单
INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5140, 'Option Instances', 'cms:car-model-option', 2, 4, 5013, 'option-instance', 'list', 'cms/carmodel/option/instance/index', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

-- 车型选项管理按钮权限
INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5141, 'Query Option', 'cms:car-model-option:query', 3, 1, 5140, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5142, 'Create Option', 'cms:car-model-option:create', 3, 2, 5140, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5143, 'Update Option', 'cms:car-model-option:update', 3, 3, 5140, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5144, 'Delete Option', 'cms:car-model-option:delete', 3, 4, 5140, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5145, 'Batch Operation', 'cms:car-model-option:batch', 3, 5, 5140, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

-- =============================================
-- 3. 融资方案管理模块
-- =============================================

-- 3.1 融资方案菜单
INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5150, 'Finance Plans', 'cms:car-finance', 2, 5, 5013, 'finance', 'money', 'cms/carmodel/finance/plan/index', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

-- 融资方案按钮权限
INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5151, 'Query Plan', 'cms:car-finance-plan:query', 3, 1, 5150, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5152, 'Create Plan', 'cms:car-finance-plan:create', 3, 2, 5150, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5153, 'Update Plan', 'cms:car-finance-plan:update', 3, 3, 5150, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5154, 'Delete Plan', 'cms:car-finance-plan:delete', 3, 4, 5150, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5155, 'Calculate Plan', 'cms:car-finance-plan:calculate', 3, 5, 5150, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

-- 3.2 融资模板管理菜单
INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5160, 'Finance Templates', 'cms:finance-template', 2, 6, 5013, 'finance-template', 'template', 'cms/carmodel/finance/template/index', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

-- 融资模板按钮权限
INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5161, 'Query Template', 'cms:finance-option:query', 3, 1, 5160, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5162, 'Create Template', 'cms:finance-option:create', 3, 2, 5160, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5163, 'Update Template', 'cms:finance-option:update', 3, 3, 5160, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5164, 'Delete Template', 'cms:finance-option:delete', 3, 4, 5160, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

-- =============================================
-- 4. API配置管理模块
-- =============================================

-- 4.1 API配置菜单
INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5170, 'API Config', 'cms:api-config', 2, 7, 5013, 'api-config', 'api', 'cms/carmodel/apiconfig/index', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

-- API配置按钮权限
INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5171, 'Query API', 'cms:api-sql-config:query', 3, 1, 5170, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5172, 'Create API', 'cms:api-sql-config:create', 3, 2, 5170, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5173, 'Update API', 'cms:api-sql-config:update', 3, 3, 5170, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5174, 'Delete API', 'cms:api-sql-config:delete', 3, 4, 5170, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

INSERT INTO system_menu(id, name, permission, type, sort, parent_id, path, icon, component, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES (5175, 'Test API', 'cms:api-sql-config:test', 3, 5, 5170, '', '', '', 0, TRUE, TRUE, TRUE, 'admin', NOW(), 'admin', NOW(), FALSE);

-- =============================================
-- 更新主菜单信息
-- =============================================
UPDATE system_menu 
SET 
    icon = 'car',
    always_show = TRUE,
    status = 0,
    visible = TRUE,
    update_time = NOW()
WHERE id = 5013;