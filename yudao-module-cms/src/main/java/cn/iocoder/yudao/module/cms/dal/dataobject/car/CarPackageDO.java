package cn.iocoder.yudao.module.cms.dal.dataobject.car;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.cms.dal.mysql.car.type.CarPackageFeatureListTypeHandler;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 配置包 DO
 *
 * <AUTHOR>
 */
@TableName(value = "cms_car_packages", autoResultMap = true)
@KeySequence("cms_car_packages_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CarPackageDO extends BaseDO {

    /**
     * 配置包ID
     */
    @TableId
    private Long id;

    /**
     * 车型ID
     */
    private Long modelId;

    /**
     * 配置包代码
     */
    private String packageCode;

    /**
     * 配置包名称
     */
    private String name;

    /**
     * 配置包价格
     */
    private BigDecimal price;

    /**
     * 特性标题
     */
    private String featuresTitle;

    /**
     * 配置包特性列表
     */
    @TableField(typeHandler = CarPackageFeatureListTypeHandler.class)
    private List<CarPackageFeatureDO> features;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 状态
     *
     * 枚举 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer status;

}