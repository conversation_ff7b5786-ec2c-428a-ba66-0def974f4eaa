package cn.iocoder.yudao.module.cms.service.car;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelOptionCreateReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelOptionPageReqVO;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelOptionUpdateReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarModelOptionDO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarOptionTypeDO;
import cn.iocoder.yudao.module.cms.dal.mysql.car.CarModelOptionMapper;
import cn.iocoder.yudao.module.cms.service.car.validator.JsonSchemaValidator;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.cms.enums.ErrorCodeConstants.*;

/**
 * 车型配置选项 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CarModelOptionServiceImpl implements CarModelOptionService {

    @Resource
    private CarModelOptionMapper carModelOptionMapper;

    @Resource
    private CarOptionTypeService carOptionTypeService;

    @Resource
    private JsonSchemaValidator jsonSchemaValidator;

    @Override
    public Long createCarModelOption(CarModelOptionCreateReqVO createReqVO) {
        // 验证选项类型存在
        CarOptionTypeDO optionType = validateOptionTypeExists(createReqVO.getOptionTypeId());
        
        // 验证配置数据
        if (createReqVO.getConfigData() != null && !createReqVO.getConfigData().isEmpty()) {
            validateConfigData(optionType, createReqVO.getConfigData());
        }

        // 插入
        CarModelOptionDO carModelOption = BeanUtils.toBean(createReqVO, CarModelOptionDO.class);
        carModelOptionMapper.insert(carModelOption);
        // 返回
        return carModelOption.getId();
    }

    @Override
    public void updateCarModelOption(CarModelOptionUpdateReqVO updateReqVO) {
        // 校验存在
        validateCarModelOptionExists(updateReqVO.getId());
        
        // 验证选项类型存在
        CarOptionTypeDO optionType = validateOptionTypeExists(updateReqVO.getOptionTypeId());
        
        // 验证配置数据
        if (updateReqVO.getConfigData() != null && !updateReqVO.getConfigData().isEmpty()) {
            validateConfigData(optionType, updateReqVO.getConfigData());
        }

        // 更新
        CarModelOptionDO updateObj = BeanUtils.toBean(updateReqVO, CarModelOptionDO.class);
        carModelOptionMapper.updateById(updateObj);
    }

    @Override
    public void deleteCarModelOption(Long id) {
        // 校验存在
        validateCarModelOptionExists(id);
        // 删除
        carModelOptionMapper.deleteById(id);
    }

    @Override
    public CarModelOptionDO getCarModelOption(Long id) {
        return carModelOptionMapper.selectById(id);
    }

    @Override
    public PageResult<CarModelOptionDO> getCarModelOptionPage(CarModelOptionPageReqVO pageReqVO) {
        return carModelOptionMapper.selectPage(pageReqVO.getModelId(), pageReqVO.getOptionTypeId(),
                pageReqVO.getName(), pageReqVO.getStatus(),
                pageReqVO.getPageNo(), pageReqVO.getPageSize());
    }

    @Override
    public List<CarModelOptionDO> getCarModelOptionListByModelId(Long modelId) {
        return carModelOptionMapper.selectListByModelId(modelId);
    }

    @Override
    public List<CarModelOptionDO> getCarModelOptionListByModelIdAndTypeId(Long modelId, Long optionTypeId) {
        return carModelOptionMapper.selectListByModelIdAndTypeId(modelId, optionTypeId);
    }

    @Override
    public List<CarModelOptionDO> getCarModelOptionsWithType(Long modelId) {
        return carModelOptionMapper.selectOptionsWithType(modelId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> batchCreateCarModelOptions(List<CarModelOptionCreateReqVO> createReqVOList) {
        List<Long> resultIds = new ArrayList<>();
        
        for (CarModelOptionCreateReqVO createReqVO : createReqVOList) {
            Long id = createCarModelOption(createReqVO);
            resultIds.add(id);
        }
        
        return resultIds;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateCarModelOptions(List<CarModelOptionUpdateReqVO> updateReqVOList) {
        for (CarModelOptionUpdateReqVO updateReqVO : updateReqVOList) {
            updateCarModelOption(updateReqVO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCarModelOptionsByModelId(Long modelId) {
        List<CarModelOptionDO> options = carModelOptionMapper.selectListByModelId(modelId);
        for (CarModelOptionDO option : options) {
            carModelOptionMapper.deleteById(option.getId());
        }
    }

    @Override
    public boolean validateOptionConfigData(Long optionTypeId, Object configData) {
        CarOptionTypeDO optionType = carOptionTypeService.getCarOptionType(optionTypeId);
        if (optionType == null) {
            throw exception(CAR_OPTION_TYPE_NOT_EXISTS);
        }
        
        if (optionType.getConfigSchema() == null || optionType.getConfigSchema().isEmpty()) {
            return true;
        }
        
        if (!(configData instanceof Map)) {
            throw exception(CAR_MODEL_OPTION_CONFIG_INVALID);
        }
        
        @SuppressWarnings("unchecked")
        Map<String, Object> configMap = (Map<String, Object>) configData;
        
        try {
            return jsonSchemaValidator.validateData(optionType.getConfigSchema(), configMap);
        } catch (Exception e) {
            throw exception(CAR_MODEL_OPTION_CONFIG_INVALID);
        }
    }

    private void validateCarModelOptionExists(Long id) {
        if (carModelOptionMapper.selectById(id) == null) {
            throw exception(CAR_MODEL_OPTION_NOT_EXISTS);
        }
    }

    private CarOptionTypeDO validateOptionTypeExists(Long optionTypeId) {
        CarOptionTypeDO optionType = carOptionTypeService.getCarOptionType(optionTypeId);
        if (optionType == null) {
            throw exception(CAR_OPTION_TYPE_NOT_EXISTS);
        }
        return optionType;
    }

    private void validateConfigData(CarOptionTypeDO optionType, Map<String, Object> configData) {
        if (optionType.getConfigSchema() != null && !optionType.getConfigSchema().isEmpty()) {
            try {
                boolean isValid = jsonSchemaValidator.validateData(optionType.getConfigSchema(), configData);
                if (!isValid) {
                    throw exception(CAR_MODEL_OPTION_CONFIG_INVALID);
                }
            } catch (Exception e) {
                throw exception(CAR_MODEL_OPTION_CONFIG_INVALID);
            }
        }
    }

}