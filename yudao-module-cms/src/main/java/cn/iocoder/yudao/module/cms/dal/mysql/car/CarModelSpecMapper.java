package cn.iocoder.yudao.module.cms.dal.mysql.car;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.cms.controller.admin.car.vo.CarModelSpecPageReqVO;
import cn.iocoder.yudao.module.cms.dal.dataobject.car.CarModelSpecDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 车型规格 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CarModelSpecMapper extends BaseMapperX<CarModelSpecDO> {

    default PageResult<CarModelSpecDO> selectPage(CarModelSpecPageReqVO pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<CarModelSpecDO>()
                .eqIfPresent(CarModelSpecDO::getModelId, pageReqVO.getModelId())
//                .eqIfPresent(CarModelSpecDO::getCategory, pageReqVO.getCategory())
//                .likeIfPresent(CarModelSpecDO::getName, pageReqVO.getName())
//                .eqIfPresent(CarModelSpecDO::getSpecGroup, pageReqVO.getSpecGroup())
//                .eqIfPresent(CarModelSpecDO::getStatus, pageReqVO.getStatus())
//                .orderByAsc(CarModelSpecDO::getSort)
                .orderByDesc(CarModelSpecDO::getId));
    }

    default List<CarModelSpecDO> selectListByModelIdAndCategoryAndStatus(Long modelId, String category, Integer status) {
        return selectList(new LambdaQueryWrapperX<CarModelSpecDO>()
                .eqIfPresent(CarModelSpecDO::getModelId, modelId)
//                .eqIfPresent(CarModelSpecDO::getCategory, category)
//                .eqIfPresent(CarModelSpecDO::getStatus, status)
//                .orderByAsc(CarModelSpecDO::getSort)
        );
    }

    default List<CarModelSpecDO> selectListByModelIdAndSpecGroup(Long modelId, String specGroup) {
        return selectList(new LambdaQueryWrapperX<CarModelSpecDO>()
                .eq(CarModelSpecDO::getModelId, modelId)
//                .eqIfPresent(CarModelSpecDO::getSpecGroup, specGroup)
//                .eq(CarModelSpecDO::getStatus, 1)
//                .orderByAsc(CarModelSpecDO::getSort)
                );
    }

}